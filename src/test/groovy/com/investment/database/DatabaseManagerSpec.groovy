package com.investment.database

import com.investment.model.Instrument
import com.investment.model.InstrumentType
import spock.lang.Specification
import spock.lang.Subject

import java.math.BigDecimal
import java.sql.Connection
import java.sql.DriverManager
import java.sql.ResultSet
import java.sql.SQLException
import java.sql.Statement

/**
 * Spock specification for testing the DatabaseManager schema migration.
 */
class DatabaseManagerSpec extends Specification {

    @Subject
    DatabaseManager databaseManager

    String testDbUrl = "*****************************************"

    def setup() {
        // Use file-based database for testing to avoid connection isolation issues
        DatabaseManager.setDbUrl(testDbUrl)
        databaseManager = new DatabaseManager()

        // Clean up any existing test database
        new File("./data/test_marketdata.duckdb").delete()
    }

    def cleanup() {
        if (databaseManager != null) {
            databaseManager.closeConnection()
        }
        // Clean up test database file
        new File("./data/test_marketdata.duckdb").delete()
    }

    def "should create new schema with all required columns"() {
        when: "initializing the database"
        databaseManager.initDatabase()

        then: "the instruments table should have the new schema"
        def columns = getTableColumns("instruments")
        columns.contains("symbol")
        columns.contains("name")
        columns.contains("instrument_type")
        columns.contains("market_cap")
        columns.contains("country")
        columns.contains("ipo_year")
        columns.contains("sector")
        columns.contains("industry")
        columns.contains("created_at")
        columns.contains("updated_at")
    }

    def "should save instrument with basic information"() {
        given: "an initialized database"
        databaseManager.initDatabase()

        when: "saving an instrument with basic info"
        databaseManager.saveInstrument("AAPL", "Apple Inc.", "US_STOCK")

        then: "the instrument should be saved successfully"
        def instrument = getInstrumentFromDb("AAPL")
        instrument != null
        instrument.symbol == "AAPL"
        instrument.name == "Apple Inc."
        instrument.instrument_type == "US_STOCK"
        instrument.market_cap == null
        instrument.country == null
        instrument.ipo_year == null
        instrument.sector == null
        instrument.industry == null
    }

    def "should save instrument with detailed information"() {
        given: "an initialized database"
        databaseManager.initDatabase()

        when: "saving an instrument with detailed info"
        databaseManager.saveInstrumentWithDetails(
            "AAPL",
            "Apple Inc.",
            "US_STOCK",
            new BigDecimal("3000000000000"), // 3 trillion market cap
            "United States",
            1980,
            "Technology",
            "Consumer Electronics"
        )

        then: "the instrument should be saved with all details"
        def instrument = getInstrumentFromDb("AAPL")
        instrument != null
        instrument.symbol == "AAPL"
        instrument.name == "Apple Inc."
        instrument.instrument_type == "US_STOCK"
        instrument.market_cap == new BigDecimal("3000000000000.00")
        instrument.country == "United States"
        instrument.ipo_year == 1980
        instrument.sector == "Technology"
        instrument.industry == "Consumer Electronics"
    }

    def "should update existing instrument with new details"() {
        given: "an initialized database with an existing instrument"
        databaseManager.initDatabase()
        databaseManager.saveInstrument("AAPL", "Apple Inc.", "US_STOCK")

        when: "updating the instrument with detailed information"
        databaseManager.saveInstrumentWithDetails(
            "AAPL",
            "Apple Inc.",
            "US_STOCK",
            new BigDecimal("3000000000000"),
            "United States",
            1980,
            "Technology",
            "Consumer Electronics"
        )

        then: "the instrument should be updated with new details"
        def instrument = getInstrumentFromDb("AAPL")
        instrument != null
        instrument.market_cap == new BigDecimal("3000000000000.00")
        instrument.country == "United States"
        instrument.sector == "Technology"
    }

    def "should handle null values in detailed fields"() {
        given: "an initialized database"
        databaseManager.initDatabase()

        when: "saving an instrument with some null detailed fields"
        databaseManager.saveInstrumentWithDetails(
            "TSLA",
            "Tesla Inc.",
            "US_STOCK",
            null, // market cap unknown
            "United States",
            null, // IPO year unknown
            "Automotive",
            null  // industry unknown
        )

        then: "the instrument should be saved with null values handled correctly"
        def instrument = getInstrumentFromDb("TSLA")
        instrument != null
        instrument.symbol == "TSLA"
        instrument.market_cap == null
        instrument.country == "United States"
        instrument.ipo_year == null
        instrument.sector == "Automotive"
        instrument.industry == null
    }

    def "should retrieve all instruments with details"() {
        given: "instruments in the database"
        databaseManager.initDatabase()
        databaseManager.saveInstrument("AAPL", "Apple Inc.", "US_STOCK")
        databaseManager.saveInstrument("GOOGL", "Alphabet Inc.", "US_STOCK")

        when: "retrieving all instruments"
        def instruments = databaseManager.getAllInstruments()

        then: "should return all instruments with details"
        instruments.size() == 2

        def apple = instruments.find { it.symbol == "AAPL" }
        apple != null
        apple.name == "Apple Inc."
        apple.instrumentType.name() == "US_STOCK"

        def google = instruments.find { it.symbol == "GOOGL" }
        google != null
        google.name == "Alphabet Inc."
        google.instrumentType.name() == "US_STOCK"
    }

    def "should handle empty instruments table when getting all instruments"() {
        given: "an initialized database"
        databaseManager.initDatabase()

        when: "retrieving all instruments from empty table"
        def instruments = databaseManager.getAllInstruments()

        then: "should return empty list"
        instruments.isEmpty()
    }

    def "should handle unknown instrument type gracefully"() {
        given: "an initialized database and an instrument with unknown type"
        databaseManager.initDatabase()

        // Manually insert with unknown type to test error handling
        def sql = "INSERT INTO instruments (symbol, name, instrument_type) VALUES (?, ?, ?)"
        def connection = databaseManager.getConnection()
        def pstmt = connection.prepareStatement(sql)
        pstmt.setString(1, "TEST")
        pstmt.setString(2, "Test Company")
        pstmt.setString(3, "UNKNOWN_TYPE")
        pstmt.executeUpdate()
        pstmt.close()

        when: "retrieving all instruments"
        def instruments = databaseManager.getAllInstruments()

        then: "should default to US_STOCK for unknown types"
        instruments.size() == 1
        def testInstrument = instruments[0]
        testInstrument.symbol == "TEST"
        testInstrument.name == "Test Company"
        testInstrument.instrumentType.name() == "US_STOCK"
    }

    def "should retrieve instruments ordered by symbol"() {
        given: "instruments inserted in random order"
        databaseManager.initDatabase()
        databaseManager.saveInstrument("ZZTEST", "Z Company", "US_STOCK")
        databaseManager.saveInstrument("AATEST", "A Company", "US_STOCK")
        databaseManager.saveInstrument("MMTEST", "M Company", "US_STOCK")

        when: "retrieving all instruments"
        def instruments = databaseManager.getAllInstruments()

        then: "should be ordered by symbol"
        instruments.size() == 3
        instruments[0].symbol == "AATEST"
        instruments[1].symbol == "MMTEST"
        instruments[2].symbol == "ZZTEST"
    }

    private List<String> getTableColumns(String tableName) {
        List<String> columns = []
        Connection conn = DriverManager.getConnection(testDbUrl, "sa", "")
        try {
            Statement stmt = conn.createStatement()
            // Use DuckDB's PRAGMA table_info equivalent
            ResultSet rs = stmt.executeQuery("SELECT column_name FROM information_schema.columns WHERE table_name = '${tableName}'")
            while (rs.next()) {
                columns.add(rs.getString("column_name"))
            }
        } catch (SQLException e) {
            // Fallback: try to query the table structure differently
            try {
                Statement stmt = conn.createStatement()
                ResultSet rs = stmt.executeQuery("SELECT * FROM ${tableName} LIMIT 0")
                def metaData = rs.getMetaData()
                for (int i = 1; i <= metaData.getColumnCount(); i++) {
                    columns.add(metaData.getColumnName(i))
                }
            } catch (SQLException e2) {
                // Table doesn't exist
            }
        } finally {
            conn.close()
        }
        return columns
    }

    private Map getInstrumentFromDb(String symbol) {
        Connection conn = DriverManager.getConnection(testDbUrl, "sa", "")
        try {
            Statement stmt = conn.createStatement()
            ResultSet rs = stmt.executeQuery("SELECT * FROM instruments WHERE symbol = '${symbol}'")
            if (rs.next()) {
                return [
                    symbol: rs.getString("symbol"),
                    name: rs.getString("name"),
                    instrument_type: rs.getString("instrument_type"),
                    market_cap: rs.getBigDecimal("market_cap"),
                    country: rs.getString("country"),
                    ipo_year: rs.getObject("ipo_year", Integer.class),
                    sector: rs.getString("sector"),
                    industry: rs.getString("industry")
                ]
            }
        } finally {
            conn.close()
        }
        return null
    }
}
