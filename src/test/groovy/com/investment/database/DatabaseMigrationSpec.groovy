package com.investment.database

import spock.lang.Specification

import java.sql.Connection
import java.sql.DriverManager
import java.sql.Statement

/**
 * Spock specification for testing database migration from old to new schema.
 */
class DatabaseMigrationSpec extends Specification {

    String testDbUrl = "****************************************"

    def setup() {
        // Clean up any existing test database
        new File("./data/migration_test.duckdb").delete()
    }

    def cleanup() {
        // Clean up test database file
        new File("./data/migration_test.duckdb").delete()
    }

    def "should migrate from old schema to new schema"() {
        given: "a database with old schema"
        createOldSchema()
        insertOldSchemaData()

        when: "initializing DatabaseManager (which triggers migration)"
        DatabaseManager.setDbUrl(testDbUrl)
        DatabaseManager dbManager = new DatabaseManager()
        dbManager.initDatabase()

        then: "the schema should be migrated to new format"
        def columns = getTableColumns("instruments")
        columns.contains("symbol")
        columns.contains("name")
        columns.contains("instrument_type") // renamed from 'type'
        columns.contains("market_cap")
        columns.contains("country")
        columns.contains("ipo_year")
        columns.contains("sector")
        columns.contains("industry")
        columns.contains("created_at")
        columns.contains("updated_at")

        and: "existing data should be preserved"
        def instrument = getInstrumentFromDb("AAPL")
        instrument != null
        instrument.symbol == "AAPL"
        instrument.name == "Apple Inc."
        instrument.instrument_type == "US_STOCK"

        cleanup:
        dbManager?.closeConnection()
    }

    private void createOldSchema() {
        Connection conn = DriverManager.getConnection(testDbUrl, "sa", "")
        try {
            Statement stmt = conn.createStatement()
            
            // Create schema version table
            stmt.execute(
                "CREATE TABLE schema_version (" +
                "version INTEGER PRIMARY KEY, " +
                "applied_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP" +
                ")"
            )
            
            // Create old instruments table schema
            stmt.execute(
                "CREATE TABLE instruments (" +
                "symbol VARCHAR(20) PRIMARY KEY, " +
                "name VARCHAR(100) NOT NULL, " +
                "type VARCHAR(20) NOT NULL, " +
                "created_at TIMESTAMP, " +
                "updated_at TIMESTAMP" +
                ")"
            )
            
            // Insert version 1 to indicate old schema
            stmt.execute("INSERT INTO schema_version (version) VALUES (1)")
        } finally {
            conn.close()
        }
    }

    private void insertOldSchemaData() {
        Connection conn = DriverManager.getConnection(testDbUrl, "sa", "")
        try {
            Statement stmt = conn.createStatement()
            stmt.execute(
                "INSERT INTO instruments (symbol, name, type, created_at, updated_at) " +
                "VALUES ('AAPL', 'Apple Inc.', 'US_STOCK', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)"
            )
        } finally {
            conn.close()
        }
    }

    private List<String> getTableColumns(String tableName) {
        List<String> columns = []
        Connection conn = DriverManager.getConnection(testDbUrl, "sa", "")
        try {
            Statement stmt = conn.createStatement()
            def rs = stmt.executeQuery("SELECT * FROM ${tableName} LIMIT 0")
            def metaData = rs.getMetaData()
            for (int i = 1; i <= metaData.getColumnCount(); i++) {
                columns.add(metaData.getColumnName(i))
            }
        } finally {
            conn.close()
        }
        return columns
    }

    private Map getInstrumentFromDb(String symbol) {
        Connection conn = DriverManager.getConnection(testDbUrl, "sa", "")
        try {
            Statement stmt = conn.createStatement()
            def rs = stmt.executeQuery("SELECT * FROM instruments WHERE symbol = '${symbol}'")
            if (rs.next()) {
                return [
                    symbol: rs.getString("symbol"),
                    name: rs.getString("name"),
                    instrument_type: rs.getString("instrument_type")
                ]
            }
        } finally {
            conn.close()
        }
        return null
    }
}
