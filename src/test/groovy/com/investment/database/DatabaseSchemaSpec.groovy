package com.investment.database

import spock.lang.Specification

import java.math.BigDecimal
import java.sql.Connection
import java.sql.DriverManager
import java.sql.Statement

/**
 * Simple test to verify the new database schema works correctly.
 */
class DatabaseSchemaSpec extends Specification {

    String testDbUrl = "*************************************"
    DatabaseManager databaseManager

    def setup() {
        // Clean up any existing test database
        new File("./data/schema_test.duckdb").delete()
        
        DatabaseManager.setDbUrl(testDbUrl)
        databaseManager = new DatabaseManager()
    }

    def cleanup() {
        databaseManager?.closeConnection()
        // Clean up test database file
        new File("./data/schema_test.duckdb").delete()
    }

    def "should create new schema and save instruments with extended data"() {
        when: "initializing the database"
        databaseManager.initDatabase()

        and: "saving an instrument with extended data"
        databaseManager.saveInstrumentWithDetails(
            "AAPL", 
            "Apple Inc.", 
            "US_STOCK",
            new BigDecimal("3000000000000"), // 3 trillion market cap
            "United States",
            1980,
            "Technology",
            "Consumer Electronics"
        )

        then: "the instrument should be saved with all details"
        def instrument = getInstrumentFromDb("AAPL")
        instrument != null
        instrument.symbol == "AAPL"
        instrument.name == "Apple Inc."
        instrument.instrument_type == "US_STOCK"
        instrument.market_cap == new BigDecimal("3000000000000.00")
        instrument.country == "United States"
        instrument.ipo_year == 1980
        instrument.sector == "Technology"
        instrument.industry == "Consumer Electronics"
    }

    def "should support backward compatibility with old saveInstrument method"() {
        when: "initializing the database"
        databaseManager.initDatabase()

        and: "saving an instrument with old method"
        databaseManager.saveInstrument("MSFT", "Microsoft Corporation", "US_STOCK")

        then: "the instrument should be saved with basic info"
        def instrument = getInstrumentFromDb("MSFT")
        instrument != null
        instrument.symbol == "MSFT"
        instrument.name == "Microsoft Corporation"
        instrument.instrument_type == "US_STOCK"
        instrument.market_cap == null
        instrument.country == null
        instrument.ipo_year == null
        instrument.sector == null
        instrument.industry == null
    }

    private Map getInstrumentFromDb(String symbol) {
        Connection conn = DriverManager.getConnection(testDbUrl, "sa", "")
        try {
            Statement stmt = conn.createStatement()
            def rs = stmt.executeQuery("SELECT * FROM instruments WHERE symbol = '${symbol}'")
            if (rs.next()) {
                return [
                    symbol: rs.getString("symbol"),
                    name: rs.getString("name"),
                    instrument_type: rs.getString("instrument_type"),
                    market_cap: rs.getBigDecimal("market_cap"),
                    country: rs.getString("country"),
                    ipo_year: rs.getObject("ipo_year", Integer.class),
                    sector: rs.getString("sector"),
                    industry: rs.getString("industry")
                ]
            }
        } finally {
            conn.close()
        }
        return null
    }
}
