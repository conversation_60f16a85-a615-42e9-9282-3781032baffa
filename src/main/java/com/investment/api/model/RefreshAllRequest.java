package com.investment.api.model;

/**
 * Request model for refreshing OHLCV data for all instruments.
 */
public class RefreshAllRequest {
    private boolean dryRun = true;
    private int maxSymbols = 100;
    private boolean skipExisting = false;
    
    public RefreshAllRequest() {}
    
    public RefreshAllRequest(boolean dryRun, int maxSymbols, boolean skipExisting) {
        this.dryRun = dryRun;
        this.maxSymbols = maxSymbols;
        this.skipExisting = skipExisting;
    }
    
    /**
     * Whether to perform a dry run (no actual data updates).
     * @return true if this is a dry run, false for actual refresh
     */
    public boolean isDryRun() {
        return dryRun;
    }
    
    public void setDryRun(boolean dryRun) {
        this.dryRun = dryRun;
    }
    
    /**
     * Maximum number of symbols to process in a single operation.
     * @return maximum number of symbols to process
     */
    public int getMaxSymbols() {
        return maxSymbols;
    }
    
    public void setMaxSymbols(int maxSymbols) {
        this.maxSymbols = maxSymbols;
    }
    
    /**
     * Whether to skip symbols that already have recent data.
     * @return true to skip symbols with recent data, false to refresh all
     */
    public boolean isSkipExisting() {
        return skipExisting;
    }
    
    public void setSkipExisting(boolean skipExisting) {
        this.skipExisting = skipExisting;
    }
    
    @Override
    public String toString() {
        return "RefreshAllRequest{" +
                "dryRun=" + dryRun +
                ", maxSymbols=" + maxSymbols +
                ", skipExisting=" + skipExisting +
                '}';
    }
}
