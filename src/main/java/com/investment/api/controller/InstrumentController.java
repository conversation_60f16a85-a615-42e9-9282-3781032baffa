package com.investment.api.controller;

import com.investment.api.model.ApiResponse;
import com.investment.api.model.CsvUploadResponse;
import com.investment.api.model.SyncRequest;
import com.investment.api.model.SyncResponse;
import com.investment.api.model.ValidationRequest;
import com.investment.api.model.ValidationResponse;
import com.investment.service.CsvInstrumentService;
import com.investment.service.SecSynchronizationService;
import com.investment.service.SymbolValidationService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.util.Map;

/**
 * REST controller for instrument management operations.
 */
@RestController
@RequestMapping("/api/instruments")
@Tag(name = "Instruments", description = "Instrument management operations")
public class InstrumentController {
    private static final Logger logger = LoggerFactory.getLogger(InstrumentController.class);

    private final SymbolValidationService symbolValidationService;
    private final SecSynchronizationService secSynchronizationService;
    private final CsvInstrumentService csvInstrumentService;

    public InstrumentController(SymbolValidationService symbolValidationService,
                               SecSynchronizationService secSynchronizationService,
                               CsvInstrumentService csvInstrumentService) {
        this.symbolValidationService = symbolValidationService;
        this.secSynchronizationService = secSynchronizationService;
        this.csvInstrumentService = csvInstrumentService;
    }

    /**
     * Validate symbols against SEC data with dry-run mode.
     * This endpoint performs validation without making any changes to the database.
     *
     * @param forceRefresh Whether to force refresh of SEC data from remote source
     * @return Validation results showing what would be cleaned up
     */
    @GetMapping("/validate-symbols")
    @Operation(summary = "Validate symbols against SEC data (dry-run)",
               description = "Validates all symbols in the database against official SEC ticker data. " +
                           "This is a safe operation that only reports what would be cleaned up without making changes.")
    @ApiResponses(value = {
        @io.swagger.v3.oas.annotations.responses.ApiResponse(
            responseCode = "200",
            description = "Validation completed successfully",
            content = @Content(schema = @Schema(implementation = ValidationResponse.class))
        ),
        @io.swagger.v3.oas.annotations.responses.ApiResponse(
            responseCode = "500",
            description = "Internal server error"
        )
    })
    public ResponseEntity<ApiResponse<ValidationResponse>> validateSymbolsDryRun(
            @Parameter(description = "Force refresh of SEC data from remote source")
            @RequestParam(defaultValue = "false") boolean forceRefresh) {

        try {
            logger.info("Starting symbol validation (dry-run) - forceRefresh: {}", forceRefresh);

            ValidationResponse response = symbolValidationService.validateSymbols(true, forceRefresh);

            logger.info("Symbol validation (dry-run) completed: {}", response.getSummary());
            return ResponseEntity.ok(ApiResponse.success("Symbol validation completed", response));

        } catch (Exception e) {
            logger.error("Error during symbol validation (dry-run)", e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(ApiResponse.error("Symbol validation failed: " + e.getMessage()));
        }
    }

    /**
     * Validate and clean up symbols against SEC data.
     * This endpoint performs actual cleanup operations and removes invalid symbols.
     *
     * @param request Validation request with options
     * @return Validation results with cleanup summary
     */
    @PostMapping("/validate-symbols")
    @Operation(summary = "Validate and clean up symbols against SEC data",
               description = "Validates all symbols in the database against official SEC ticker data. " +
                           "Can perform actual cleanup (dryRun=false) or just report (dryRun=true). " +
                           "CAUTION: When dryRun=false, this will permanently delete invalid symbols and their data.")
    @ApiResponses(value = {
        @io.swagger.v3.oas.annotations.responses.ApiResponse(
            responseCode = "200",
            description = "Validation/cleanup completed successfully",
            content = @Content(schema = @Schema(implementation = ValidationResponse.class))
        ),
        @io.swagger.v3.oas.annotations.responses.ApiResponse(
            responseCode = "400",
            description = "Invalid request"
        ),
        @io.swagger.v3.oas.annotations.responses.ApiResponse(
            responseCode = "500",
            description = "Internal server error"
        )
    })
    public ResponseEntity<ApiResponse<ValidationResponse>> validateAndCleanupSymbols(
            @Parameter(description = "Validation request with dryRun and forceRefresh options", required = true)
            @RequestBody ValidationRequest request) {

        try {
            if (request == null) {
                return ResponseEntity.status(HttpStatus.BAD_REQUEST)
                        .body(ApiResponse.error("Request body is required"));
            }

            logger.info("Starting symbol validation - dryRun: {}, forceRefresh: {}",
                       request.isDryRun(), request.isForceRefresh());

            // Log warning for actual cleanup operations
            if (!request.isDryRun()) {
                logger.warn("PERFORMING ACTUAL CLEANUP - This will permanently delete invalid symbols and their data");
            }

            ValidationResponse response = symbolValidationService.validateSymbols(
                    request.isDryRun(),
                    request.isForceRefresh()
            );

            String operation = request.isDryRun() ? "validation (dry-run)" : "cleanup";
            logger.info("Symbol {} completed: {}", operation, response.getSummary());

            return ResponseEntity.ok(ApiResponse.success(
                    "Symbol " + operation + " completed",
                    response
            ));

        } catch (Exception e) {
            logger.error("Error during symbol validation/cleanup", e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(ApiResponse.error("Symbol validation/cleanup failed: " + e.getMessage()));
        }
    }

    /**
     * Get SEC data cache status for monitoring and debugging.
     *
     * @return Cache status information
     */
    @GetMapping("/sec-cache-status")
    @Operation(summary = "Get SEC data cache status",
               description = "Returns information about the SEC data cache including last update time and cache size")
    @ApiResponses(value = {
        @io.swagger.v3.oas.annotations.responses.ApiResponse(
            responseCode = "200",
            description = "Cache status retrieved successfully"
        ),
        @io.swagger.v3.oas.annotations.responses.ApiResponse(
            responseCode = "500",
            description = "Internal server error"
        )
    })
    public ResponseEntity<ApiResponse<Map<String, Object>>> getSecCacheStatus() {
        try {
            Map<String, Object> cacheStatus = symbolValidationService.getCacheStatus();
            return ResponseEntity.ok(ApiResponse.success("Cache status retrieved", cacheStatus));

        } catch (Exception e) {
            logger.error("Error retrieving SEC cache status", e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(ApiResponse.error("Failed to retrieve cache status: " + e.getMessage()));
        }
    }

    /**
     * Synchronize SEC data with instruments database (dry-run mode).
     * This endpoint identifies missing instruments without making any changes to the database.
     *
     * @param forceRefresh Whether to force refresh of SEC data from remote source
     * @param maxInstruments Maximum number of instruments to process in one operation
     * @return Synchronization results showing what would be added
     */
    @GetMapping("/sync-sec-data")
    @Operation(summary = "Synchronize SEC data with database (dry-run)",
               description = "Identifies instruments from SEC data that are missing from our database. " +
                           "This is a safe operation that only reports what would be added without making changes.")
    @ApiResponses(value = {
        @io.swagger.v3.oas.annotations.responses.ApiResponse(
            responseCode = "200",
            description = "Synchronization analysis completed successfully",
            content = @Content(schema = @Schema(implementation = SyncResponse.class))
        ),
        @io.swagger.v3.oas.annotations.responses.ApiResponse(
            responseCode = "500",
            description = "Internal server error"
        )
    })
    public ResponseEntity<ApiResponse<SyncResponse>> syncSecDataDryRun(
            @Parameter(description = "Force refresh of SEC data from remote source")
            @RequestParam(defaultValue = "false") boolean forceRefresh,
            @Parameter(description = "Maximum number of instruments to process")
            @RequestParam(defaultValue = "1000") int maxInstruments) {

        try {
            logger.info("Starting SEC synchronization (dry-run) - forceRefresh: {}, maxInstruments: {}",
                       forceRefresh, maxInstruments);

            SyncResponse response = secSynchronizationService.synchronizeInstruments(
                    true, forceRefresh, maxInstruments);

            logger.info("SEC synchronization (dry-run) completed: {}", response.getSummary());
            return ResponseEntity.ok(ApiResponse.success("SEC synchronization analysis completed", response));

        } catch (Exception e) {
            logger.error("Error during SEC synchronization (dry-run)", e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(ApiResponse.error("SEC synchronization analysis failed: " + e.getMessage()));
        }
    }

    /**
     * Synchronize SEC data with instruments database.
     * This endpoint can perform actual synchronization operations and add missing instruments.
     *
     * @param request Synchronization request with options
     * @return Synchronization results with summary
     */
    @PostMapping("/sync-sec-data")
    @Operation(summary = "Synchronize SEC data with database",
               description = "Synchronizes SEC company ticker data with our instruments database. " +
                           "Can perform actual synchronization (dryRun=false) or just report (dryRun=true). " +
                           "CAUTION: When dryRun=false, this will add new instruments to the database.")
    @ApiResponses(value = {
        @io.swagger.v3.oas.annotations.responses.ApiResponse(
            responseCode = "200",
            description = "Synchronization completed successfully",
            content = @Content(schema = @Schema(implementation = SyncResponse.class))
        ),
        @io.swagger.v3.oas.annotations.responses.ApiResponse(
            responseCode = "400",
            description = "Invalid request"
        ),
        @io.swagger.v3.oas.annotations.responses.ApiResponse(
            responseCode = "500",
            description = "Internal server error"
        )
    })
    public ResponseEntity<ApiResponse<SyncResponse>> syncSecData(
            @Parameter(description = "Synchronization request with dryRun, forceRefresh, and maxInstruments options", required = true)
            @RequestBody SyncRequest request) {

        try {
            if (request == null) {
                return ResponseEntity.status(HttpStatus.BAD_REQUEST)
                        .body(ApiResponse.error("Request body is required"));
            }

            logger.info("Starting SEC synchronization - dryRun: {}, forceRefresh: {}, maxInstruments: {}",
                       request.isDryRun(), request.isForceRefresh(), request.getMaxInstruments());

            // Log warning for actual synchronization operations
            if (!request.isDryRun()) {
                logger.warn("PERFORMING ACTUAL SYNCHRONIZATION - This will add new instruments to the database");
            }

            SyncResponse response = secSynchronizationService.synchronizeInstruments(
                    request.isDryRun(),
                    request.isForceRefresh(),
                    request.getMaxInstruments()
            );

            String operation = request.isDryRun() ? "synchronization (dry-run)" : "synchronization";
            logger.info("SEC {} completed: {}", operation, response.getSummary());

            return ResponseEntity.ok(ApiResponse.success(
                    "SEC " + operation + " completed",
                    response
            ));

        } catch (Exception e) {
            logger.error("Error during SEC synchronization", e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(ApiResponse.error("SEC synchronization failed: " + e.getMessage()));
        }
    }

    /**
     * Upload and process a CSV file containing instrument data.
     * This endpoint allows uploading a CSV file with financial instrument data and importing it into the database.
     *
     * @param file The CSV file to upload
     * @param dryRun Whether to perform a dry run (no actual insertions)
     * @param maxInstruments Maximum number of instruments to process
     * @param skipDuplicates Whether to skip instruments that already exist
     * @param validateData Whether to perform data validation
     * @return CSV processing results with summary
     */
    @PostMapping(value = "/upload-csv", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    @Operation(summary = "Upload and process CSV file with instrument data",
               description = "Uploads a CSV file containing financial instrument data and imports it into the database. " +
                           "The CSV file must have headers: Symbol, Name, Last Sale, Net Change, % Change, Market Cap, Country, IPO Year, Volume, Sector, Industry. " +
                           "Can perform actual import (dryRun=false) or just validate and report (dryRun=true). " +
                           "CAUTION: When dryRun=false, this will add/update instruments in the database.")
    @ApiResponses(value = {
        @io.swagger.v3.oas.annotations.responses.ApiResponse(
            responseCode = "200",
            description = "CSV processing completed successfully",
            content = @Content(schema = @Schema(implementation = CsvUploadResponse.class))
        ),
        @io.swagger.v3.oas.annotations.responses.ApiResponse(
            responseCode = "400",
            description = "Invalid file or request parameters"
        ),
        @io.swagger.v3.oas.annotations.responses.ApiResponse(
            responseCode = "500",
            description = "Internal server error"
        )
    })
    public ResponseEntity<ApiResponse<CsvUploadResponse>> uploadCsv(
            @Parameter(description = "CSV file containing instrument data", required = true)
            @RequestParam("file") MultipartFile file,
            @Parameter(description = "Perform dry run without actual database changes")
            @RequestParam(defaultValue = "true") boolean dryRun,
            @Parameter(description = "Maximum number of instruments to process")
            @RequestParam(defaultValue = "1000") int maxInstruments,
            @Parameter(description = "Skip instruments that already exist in database")
            @RequestParam(defaultValue = "true") boolean skipDuplicates,
            @Parameter(description = "Perform data validation on CSV content")
            @RequestParam(defaultValue = "true") boolean validateData) {

        try {
            if (file == null || file.isEmpty()) {
                return ResponseEntity.status(HttpStatus.BAD_REQUEST)
                        .body(ApiResponse.error("File is required and cannot be empty"));
            }

            logger.info("Starting CSV upload processing - file: {}, size: {} bytes, dryRun: {}, maxInstruments: {}, skipDuplicates: {}, validateData: {}",
                       file.getOriginalFilename(), file.getSize(), dryRun, maxInstruments, skipDuplicates, validateData);

            // Log warning for actual import operations
            if (!dryRun) {
                logger.warn("PERFORMING ACTUAL CSV IMPORT - This will add/update instruments in the database");
            }

            CsvUploadResponse response = csvInstrumentService.processCsvFile(
                    file, dryRun, maxInstruments, skipDuplicates, validateData);

            String operation = dryRun ? "CSV validation" : "CSV import";
            logger.info("{} completed: {}", operation, response.getSummary());

            return ResponseEntity.ok(ApiResponse.success(
                    operation + " completed",
                    response
            ));

        } catch (Exception e) {
            logger.error("Error during CSV processing", e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(ApiResponse.error("CSV processing failed: " + e.getMessage()));
        }
    }
}
