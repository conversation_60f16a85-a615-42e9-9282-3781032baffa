package com.investment.config;

import com.investment.database.DatabaseManager;
import com.investment.provider.DataProvider;
import com.investment.provider.YahooFinanceProvider;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * Application configuration class.
 */
@Configuration
public class AppConfig {
    
    @Bean
    public DatabaseManager databaseManager() {
        DatabaseManager dbManager = new DatabaseManager();
        dbManager.initDatabase();
        return dbManager;
    }
    
    @Bean
    public DataProvider dataProvider() {
        return new YahooFinanceProvider();
    }
}
