package com.investment;

import io.swagger.v3.oas.annotations.OpenAPIDefinition;
import io.swagger.v3.oas.annotations.info.Info;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;

/**
 * Main Spring Boot application class for the Investment Toolkit.
 * This class serves as the entry point for the Spring Boot application.
 */
@SpringBootApplication
@OpenAPIDefinition(
    info = @Info(
        title = "Investment Toolkit API",
        version = "1.0",
        description = "REST API for accessing and managing investment data"
    )
)
public class InvestmentApplication {
    
    public static void main(String[] args) {
        SpringApplication.run(InvestmentApplication.class, args);
    }
}
