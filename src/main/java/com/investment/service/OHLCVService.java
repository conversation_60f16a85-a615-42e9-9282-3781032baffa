package com.investment.service;

import com.investment.database.DatabaseManager;
import com.investment.model.Instrument;
import com.investment.model.InstrumentType;
import com.investment.model.OHLCV;
import com.investment.provider.DataProvider;
import com.investment.provider.YahooFinanceProvider;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import java.time.LocalDate;
import java.util.List;

/**
 * Service for handling OHLCV data operations.
 */
@Service
public class OHLCVService {
    private static final Logger logger = LoggerFactory.getLogger(OHLCVService.class);

    private final DatabaseManager dbManager;
    private final DataProvider dataProvider;

    public OHLCVService(DatabaseManager dbManager, DataProvider dataProvider) {
        this.dbManager = dbManager;
        this.dataProvider = dataProvider;
    }

    /**
     * Get OHLCV data for a specific symbol within a date range.
     *
     * @param symbol The stock symbol
     * @param startDate The start date (inclusive)
     * @param endDate The end date (inclusive)
     * @return List of OHLCV data points
     */
    public List<OHLCV> getOHLCVData(String symbol, LocalDate startDate, LocalDate endDate) {
        try {
            return dbManager.getOHLCVData(symbol, startDate, endDate);
        } catch (Exception e) {
            logger.error("Error retrieving OHLCV data for symbol: {}", symbol, e);
            throw new RuntimeException("Failed to retrieve OHLCV data", e);
        }
    }

    /**
     * Update OHLCV data for a specific symbol.
     *
     * @param symbol The stock symbol
     * @param name The instrument name (optional, used only if the instrument doesn't exist)
     * @param type The instrument type (optional, used only if the instrument doesn't exist)
     * @return Number of data points updated
     */
    public int updateOHLCVData(String symbol, String name, InstrumentType type) {
        try {
            // Get the last date we have data for
            LocalDate startDate = dbManager.getLastDataDate(symbol);
            if (startDate == null) {
                // If no data exists, start from 5 years ago
                startDate = LocalDate.now().minusYears(5);
            } else {
                // Start from the day after the last data point
                startDate = startDate.plusDays(1);
            }

            // Only download if we need new data
            if (startDate.isBefore(LocalDate.now()) || startDate.isEqual(LocalDate.now())) {
                logger.info("Updating data for {} from {} to today", symbol, startDate);

                // Create instrument object
                Instrument instrument = new Instrument(symbol, name != null ? name : symbol,
                        type != null ? type : InstrumentType.US_STOCK);

                // Download and save data
                dataProvider.downloadHistoricalData(instrument, startDate, LocalDate.now(), dbManager);

                // Return the number of days updated
                return (int) (LocalDate.now().toEpochDay() - startDate.toEpochDay() + 1);
            } else {
                logger.info("Data is already up to date for {}", symbol);
                return 0;
            }
        } catch (Exception e) {
            logger.error("Error updating OHLCV data for symbol: {}", symbol, e);
            throw new RuntimeException("Failed to update OHLCV data", e);
        }
    }

    /**
     * Update OHLCV data for multiple symbols.
     *
     * @param symbols List of stock symbols
     * @return Number of symbols updated
     */
    public int updateOHLCVDataBatch(List<String> symbols) {
        int updatedCount = 0;

        for (String symbol : symbols) {
            try {
                int updated = updateOHLCVData(symbol, null, null);
                if (updated > 0) {
                    updatedCount++;
                }
            } catch (Exception e) {
                logger.error("Error updating OHLCV data for symbol: {}", symbol, e);
                // Continue with the next symbol
            }
        }

        return updatedCount;
    }

    /**
     * Close database connection when service is destroyed.
     */
    public void cleanup() {
        if (dbManager != null) {
            dbManager.closeConnection();
        }
    }
}
