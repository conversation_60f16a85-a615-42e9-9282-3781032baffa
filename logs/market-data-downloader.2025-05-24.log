2025-05-24 18:23:16 [background-preinit] INFO  o.h.validator.internal.util.Version - HV000001: Hibernate Validator 8.0.1.Final
2025-05-24 18:23:16 [main] INFO  c.investment.InvestmentApplication - Starting InvestmentApplication using Java 21.0.5 with PID 29608 (C:\workspace-intellij\InvestmentTookKitV2\build\classes\java\main started by user in C:\workspace-intellij\InvestmentTookKitV2)
2025-05-24 18:23:16 [main] DEBUG c.investment.InvestmentApplication - Running with Spring Boot v3.2.3, Spring v6.1.4
2025-05-24 18:23:16 [main] INFO  c.investment.InvestmentApplication - No active profile set, falling back to 1 default profile: "default"
2025-05-24 18:23:17 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat initialized with port 8080 (http)
2025-05-24 18:23:17 [main] INFO  o.a.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-8080"]
2025-05-24 18:23:17 [main] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2025-05-24 18:23:17 [main] INFO  o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/10.1.19]
2025-05-24 18:23:17 [main] INFO  o.a.c.c.C.[.[.[/investment-toolkit] - Initializing Spring embedded WebApplicationContext
2025-05-24 18:23:17 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 412 ms
2025-05-24 18:23:17 [main] INFO  c.i.database.DatabaseManager - Connected to the database
2025-05-24 18:23:17 [main] INFO  c.i.database.DatabaseManager - Database tables created or already exist
2025-05-24 18:23:17 [main] INFO  o.a.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-8080"]
2025-05-24 18:23:17 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat started on port 8080 (http) with context path '/investment-toolkit'
2025-05-24 18:23:17 [main] INFO  c.investment.InvestmentApplication - Started InvestmentApplication in 1.106 seconds (process running for 1.344)
