2025-05-28 11:18:55 [main] INFO  c.i.database.DatabaseManager - Connected to the database
2025-05-28 11:18:55 [main] INFO  c.i.database.DatabaseManager - Database tables created or already exist
2025-05-28 11:18:55 [main] INFO  c.investment.MarketDataDownloaderApp - Processing instrument: AAPL
2025-05-28 11:18:55 [main] INFO  c.investment.MarketDataDownloaderApp - Downloading data from 2025-05-24 to today
2025-05-28 11:18:55 [main] INFO  c.i.provider.YahooFinanceProvider - Requesting data from Yahoo Finance for symbol: AAPL, from 2025-05-24 to 2025-05-28
2025-05-28 11:18:55 [main] INFO  c.i.provider.YahooFinanceScraper - Scraping data from Yahoo Finance: https://finance.yahoo.com/quote/AAPL/history/?period1=**********&period2=**********&interval=1d
2025-05-28 11:18:56 [main] INFO  c.i.provider.YahooFinanceScraper - Saved HTML content to file: C:\Users\<USER>\Downloads\yahoo-finance-html\yahoo_finance_AAPL_1748402336.html
2025-05-28 11:18:56 [main] INFO  c.i.provider.YahooFinanceScraper - Read HTML content from file: C:\Users\<USER>\Downloads\yahoo-finance-html\yahoo_finance_AAPL_1748402336.html
2025-05-28 11:18:56 [main] INFO  c.i.provider.YahooFinanceScraper - Extracted 1 OHLCV data points for AAPL
2025-05-28 11:18:56 [main] INFO  c.i.database.DatabaseManager - Saved 1 OHLCV data points for AAPL
2025-05-28 11:18:56 [main] INFO  c.i.provider.YahooFinanceProvider - Downloaded and saved 1 data points for AAPL
2025-05-28 11:18:56 [main] INFO  c.investment.MarketDataDownloaderApp - Data download completed successfully
2025-05-28 11:18:56 [main] INFO  c.i.database.DatabaseManager - Database connection closed
2025-05-28 18:56:46 [Test worker] INFO  c.i.database.DatabaseManager - Connected to the database
2025-05-28 18:56:46 [Test worker] INFO  c.i.database.DatabaseManager - Database tables created or already exist
2025-05-28 18:56:46 [Test worker] INFO  c.i.database.DatabaseManager - Current schema version: 0
2025-05-28 18:56:46 [Test worker] INFO  c.i.database.DatabaseManager - Running migrations from version 0 to 2
2025-05-28 18:56:46 [Test worker] INFO  c.i.database.DatabaseManager - Migrating to schema version 1
2025-05-28 18:56:46 [Test worker] INFO  c.i.database.DatabaseManager - Migrating to schema version 2 - updating instruments table
2025-05-28 18:56:46 [Test worker] INFO  c.i.database.DatabaseManager - Schema already up to date
2025-05-28 18:56:46 [Test worker] INFO  c.i.database.DatabaseManager - Migrations completed successfully
2025-05-28 18:56:46 [Test worker] INFO  c.i.database.DatabaseManager - Database connection closed
2025-05-28 18:56:46 [Test worker] INFO  c.i.database.DatabaseManager - Connected to the database
2025-05-28 18:56:46 [Test worker] INFO  c.i.database.DatabaseManager - Database tables created or already exist
2025-05-28 18:56:46 [Test worker] INFO  c.i.database.DatabaseManager - Current schema version: 0
2025-05-28 18:56:46 [Test worker] INFO  c.i.database.DatabaseManager - Running migrations from version 0 to 2
2025-05-28 18:56:46 [Test worker] INFO  c.i.database.DatabaseManager - Migrating to schema version 1
2025-05-28 18:56:46 [Test worker] INFO  c.i.database.DatabaseManager - Migrating to schema version 2 - updating instruments table
2025-05-28 18:56:46 [Test worker] INFO  c.i.database.DatabaseManager - Schema already up to date
2025-05-28 18:56:46 [Test worker] INFO  c.i.database.DatabaseManager - Migrations completed successfully
2025-05-28 18:56:46 [Test worker] INFO  c.i.database.DatabaseManager - Database connection closed
2025-05-28 18:56:46 [Test worker] INFO  c.i.database.DatabaseManager - Connected to the database
2025-05-28 18:56:46 [Test worker] INFO  c.i.database.DatabaseManager - Database tables created or already exist
2025-05-28 18:56:46 [Test worker] INFO  c.i.database.DatabaseManager - Current schema version: 0
2025-05-28 18:56:46 [Test worker] INFO  c.i.database.DatabaseManager - Running migrations from version 0 to 2
2025-05-28 18:56:46 [Test worker] INFO  c.i.database.DatabaseManager - Migrating to schema version 1
2025-05-28 18:56:46 [Test worker] INFO  c.i.database.DatabaseManager - Migrating to schema version 2 - updating instruments table
2025-05-28 18:56:46 [Test worker] INFO  c.i.database.DatabaseManager - Schema already up to date
2025-05-28 18:56:46 [Test worker] INFO  c.i.database.DatabaseManager - Migrations completed successfully
2025-05-28 18:56:46 [Test worker] INFO  c.i.database.DatabaseManager - Database connection closed
2025-05-28 18:56:46 [Test worker] INFO  c.i.database.DatabaseManager - Connected to the database
2025-05-28 18:56:46 [Test worker] INFO  c.i.database.DatabaseManager - Database tables created or already exist
2025-05-28 18:56:46 [Test worker] INFO  c.i.database.DatabaseManager - Current schema version: 0
2025-05-28 18:56:46 [Test worker] INFO  c.i.database.DatabaseManager - Running migrations from version 0 to 2
2025-05-28 18:56:46 [Test worker] INFO  c.i.database.DatabaseManager - Migrating to schema version 1
2025-05-28 18:56:47 [Test worker] INFO  c.i.database.DatabaseManager - Migrating to schema version 2 - updating instruments table
2025-05-28 18:56:47 [Test worker] INFO  c.i.database.DatabaseManager - Schema already up to date
2025-05-28 18:56:47 [Test worker] INFO  c.i.database.DatabaseManager - Migrations completed successfully
2025-05-28 18:56:47 [Test worker] INFO  c.i.database.DatabaseManager - Database connection closed
2025-05-28 18:56:47 [Test worker] INFO  c.i.database.DatabaseManager - Connected to the database
2025-05-28 18:56:47 [Test worker] INFO  c.i.database.DatabaseManager - Database tables created or already exist
2025-05-28 18:56:47 [Test worker] INFO  c.i.database.DatabaseManager - Current schema version: 0
2025-05-28 18:56:47 [Test worker] INFO  c.i.database.DatabaseManager - Running migrations from version 0 to 2
2025-05-28 18:56:47 [Test worker] INFO  c.i.database.DatabaseManager - Migrating to schema version 1
2025-05-28 18:56:47 [Test worker] INFO  c.i.database.DatabaseManager - Migrating to schema version 2 - updating instruments table
2025-05-28 18:56:47 [Test worker] INFO  c.i.database.DatabaseManager - Schema already up to date
2025-05-28 18:56:47 [Test worker] INFO  c.i.database.DatabaseManager - Migrations completed successfully
2025-05-28 18:56:47 [Test worker] INFO  c.i.database.DatabaseManager - Database connection closed
2025-05-28 19:16:32 [Test worker] INFO  c.i.database.DatabaseManager - Connected to the database
2025-05-28 19:16:32 [Test worker] INFO  c.i.database.DatabaseManager - Database tables created or already exist
2025-05-28 19:16:32 [Test worker] INFO  c.i.database.DatabaseManager - Current schema version: 0
2025-05-28 19:16:32 [Test worker] INFO  c.i.database.DatabaseManager - Running migrations from version 0 to 2
2025-05-28 19:16:32 [Test worker] INFO  c.i.database.DatabaseManager - Migrating to schema version 1
2025-05-28 19:16:32 [Test worker] INFO  c.i.database.DatabaseManager - Migrating to schema version 2 - updating instruments table
2025-05-28 19:16:32 [Test worker] INFO  c.i.database.DatabaseManager - Schema already up to date
2025-05-28 19:16:32 [Test worker] INFO  c.i.database.DatabaseManager - Migrations completed successfully
2025-05-28 19:16:32 [Test worker] INFO  c.i.database.DatabaseManager - Database connection closed
2025-05-28 19:16:32 [Test worker] INFO  c.i.database.DatabaseManager - Connected to the database
2025-05-28 19:16:32 [Test worker] INFO  c.i.database.DatabaseManager - Database tables created or already exist
2025-05-28 19:16:32 [Test worker] INFO  c.i.database.DatabaseManager - Current schema version: 0
2025-05-28 19:16:32 [Test worker] INFO  c.i.database.DatabaseManager - Running migrations from version 0 to 2
2025-05-28 19:16:32 [Test worker] INFO  c.i.database.DatabaseManager - Migrating to schema version 1
2025-05-28 19:16:32 [Test worker] INFO  c.i.database.DatabaseManager - Migrating to schema version 2 - updating instruments table
2025-05-28 19:16:32 [Test worker] INFO  c.i.database.DatabaseManager - Schema already up to date
2025-05-28 19:16:32 [Test worker] INFO  c.i.database.DatabaseManager - Migrations completed successfully
2025-05-28 19:16:32 [Test worker] INFO  c.i.database.DatabaseManager - Database connection closed
2025-05-28 19:16:32 [Test worker] INFO  c.i.database.DatabaseManager - Connected to the database
2025-05-28 19:16:32 [Test worker] INFO  c.i.database.DatabaseManager - Database tables created or already exist
2025-05-28 19:16:32 [Test worker] INFO  c.i.database.DatabaseManager - Current schema version: 0
2025-05-28 19:16:32 [Test worker] INFO  c.i.database.DatabaseManager - Running migrations from version 0 to 2
2025-05-28 19:16:32 [Test worker] INFO  c.i.database.DatabaseManager - Migrating to schema version 1
2025-05-28 19:16:32 [Test worker] INFO  c.i.database.DatabaseManager - Migrating to schema version 2 - updating instruments table
2025-05-28 19:16:32 [Test worker] INFO  c.i.database.DatabaseManager - Schema already up to date
2025-05-28 19:16:32 [Test worker] INFO  c.i.database.DatabaseManager - Migrations completed successfully
2025-05-28 19:16:32 [Test worker] INFO  c.i.database.DatabaseManager - Database connection closed
2025-05-28 19:16:32 [Test worker] INFO  c.i.database.DatabaseManager - Connected to the database
2025-05-28 19:16:32 [Test worker] INFO  c.i.database.DatabaseManager - Database tables created or already exist
2025-05-28 19:16:32 [Test worker] INFO  c.i.database.DatabaseManager - Current schema version: 0
2025-05-28 19:16:32 [Test worker] INFO  c.i.database.DatabaseManager - Running migrations from version 0 to 2
2025-05-28 19:16:32 [Test worker] INFO  c.i.database.DatabaseManager - Migrating to schema version 1
2025-05-28 19:16:32 [Test worker] INFO  c.i.database.DatabaseManager - Migrating to schema version 2 - updating instruments table
2025-05-28 19:16:32 [Test worker] INFO  c.i.database.DatabaseManager - Schema already up to date
2025-05-28 19:16:32 [Test worker] INFO  c.i.database.DatabaseManager - Migrations completed successfully
2025-05-28 19:16:32 [Test worker] INFO  c.i.database.DatabaseManager - Database connection closed
2025-05-28 19:16:32 [Test worker] INFO  c.i.database.DatabaseManager - Connected to the database
2025-05-28 19:16:32 [Test worker] INFO  c.i.database.DatabaseManager - Database tables created or already exist
2025-05-28 19:16:32 [Test worker] INFO  c.i.database.DatabaseManager - Current schema version: 0
2025-05-28 19:16:32 [Test worker] INFO  c.i.database.DatabaseManager - Running migrations from version 0 to 2
2025-05-28 19:16:32 [Test worker] INFO  c.i.database.DatabaseManager - Migrating to schema version 1
2025-05-28 19:16:32 [Test worker] INFO  c.i.database.DatabaseManager - Migrating to schema version 2 - updating instruments table
2025-05-28 19:16:32 [Test worker] INFO  c.i.database.DatabaseManager - Schema already up to date
2025-05-28 19:16:32 [Test worker] INFO  c.i.database.DatabaseManager - Migrations completed successfully
2025-05-28 19:16:32 [Test worker] INFO  c.i.database.DatabaseManager - Database connection closed
2025-05-28 19:23:21 [Test worker] INFO  c.i.database.DatabaseManager - Connected to the database
2025-05-28 19:23:21 [Test worker] INFO  c.i.database.DatabaseManager - Database tables created or already exist
2025-05-28 19:23:21 [Test worker] INFO  c.i.database.DatabaseManager - Current schema version: 0
2025-05-28 19:23:21 [Test worker] INFO  c.i.database.DatabaseManager - Running migrations from version 0 to 2
2025-05-28 19:23:21 [Test worker] INFO  c.i.database.DatabaseManager - Migrating to schema version 1
2025-05-28 19:23:21 [Test worker] INFO  c.i.database.DatabaseManager - Migrating to schema version 2 - updating instruments table
2025-05-28 19:23:21 [Test worker] INFO  c.i.database.DatabaseManager - Schema already up to date
2025-05-28 19:23:21 [Test worker] INFO  c.i.database.DatabaseManager - Migrations completed successfully
2025-05-28 19:23:21 [Test worker] INFO  c.i.database.DatabaseManager - Database connection closed
2025-05-28 19:24:48 [Test worker] INFO  c.i.database.DatabaseManager - Connected to the database
2025-05-28 19:24:48 [Test worker] INFO  c.i.database.DatabaseManager - Database tables created or already exist
2025-05-28 19:24:48 [Test worker] INFO  c.i.database.DatabaseManager - Current schema version: 0
2025-05-28 19:24:48 [Test worker] INFO  c.i.database.DatabaseManager - Running migrations from version 0 to 2
2025-05-28 19:24:48 [Test worker] INFO  c.i.database.DatabaseManager - Migrating to schema version 1
2025-05-28 19:24:48 [Test worker] INFO  c.i.database.DatabaseManager - Migrating to schema version 2 - updating instruments table
2025-05-28 19:24:48 [Test worker] INFO  c.i.database.DatabaseManager - Schema already up to date
2025-05-28 19:24:48 [Test worker] INFO  c.i.database.DatabaseManager - Migrations completed successfully
2025-05-28 19:24:48 [Test worker] INFO  c.i.database.DatabaseManager - Database connection closed
2025-05-28 20:25:48 [background-preinit] INFO  o.h.validator.internal.util.Version - HV000001: Hibernate Validator 8.0.1.Final
2025-05-28 20:25:48 [main] INFO  c.investment.InvestmentApplication - Starting InvestmentApplication using Java 21.0.5 with PID 12236 (C:\workspace-intellij\InvestmentTookKitV2\build\classes\java\main started by user in C:\workspace-intellij\InvestmentTookKitV2)
2025-05-28 20:25:48 [main] DEBUG c.investment.InvestmentApplication - Running with Spring Boot v3.2.3, Spring v6.1.4
2025-05-28 20:25:48 [main] INFO  c.investment.InvestmentApplication - No active profile set, falling back to 1 default profile: "default"
2025-05-28 20:25:49 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat initialized with port 8080 (http)
2025-05-28 20:25:49 [main] INFO  o.a.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-8080"]
2025-05-28 20:25:49 [main] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2025-05-28 20:25:49 [main] INFO  o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/10.1.19]
2025-05-28 20:25:49 [main] INFO  o.a.c.c.C.[.[.[/investment-toolkit] - Initializing Spring embedded WebApplicationContext
2025-05-28 20:25:49 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 669 ms
2025-05-28 20:25:49 [main] INFO  c.i.database.DatabaseManager - Connected to the database
2025-05-28 20:25:49 [main] INFO  c.i.database.DatabaseManager - Database tables created or already exist
2025-05-28 20:25:49 [main] INFO  c.i.database.DatabaseManager - Current schema version: 0
2025-05-28 20:25:49 [main] INFO  c.i.database.DatabaseManager - Running migrations from version 0 to 2
2025-05-28 20:25:49 [main] INFO  c.i.database.DatabaseManager - Migrating to schema version 1
2025-05-28 20:25:49 [main] INFO  c.i.database.DatabaseManager - Migrating to schema version 2 - updating instruments table
2025-05-28 20:25:49 [main] INFO  c.i.database.DatabaseManager - Detected old schema, performing migration
2025-05-28 20:25:49 [main] ERROR c.i.database.DatabaseManager - Error initializing database
java.sql.SQLException: Catalog Error: Could not drop the table because this table is main key table of the table "ohlcv"
	at org.duckdb.DuckDBNative.duckdb_jdbc_execute(Native Method)
	at org.duckdb.DuckDBPreparedStatement.execute(DuckDBPreparedStatement.java:148)
	at org.duckdb.DuckDBPreparedStatement.execute(DuckDBPreparedStatement.java:127)
	at org.duckdb.DuckDBPreparedStatement.execute(DuckDBPreparedStatement.java:202)
	at com.investment.database.DatabaseManager.migrateToVersion2(DatabaseManager.java:200)
	at com.investment.database.DatabaseManager.runMigrations(DatabaseManager.java:124)
	at com.investment.database.DatabaseManager.initDatabase(DatabaseManager.java:41)
	at com.investment.config.AppConfig.databaseManager(AppConfig.java:18)
	at com.investment.config.AppConfig$$SpringCGLIB$$0.CGLIB$databaseManager$1(<generated>)
	at com.investment.config.AppConfig$$SpringCGLIB$$FastClass$$1.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invokeSuper(MethodProxy.java:258)
	at org.springframework.context.annotation.ConfigurationClassEnhancer$BeanMethodInterceptor.intercept(ConfigurationClassEnhancer.java:331)
	at com.investment.config.AppConfig$$SpringCGLIB$$0.databaseManager(<generated>)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.springframework.beans.factory.support.SimpleInstantiationStrategy.instantiate(SimpleInstantiationStrategy.java:140)
	at org.springframework.beans.factory.support.ConstructorResolver.instantiate(ConstructorResolver.java:647)
	at org.springframework.beans.factory.support.ConstructorResolver.instantiateUsingFactoryMethod(ConstructorResolver.java:485)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.instantiateUsingFactoryMethod(AbstractAutowireCapableBeanFactory.java:1335)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBeanInstance(AbstractAutowireCapableBeanFactory.java:1165)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:562)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:522)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:325)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:323)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:199)
	at org.springframework.beans.factory.config.DependencyDescriptor.resolveCandidate(DependencyDescriptor.java:254)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.doResolveDependency(DefaultListableBeanFactory.java:1443)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveDependency(DefaultListableBeanFactory.java:1353)
	at org.springframework.beans.factory.support.ConstructorResolver.resolveAutowiredArgument(ConstructorResolver.java:907)
	at org.springframework.beans.factory.support.ConstructorResolver.createArgumentArray(ConstructorResolver.java:785)
	at org.springframework.beans.factory.support.ConstructorResolver.autowireConstructor(ConstructorResolver.java:237)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.autowireConstructor(AbstractAutowireCapableBeanFactory.java:1355)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBeanInstance(AbstractAutowireCapableBeanFactory.java:1192)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:562)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:522)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:325)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:323)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:199)
	at org.springframework.beans.factory.config.DependencyDescriptor.resolveCandidate(DependencyDescriptor.java:254)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.doResolveDependency(DefaultListableBeanFactory.java:1443)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveDependency(DefaultListableBeanFactory.java:1353)
	at org.springframework.beans.factory.support.ConstructorResolver.resolveAutowiredArgument(ConstructorResolver.java:907)
	at org.springframework.beans.factory.support.ConstructorResolver.createArgumentArray(ConstructorResolver.java:785)
	at org.springframework.beans.factory.support.ConstructorResolver.autowireConstructor(ConstructorResolver.java:237)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.autowireConstructor(AbstractAutowireCapableBeanFactory.java:1355)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBeanInstance(AbstractAutowireCapableBeanFactory.java:1192)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:562)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:522)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:325)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:323)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:199)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.preInstantiateSingletons(DefaultListableBeanFactory.java:975)
	at org.springframework.context.support.AbstractApplicationContext.finishBeanFactoryInitialization(AbstractApplicationContext.java:959)
	at org.springframework.context.support.AbstractApplicationContext.refresh(AbstractApplicationContext.java:624)
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.refresh(ServletWebServerApplicationContext.java:146)
	at org.springframework.boot.SpringApplication.refresh(SpringApplication.java:754)
	at org.springframework.boot.SpringApplication.refreshContext(SpringApplication.java:456)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:334)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1354)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1343)
	at com.investment.InvestmentApplication.main(InvestmentApplication.java:23)
2025-05-28 20:25:49 [main] WARN  o.s.b.w.s.c.AnnotationConfigServletWebServerApplicationContext - Exception encountered during context initialization - cancelling refresh attempt: org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'OHLCVController' defined in file [C:\workspace-intellij\InvestmentTookKitV2\build\classes\java\main\com\investment\api\controller\OHLCVController.class]: Unsatisfied dependency expressed through constructor parameter 0: Error creating bean with name 'OHLCVService' defined in file [C:\workspace-intellij\InvestmentTookKitV2\build\classes\java\main\com\investment\service\OHLCVService.class]: Unsatisfied dependency expressed through constructor parameter 0: Error creating bean with name 'databaseManager' defined in class path resource [com/investment/config/AppConfig.class]: Failed to instantiate [com.investment.database.DatabaseManager]: Factory method 'databaseManager' threw exception with message: Failed to initialize database
2025-05-28 20:25:49 [main] INFO  o.a.catalina.core.StandardService - Stopping service [Tomcat]
2025-05-28 20:25:49 [main] INFO  o.s.b.a.l.ConditionEvaluationReportLogger - 

Error starting ApplicationContext. To display the condition evaluation report re-run your application with 'debug' enabled.
2025-05-28 20:25:49 [main] ERROR o.s.boot.SpringApplication - Application run failed
org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'OHLCVController' defined in file [C:\workspace-intellij\InvestmentTookKitV2\build\classes\java\main\com\investment\api\controller\OHLCVController.class]: Unsatisfied dependency expressed through constructor parameter 0: Error creating bean with name 'OHLCVService' defined in file [C:\workspace-intellij\InvestmentTookKitV2\build\classes\java\main\com\investment\service\OHLCVService.class]: Unsatisfied dependency expressed through constructor parameter 0: Error creating bean with name 'databaseManager' defined in class path resource [com/investment/config/AppConfig.class]: Failed to instantiate [com.investment.database.DatabaseManager]: Factory method 'databaseManager' threw exception with message: Failed to initialize database
	at org.springframework.beans.factory.support.ConstructorResolver.createArgumentArray(ConstructorResolver.java:798)
	at org.springframework.beans.factory.support.ConstructorResolver.autowireConstructor(ConstructorResolver.java:237)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.autowireConstructor(AbstractAutowireCapableBeanFactory.java:1355)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBeanInstance(AbstractAutowireCapableBeanFactory.java:1192)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:562)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:522)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:325)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:323)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:199)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.preInstantiateSingletons(DefaultListableBeanFactory.java:975)
	at org.springframework.context.support.AbstractApplicationContext.finishBeanFactoryInitialization(AbstractApplicationContext.java:959)
	at org.springframework.context.support.AbstractApplicationContext.refresh(AbstractApplicationContext.java:624)
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.refresh(ServletWebServerApplicationContext.java:146)
	at org.springframework.boot.SpringApplication.refresh(SpringApplication.java:754)
	at org.springframework.boot.SpringApplication.refreshContext(SpringApplication.java:456)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:334)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1354)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1343)
	at com.investment.InvestmentApplication.main(InvestmentApplication.java:23)
Caused by: org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'OHLCVService' defined in file [C:\workspace-intellij\InvestmentTookKitV2\build\classes\java\main\com\investment\service\OHLCVService.class]: Unsatisfied dependency expressed through constructor parameter 0: Error creating bean with name 'databaseManager' defined in class path resource [com/investment/config/AppConfig.class]: Failed to instantiate [com.investment.database.DatabaseManager]: Factory method 'databaseManager' threw exception with message: Failed to initialize database
	at org.springframework.beans.factory.support.ConstructorResolver.createArgumentArray(ConstructorResolver.java:798)
	at org.springframework.beans.factory.support.ConstructorResolver.autowireConstructor(ConstructorResolver.java:237)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.autowireConstructor(AbstractAutowireCapableBeanFactory.java:1355)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBeanInstance(AbstractAutowireCapableBeanFactory.java:1192)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:562)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:522)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:325)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:323)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:199)
	at org.springframework.beans.factory.config.DependencyDescriptor.resolveCandidate(DependencyDescriptor.java:254)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.doResolveDependency(DefaultListableBeanFactory.java:1443)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveDependency(DefaultListableBeanFactory.java:1353)
	at org.springframework.beans.factory.support.ConstructorResolver.resolveAutowiredArgument(ConstructorResolver.java:907)
	at org.springframework.beans.factory.support.ConstructorResolver.createArgumentArray(ConstructorResolver.java:785)
	... 19 common frames omitted
Caused by: org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'databaseManager' defined in class path resource [com/investment/config/AppConfig.class]: Failed to instantiate [com.investment.database.DatabaseManager]: Factory method 'databaseManager' threw exception with message: Failed to initialize database
	at org.springframework.beans.factory.support.ConstructorResolver.instantiate(ConstructorResolver.java:651)
	at org.springframework.beans.factory.support.ConstructorResolver.instantiateUsingFactoryMethod(ConstructorResolver.java:485)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.instantiateUsingFactoryMethod(AbstractAutowireCapableBeanFactory.java:1335)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBeanInstance(AbstractAutowireCapableBeanFactory.java:1165)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:562)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:522)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:325)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:323)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:199)
	at org.springframework.beans.factory.config.DependencyDescriptor.resolveCandidate(DependencyDescriptor.java:254)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.doResolveDependency(DefaultListableBeanFactory.java:1443)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveDependency(DefaultListableBeanFactory.java:1353)
	at org.springframework.beans.factory.support.ConstructorResolver.resolveAutowiredArgument(ConstructorResolver.java:907)
	at org.springframework.beans.factory.support.ConstructorResolver.createArgumentArray(ConstructorResolver.java:785)
	... 33 common frames omitted
Caused by: org.springframework.beans.BeanInstantiationException: Failed to instantiate [com.investment.database.DatabaseManager]: Factory method 'databaseManager' threw exception with message: Failed to initialize database
	at org.springframework.beans.factory.support.SimpleInstantiationStrategy.instantiate(SimpleInstantiationStrategy.java:177)
	at org.springframework.beans.factory.support.ConstructorResolver.instantiate(ConstructorResolver.java:647)
	... 47 common frames omitted
Caused by: java.lang.RuntimeException: Failed to initialize database
	at com.investment.database.DatabaseManager.initDatabase(DatabaseManager.java:44)
	at com.investment.config.AppConfig.databaseManager(AppConfig.java:18)
	at com.investment.config.AppConfig$$SpringCGLIB$$0.CGLIB$databaseManager$1(<generated>)
	at com.investment.config.AppConfig$$SpringCGLIB$$FastClass$$1.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invokeSuper(MethodProxy.java:258)
	at org.springframework.context.annotation.ConfigurationClassEnhancer$BeanMethodInterceptor.intercept(ConfigurationClassEnhancer.java:331)
	at com.investment.config.AppConfig$$SpringCGLIB$$0.databaseManager(<generated>)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.springframework.beans.factory.support.SimpleInstantiationStrategy.instantiate(SimpleInstantiationStrategy.java:140)
	... 48 common frames omitted
Caused by: java.sql.SQLException: Catalog Error: Could not drop the table because this table is main key table of the table "ohlcv"
	at org.duckdb.DuckDBNative.duckdb_jdbc_execute(Native Method)
	at org.duckdb.DuckDBPreparedStatement.execute(DuckDBPreparedStatement.java:148)
	at org.duckdb.DuckDBPreparedStatement.execute(DuckDBPreparedStatement.java:127)
	at org.duckdb.DuckDBPreparedStatement.execute(DuckDBPreparedStatement.java:202)
	at com.investment.database.DatabaseManager.migrateToVersion2(DatabaseManager.java:200)
	at com.investment.database.DatabaseManager.runMigrations(DatabaseManager.java:124)
	at com.investment.database.DatabaseManager.initDatabase(DatabaseManager.java:41)
	... 57 common frames omitted
2025-05-28 21:57:21 [background-preinit] INFO  o.h.validator.internal.util.Version - HV000001: Hibernate Validator 8.0.1.Final
2025-05-28 21:57:21 [main] INFO  c.investment.InvestmentApplication - Starting InvestmentApplication using Java 21.0.5 with PID 1812 (C:\workspace-intellij\InvestmentTookKitV2\build\classes\java\main started by user in C:\workspace-intellij\InvestmentTookKitV2)
2025-05-28 21:57:21 [main] DEBUG c.investment.InvestmentApplication - Running with Spring Boot v3.2.3, Spring v6.1.4
2025-05-28 21:57:21 [main] INFO  c.investment.InvestmentApplication - No active profile set, falling back to 1 default profile: "default"
2025-05-28 21:57:21 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat initialized with port 8080 (http)
2025-05-28 21:57:21 [main] INFO  o.a.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-8080"]
2025-05-28 21:57:21 [main] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2025-05-28 21:57:21 [main] INFO  o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/10.1.19]
2025-05-28 21:57:21 [main] INFO  o.a.c.c.C.[.[.[/investment-toolkit] - Initializing Spring embedded WebApplicationContext
2025-05-28 21:57:21 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 693 ms
2025-05-28 21:57:22 [main] INFO  c.i.database.DatabaseManager - Connected to the database
2025-05-28 21:57:22 [main] INFO  c.i.database.DatabaseManager - Database tables created or already exist
2025-05-28 21:57:22 [main] INFO  c.i.database.DatabaseManager - Current schema version: 1
2025-05-28 21:57:22 [main] INFO  c.i.database.DatabaseManager - Running migrations from version 1 to 2
2025-05-28 21:57:22 [main] INFO  c.i.database.DatabaseManager - Migrating to schema version 2 - updating instruments table
2025-05-28 21:57:22 [main] INFO  c.i.database.DatabaseManager - Detected old schema, performing migration
2025-05-28 21:57:22 [main] ERROR c.i.database.DatabaseManager - Error initializing database
java.sql.SQLException: Catalog Error: Table with name "instruments_new" already exists!
	at org.duckdb.DuckDBNative.duckdb_jdbc_execute(Native Method)
	at org.duckdb.DuckDBPreparedStatement.execute(DuckDBPreparedStatement.java:148)
	at org.duckdb.DuckDBPreparedStatement.execute(DuckDBPreparedStatement.java:127)
	at org.duckdb.DuckDBPreparedStatement.execute(DuckDBPreparedStatement.java:202)
	at com.investment.database.DatabaseManager.migrateToVersion2(DatabaseManager.java:209)
	at com.investment.database.DatabaseManager.runMigrations(DatabaseManager.java:126)
	at com.investment.database.DatabaseManager.initDatabase(DatabaseManager.java:43)
	at com.investment.config.AppConfig.databaseManager(AppConfig.java:18)
	at com.investment.config.AppConfig$$SpringCGLIB$$0.CGLIB$databaseManager$0(<generated>)
	at com.investment.config.AppConfig$$SpringCGLIB$$FastClass$$1.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invokeSuper(MethodProxy.java:258)
	at org.springframework.context.annotation.ConfigurationClassEnhancer$BeanMethodInterceptor.intercept(ConfigurationClassEnhancer.java:331)
	at com.investment.config.AppConfig$$SpringCGLIB$$0.databaseManager(<generated>)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.springframework.beans.factory.support.SimpleInstantiationStrategy.instantiate(SimpleInstantiationStrategy.java:140)
	at org.springframework.beans.factory.support.ConstructorResolver.instantiate(ConstructorResolver.java:647)
	at org.springframework.beans.factory.support.ConstructorResolver.instantiateUsingFactoryMethod(ConstructorResolver.java:485)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.instantiateUsingFactoryMethod(AbstractAutowireCapableBeanFactory.java:1335)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBeanInstance(AbstractAutowireCapableBeanFactory.java:1165)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:562)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:522)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:325)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:323)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:199)
	at org.springframework.beans.factory.config.DependencyDescriptor.resolveCandidate(DependencyDescriptor.java:254)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.doResolveDependency(DefaultListableBeanFactory.java:1443)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveDependency(DefaultListableBeanFactory.java:1353)
	at org.springframework.beans.factory.support.ConstructorResolver.resolveAutowiredArgument(ConstructorResolver.java:907)
	at org.springframework.beans.factory.support.ConstructorResolver.createArgumentArray(ConstructorResolver.java:785)
	at org.springframework.beans.factory.support.ConstructorResolver.autowireConstructor(ConstructorResolver.java:237)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.autowireConstructor(AbstractAutowireCapableBeanFactory.java:1355)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBeanInstance(AbstractAutowireCapableBeanFactory.java:1192)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:562)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:522)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:325)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:323)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:199)
	at org.springframework.beans.factory.config.DependencyDescriptor.resolveCandidate(DependencyDescriptor.java:254)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.doResolveDependency(DefaultListableBeanFactory.java:1443)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveDependency(DefaultListableBeanFactory.java:1353)
	at org.springframework.beans.factory.support.ConstructorResolver.resolveAutowiredArgument(ConstructorResolver.java:907)
	at org.springframework.beans.factory.support.ConstructorResolver.createArgumentArray(ConstructorResolver.java:785)
	at org.springframework.beans.factory.support.ConstructorResolver.autowireConstructor(ConstructorResolver.java:237)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.autowireConstructor(AbstractAutowireCapableBeanFactory.java:1355)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBeanInstance(AbstractAutowireCapableBeanFactory.java:1192)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:562)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:522)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:325)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:323)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:199)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.preInstantiateSingletons(DefaultListableBeanFactory.java:975)
	at org.springframework.context.support.AbstractApplicationContext.finishBeanFactoryInitialization(AbstractApplicationContext.java:959)
	at org.springframework.context.support.AbstractApplicationContext.refresh(AbstractApplicationContext.java:624)
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.refresh(ServletWebServerApplicationContext.java:146)
	at org.springframework.boot.SpringApplication.refresh(SpringApplication.java:754)
	at org.springframework.boot.SpringApplication.refreshContext(SpringApplication.java:456)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:334)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1354)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1343)
	at com.investment.InvestmentApplication.main(InvestmentApplication.java:23)
2025-05-28 21:57:22 [main] WARN  o.s.b.w.s.c.AnnotationConfigServletWebServerApplicationContext - Exception encountered during context initialization - cancelling refresh attempt: org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'OHLCVController' defined in file [C:\workspace-intellij\InvestmentTookKitV2\build\classes\java\main\com\investment\api\controller\OHLCVController.class]: Unsatisfied dependency expressed through constructor parameter 0: Error creating bean with name 'OHLCVService' defined in file [C:\workspace-intellij\InvestmentTookKitV2\build\classes\java\main\com\investment\service\OHLCVService.class]: Unsatisfied dependency expressed through constructor parameter 0: Error creating bean with name 'databaseManager' defined in class path resource [com/investment/config/AppConfig.class]: Failed to instantiate [com.investment.database.DatabaseManager]: Factory method 'databaseManager' threw exception with message: Failed to initialize database
2025-05-28 21:57:22 [main] INFO  o.a.catalina.core.StandardService - Stopping service [Tomcat]
2025-05-28 21:57:22 [main] INFO  o.s.b.a.l.ConditionEvaluationReportLogger - 

Error starting ApplicationContext. To display the condition evaluation report re-run your application with 'debug' enabled.
2025-05-28 21:57:22 [main] ERROR o.s.boot.SpringApplication - Application run failed
org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'OHLCVController' defined in file [C:\workspace-intellij\InvestmentTookKitV2\build\classes\java\main\com\investment\api\controller\OHLCVController.class]: Unsatisfied dependency expressed through constructor parameter 0: Error creating bean with name 'OHLCVService' defined in file [C:\workspace-intellij\InvestmentTookKitV2\build\classes\java\main\com\investment\service\OHLCVService.class]: Unsatisfied dependency expressed through constructor parameter 0: Error creating bean with name 'databaseManager' defined in class path resource [com/investment/config/AppConfig.class]: Failed to instantiate [com.investment.database.DatabaseManager]: Factory method 'databaseManager' threw exception with message: Failed to initialize database
	at org.springframework.beans.factory.support.ConstructorResolver.createArgumentArray(ConstructorResolver.java:798)
	at org.springframework.beans.factory.support.ConstructorResolver.autowireConstructor(ConstructorResolver.java:237)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.autowireConstructor(AbstractAutowireCapableBeanFactory.java:1355)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBeanInstance(AbstractAutowireCapableBeanFactory.java:1192)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:562)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:522)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:325)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:323)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:199)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.preInstantiateSingletons(DefaultListableBeanFactory.java:975)
	at org.springframework.context.support.AbstractApplicationContext.finishBeanFactoryInitialization(AbstractApplicationContext.java:959)
	at org.springframework.context.support.AbstractApplicationContext.refresh(AbstractApplicationContext.java:624)
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.refresh(ServletWebServerApplicationContext.java:146)
	at org.springframework.boot.SpringApplication.refresh(SpringApplication.java:754)
	at org.springframework.boot.SpringApplication.refreshContext(SpringApplication.java:456)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:334)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1354)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1343)
	at com.investment.InvestmentApplication.main(InvestmentApplication.java:23)
Caused by: org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'OHLCVService' defined in file [C:\workspace-intellij\InvestmentTookKitV2\build\classes\java\main\com\investment\service\OHLCVService.class]: Unsatisfied dependency expressed through constructor parameter 0: Error creating bean with name 'databaseManager' defined in class path resource [com/investment/config/AppConfig.class]: Failed to instantiate [com.investment.database.DatabaseManager]: Factory method 'databaseManager' threw exception with message: Failed to initialize database
	at org.springframework.beans.factory.support.ConstructorResolver.createArgumentArray(ConstructorResolver.java:798)
	at org.springframework.beans.factory.support.ConstructorResolver.autowireConstructor(ConstructorResolver.java:237)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.autowireConstructor(AbstractAutowireCapableBeanFactory.java:1355)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBeanInstance(AbstractAutowireCapableBeanFactory.java:1192)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:562)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:522)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:325)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:323)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:199)
	at org.springframework.beans.factory.config.DependencyDescriptor.resolveCandidate(DependencyDescriptor.java:254)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.doResolveDependency(DefaultListableBeanFactory.java:1443)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveDependency(DefaultListableBeanFactory.java:1353)
	at org.springframework.beans.factory.support.ConstructorResolver.resolveAutowiredArgument(ConstructorResolver.java:907)
	at org.springframework.beans.factory.support.ConstructorResolver.createArgumentArray(ConstructorResolver.java:785)
	... 19 common frames omitted
Caused by: org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'databaseManager' defined in class path resource [com/investment/config/AppConfig.class]: Failed to instantiate [com.investment.database.DatabaseManager]: Factory method 'databaseManager' threw exception with message: Failed to initialize database
	at org.springframework.beans.factory.support.ConstructorResolver.instantiate(ConstructorResolver.java:651)
	at org.springframework.beans.factory.support.ConstructorResolver.instantiateUsingFactoryMethod(ConstructorResolver.java:485)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.instantiateUsingFactoryMethod(AbstractAutowireCapableBeanFactory.java:1335)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBeanInstance(AbstractAutowireCapableBeanFactory.java:1165)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:562)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:522)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:325)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:323)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:199)
	at org.springframework.beans.factory.config.DependencyDescriptor.resolveCandidate(DependencyDescriptor.java:254)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.doResolveDependency(DefaultListableBeanFactory.java:1443)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveDependency(DefaultListableBeanFactory.java:1353)
	at org.springframework.beans.factory.support.ConstructorResolver.resolveAutowiredArgument(ConstructorResolver.java:907)
	at org.springframework.beans.factory.support.ConstructorResolver.createArgumentArray(ConstructorResolver.java:785)
	... 33 common frames omitted
Caused by: org.springframework.beans.BeanInstantiationException: Failed to instantiate [com.investment.database.DatabaseManager]: Factory method 'databaseManager' threw exception with message: Failed to initialize database
	at org.springframework.beans.factory.support.SimpleInstantiationStrategy.instantiate(SimpleInstantiationStrategy.java:177)
	at org.springframework.beans.factory.support.ConstructorResolver.instantiate(ConstructorResolver.java:647)
	... 47 common frames omitted
Caused by: java.lang.RuntimeException: Failed to initialize database
	at com.investment.database.DatabaseManager.initDatabase(DatabaseManager.java:46)
	at com.investment.config.AppConfig.databaseManager(AppConfig.java:18)
	at com.investment.config.AppConfig$$SpringCGLIB$$0.CGLIB$databaseManager$0(<generated>)
	at com.investment.config.AppConfig$$SpringCGLIB$$FastClass$$1.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invokeSuper(MethodProxy.java:258)
	at org.springframework.context.annotation.ConfigurationClassEnhancer$BeanMethodInterceptor.intercept(ConfigurationClassEnhancer.java:331)
	at com.investment.config.AppConfig$$SpringCGLIB$$0.databaseManager(<generated>)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.springframework.beans.factory.support.SimpleInstantiationStrategy.instantiate(SimpleInstantiationStrategy.java:140)
	... 48 common frames omitted
Caused by: java.sql.SQLException: Catalog Error: Table with name "instruments_new" already exists!
	at org.duckdb.DuckDBNative.duckdb_jdbc_execute(Native Method)
	at org.duckdb.DuckDBPreparedStatement.execute(DuckDBPreparedStatement.java:148)
	at org.duckdb.DuckDBPreparedStatement.execute(DuckDBPreparedStatement.java:127)
	at org.duckdb.DuckDBPreparedStatement.execute(DuckDBPreparedStatement.java:202)
	at com.investment.database.DatabaseManager.migrateToVersion2(DatabaseManager.java:209)
	at com.investment.database.DatabaseManager.runMigrations(DatabaseManager.java:126)
	at com.investment.database.DatabaseManager.initDatabase(DatabaseManager.java:43)
	... 57 common frames omitted
2025-05-28 22:06:42 [background-preinit] INFO  o.h.validator.internal.util.Version - HV000001: Hibernate Validator 8.0.1.Final
2025-05-28 22:06:42 [main] INFO  c.investment.InvestmentApplication - Starting InvestmentApplication using Java 21.0.5 with PID 21844 (C:\workspace-intellij\InvestmentTookKitV2\build\classes\java\main started by user in C:\workspace-intellij\InvestmentTookKitV2)
2025-05-28 22:06:42 [main] DEBUG c.investment.InvestmentApplication - Running with Spring Boot v3.2.3, Spring v6.1.4
2025-05-28 22:06:42 [main] INFO  c.investment.InvestmentApplication - No active profile set, falling back to 1 default profile: "default"
2025-05-28 22:06:43 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat initialized with port 8080 (http)
2025-05-28 22:06:43 [main] INFO  o.a.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-8080"]
2025-05-28 22:06:43 [main] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2025-05-28 22:06:43 [main] INFO  o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/10.1.19]
2025-05-28 22:06:43 [main] INFO  o.a.c.c.C.[.[.[/investment-toolkit] - Initializing Spring embedded WebApplicationContext
2025-05-28 22:06:43 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 686 ms
2025-05-28 22:06:43 [main] INFO  c.i.database.DatabaseManager - Connected to the database
2025-05-28 22:06:43 [main] INFO  c.i.database.DatabaseManager - Database tables created or already exist
2025-05-28 22:06:43 [main] INFO  c.i.database.DatabaseManager - Current schema version: 0
2025-05-28 22:06:43 [main] INFO  c.i.database.DatabaseManager - Running migrations from version 0 to 2
2025-05-28 22:06:43 [main] INFO  c.i.database.DatabaseManager - Migrating to schema version 1
2025-05-28 22:06:43 [main] INFO  c.i.database.DatabaseManager - Migrating to schema version 2 - updating instruments table
2025-05-28 22:06:43 [main] INFO  c.i.database.DatabaseManager - Schema already up to date
2025-05-28 22:06:43 [main] INFO  c.i.database.DatabaseManager - Migrations completed successfully
2025-05-28 22:06:44 [main] INFO  o.a.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-8080"]
2025-05-28 22:06:44 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat started on port 8080 (http) with context path '/investment-toolkit'
2025-05-28 22:06:44 [main] INFO  c.investment.InvestmentApplication - Started InvestmentApplication in 1.709 seconds (process running for 1.99)
2025-05-28 22:07:59 [Test worker] INFO  c.i.database.DatabaseManager - Connected to the database
2025-05-28 22:07:59 [Test worker] INFO  c.i.database.DatabaseManager - Database tables created or already exist
2025-05-28 22:07:59 [Test worker] INFO  c.i.database.DatabaseManager - Current schema version: 0
2025-05-28 22:07:59 [Test worker] INFO  c.i.database.DatabaseManager - Running migrations from version 0 to 2
2025-05-28 22:07:59 [Test worker] INFO  c.i.database.DatabaseManager - Migrating to schema version 1
2025-05-28 22:07:59 [Test worker] INFO  c.i.database.DatabaseManager - Migrating to schema version 2 - updating instruments table
2025-05-28 22:07:59 [Test worker] INFO  c.i.database.DatabaseManager - Schema already up to date
2025-05-28 22:07:59 [Test worker] INFO  c.i.database.DatabaseManager - Migrations completed successfully
2025-05-28 22:07:59 [Test worker] INFO  c.i.database.DatabaseManager - Database connection closed
2025-05-28 22:12:02 [main] INFO  c.i.database.DatabaseManager - Connected to the database
2025-05-28 22:12:02 [main] INFO  c.i.database.DatabaseManager - Database tables created or already exist
2025-05-28 22:12:02 [main] INFO  c.i.database.DatabaseManager - Current schema version: 2
2025-05-28 22:12:03 [main] INFO  c.investment.MarketDataDownloaderApp - Processing instrument: AAPL
2025-05-28 22:12:03 [main] INFO  c.investment.MarketDataDownloaderApp - Downloading data from 1963-05-28 to today
2025-05-28 22:12:03 [main] INFO  c.i.provider.YahooFinanceProvider - Requesting data from Yahoo Finance for symbol: AAPL, from 1963-05-28 to 2025-05-28
2025-05-28 22:12:03 [main] INFO  c.i.provider.YahooFinanceScraper - Scraping data from Yahoo Finance: https://finance.yahoo.com/quote/AAPL/history/?period1=-208224000&period2=**********&interval=1d
2025-05-28 22:12:11 [main] INFO  c.i.provider.YahooFinanceScraper - Saved HTML content to file: C:\Users\<USER>\Downloads\yahoo-finance-html\yahoo_finance_AAPL_1748441531.html
2025-05-28 22:12:11 [main] INFO  c.i.provider.YahooFinanceScraper - Read HTML content from file: C:\Users\<USER>\Downloads\yahoo-finance-html\yahoo_finance_AAPL_1748441531.html
2025-05-28 22:12:11 [main] INFO  c.i.provider.YahooFinanceScraper - Extracted 11204 OHLCV data points for AAPL
2025-05-28 22:12:14 [main] INFO  c.i.database.DatabaseManager - Saved 11204 OHLCV data points for AAPL
2025-05-28 22:12:14 [main] INFO  c.i.provider.YahooFinanceProvider - Downloaded and saved 11204 data points for AAPL
2025-05-28 22:12:14 [main] INFO  c.investment.MarketDataDownloaderApp - Data download completed successfully
2025-05-28 22:12:14 [main] INFO  c.i.database.DatabaseManager - Database connection closed
