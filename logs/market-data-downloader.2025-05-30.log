2025-05-30 00:00:51 [main] INFO  c.i.database.DatabaseManager - Connected to the database
2025-05-30 00:00:51 [main] INFO  c.i.database.DatabaseManager - Database tables created or already exist
2025-05-30 00:00:51 [main] INFO  c.i.database.DatabaseManager - Current schema version: 2
2025-05-30 00:00:51 [main] INFO  c.investment.MarketDataDownloaderApp - Processing instrument: AAPL
2025-05-30 00:00:51 [main] INFO  c.investment.MarketDataDownloaderApp - Downloading data from 2025-05-21 to today
2025-05-30 00:00:51 [main] INFO  c.i.provider.YahooFinanceProvider - Requesting data from Yahoo Finance for symbol: AAPL, from 2025-05-21 to 2025-05-30
2025-05-30 00:00:51 [main] INFO  c.i.provider.YahooFinanceScraper - Scraping data from Yahoo Finance: https://finance.yahoo.com/quote/AAPL/history/?period1=**********&period2=**********&interval=1d
2025-05-30 00:00:52 [main] INFO  c.i.provider.YahooFinanceScraper - Saved HTML content to file: C:\Users\<USER>\Downloads\yahoo-finance-html\yahoo_finance_AAPL_1748534452.html
2025-05-30 00:00:52 [main] INFO  c.i.provider.YahooFinanceScraper - Read HTML content from file: C:\Users\<USER>\Downloads\yahoo-finance-html\yahoo_finance_AAPL_1748534452.html
2025-05-30 00:00:52 [main] INFO  c.i.provider.YahooFinanceScraper - Extracted 6 OHLCV data points for AAPL
2025-05-30 00:00:52 [main] INFO  c.i.database.DatabaseManager - Saved 6 OHLCV data points for AAPL
2025-05-30 00:00:52 [main] INFO  c.i.provider.YahooFinanceProvider - Downloaded and saved 6 data points for AAPL
2025-05-30 00:00:52 [main] INFO  c.investment.MarketDataDownloaderApp - Data download completed successfully
2025-05-30 00:00:52 [main] INFO  c.i.database.DatabaseManager - Database connection closed
2025-05-30 00:21:41 [Test worker] INFO  c.i.database.DatabaseManager - Connected to the database
2025-05-30 00:21:41 [Test worker] INFO  c.i.database.DatabaseManager - Database tables created or already exist
2025-05-30 00:21:41 [Test worker] INFO  c.i.database.DatabaseManager - Current schema version: 0
2025-05-30 00:21:41 [Test worker] INFO  c.i.database.DatabaseManager - Running migrations from version 0 to 2
2025-05-30 00:21:41 [Test worker] INFO  c.i.database.DatabaseManager - Migrating to schema version 1
2025-05-30 00:21:41 [Test worker] INFO  c.i.database.DatabaseManager - Migrating to schema version 2 - updating instruments table
2025-05-30 00:21:41 [Test worker] INFO  c.i.database.DatabaseManager - Schema already up to date
2025-05-30 00:21:41 [Test worker] INFO  c.i.database.DatabaseManager - Migrations completed successfully
2025-05-30 00:21:41 [Test worker] INFO  c.i.database.DatabaseManager - Database connection closed
2025-05-30 00:32:46 [main] ERROR c.i.database.DatabaseManager - Error initializing database
java.sql.SQLException: IO Error: File is already open in 
C:\Program Files\DBeaver\dbeaver.exe (PID 34232)
	at org.duckdb.DuckDBNative.duckdb_jdbc_startup(Native Method)
	at org.duckdb.DuckDBConnection.newConnection(DuckDBConnection.java:52)
	at org.duckdb.DuckDBDriver.connect(DuckDBDriver.java:48)
	at java.sql/java.sql.DriverManager.getConnection(DriverManager.java:683)
	at java.sql/java.sql.DriverManager.getConnection(DriverManager.java:230)
	at com.investment.database.DatabaseManager.initDatabase(DatabaseManager.java:38)
	at com.investment.MarketDataDownloaderApp.main(MarketDataDownloaderApp.java:30)
2025-05-30 00:32:46 [main] ERROR c.investment.MarketDataDownloaderApp - Error in data download process
java.lang.RuntimeException: Failed to initialize database
	at com.investment.database.DatabaseManager.initDatabase(DatabaseManager.java:48)
	at com.investment.MarketDataDownloaderApp.main(MarketDataDownloaderApp.java:30)
Caused by: java.sql.SQLException: IO Error: File is already open in 
C:\Program Files\DBeaver\dbeaver.exe (PID 34232)
	at org.duckdb.DuckDBNative.duckdb_jdbc_startup(Native Method)
	at org.duckdb.DuckDBConnection.newConnection(DuckDBConnection.java:52)
	at org.duckdb.DuckDBDriver.connect(DuckDBDriver.java:48)
	at java.sql/java.sql.DriverManager.getConnection(DriverManager.java:683)
	at java.sql/java.sql.DriverManager.getConnection(DriverManager.java:230)
	at com.investment.database.DatabaseManager.initDatabase(DatabaseManager.java:38)
	... 1 common frames omitted
2025-05-30 00:33:00 [main] INFO  c.i.database.DatabaseManager - Connected to the database
2025-05-30 00:33:00 [main] INFO  c.i.database.DatabaseManager - Database tables created or already exist
2025-05-30 00:33:00 [main] INFO  c.i.database.DatabaseManager - Current schema version: 2
2025-05-30 00:33:00 [main] INFO  c.investment.MarketDataDownloaderApp - Found 10009 instruments in database for data download
2025-05-30 00:33:00 [main] INFO  c.investment.MarketDataDownloaderApp - Download configuration: MarketDataDownloadConfig{startDate=1963-05-30, endDate=2025-05-30, continueOnError=true, maxRetries=3, retryDelayMs=1000, skipExistingData=true}
2025-05-30 00:33:00 [main] INFO  c.investment.MarketDataDownloaderApp - Starting market data download for 10009 instruments
2025-05-30 00:33:00 [main] INFO  c.investment.MarketDataDownloaderApp - Processing instrument 1/10009: A (AGILENT TECHNOLOGIES, INC.)
2025-05-30 00:33:00 [main] INFO  c.investment.MarketDataDownloaderApp - Downloading data for A from 1963-05-30 to 2025-05-30 (attempt 1/4)
2025-05-30 00:33:00 [main] INFO  c.i.provider.YahooFinanceProvider - Requesting data from Yahoo Finance for symbol: A, from 1963-05-30 to 2025-05-30
2025-05-30 00:33:00 [main] INFO  c.i.provider.YahooFinanceScraper - Scraping data from Yahoo Finance: https://finance.yahoo.com/quote/A/history/?period1=-*********&period2=**********&interval=1d
2025-05-30 00:33:04 [main] INFO  c.i.provider.YahooFinanceScraper - Saved HTML content to file: C:\Users\<USER>\Downloads\yahoo-finance-html\yahoo_finance_A_1748536384.html
2025-05-30 00:33:04 [main] INFO  c.i.provider.YahooFinanceScraper - Read HTML content from file: C:\Users\<USER>\Downloads\yahoo-finance-html\yahoo_finance_A_1748536384.html
2025-05-30 00:33:04 [main] INFO  c.i.provider.YahooFinanceScraper - Extracted 6420 OHLCV data points for A
2025-05-30 00:33:05 [main] INFO  c.i.database.DatabaseManager - Saved 6420 OHLCV data points for A
2025-05-30 00:33:05 [main] INFO  c.i.provider.YahooFinanceProvider - Downloaded and saved 6420 data points for A
2025-05-30 00:33:05 [main] INFO  c.investment.MarketDataDownloaderApp - ✓ Successfully processed A
2025-05-30 00:33:06 [main] INFO  c.investment.MarketDataDownloaderApp - Processing instrument 2/10009: AA (Alcoa Corp)
2025-05-30 00:33:06 [main] INFO  c.investment.MarketDataDownloaderApp - Downloading data for AA from 1963-05-30 to 2025-05-30 (attempt 1/4)
2025-05-30 00:33:06 [main] INFO  c.i.provider.YahooFinanceProvider - Requesting data from Yahoo Finance for symbol: AA, from 1963-05-30 to 2025-05-30
2025-05-30 00:33:06 [main] INFO  c.i.provider.YahooFinanceScraper - Scraping data from Yahoo Finance: https://finance.yahoo.com/quote/AA/history/?period1=-*********&period2=**********&interval=1d
2025-05-30 00:33:16 [main] ERROR c.i.provider.YahooFinanceProvider - Error downloading data for instrument: AA
java.io.IOException: Failed to download data: Response{protocol=h2, code=404, message=, url=https://finance.yahoo.com/quote/AA/history/?period1=-*********&period2=**********&interval=1d}
	at com.investment.provider.YahooFinanceScraper.scrapeHistoricalData(YahooFinanceScraper.java:150)
	at com.investment.provider.YahooFinanceProvider.downloadHistoricalData(YahooFinanceProvider.java:32)
	at com.investment.MarketDataDownloaderApp.downloadDataForInstrument(MarketDataDownloaderApp.java:145)
	at com.investment.MarketDataDownloaderApp.downloadMarketDataForInstruments(MarketDataDownloaderApp.java:73)
	at com.investment.MarketDataDownloaderApp.main(MarketDataDownloaderApp.java:44)
2025-05-30 00:33:16 [main] WARN  c.investment.MarketDataDownloaderApp - Attempt 1/4 failed for AA: Failed to download historical data. Retrying in 1000ms...
2025-05-30 00:33:17 [main] INFO  c.investment.MarketDataDownloaderApp - Downloading data for AA from 1963-05-30 to 2025-05-30 (attempt 2/4)
2025-05-30 00:33:17 [main] INFO  c.i.provider.YahooFinanceProvider - Requesting data from Yahoo Finance for symbol: AA, from 1963-05-30 to 2025-05-30
2025-05-30 00:33:17 [main] INFO  c.i.provider.YahooFinanceScraper - Scraping data from Yahoo Finance: https://finance.yahoo.com/quote/AA/history/?period1=-*********&period2=**********&interval=1d
2025-05-30 00:33:26 [main] INFO  c.i.provider.YahooFinanceScraper - Saved HTML content to file: C:\Users\<USER>\Downloads\yahoo-finance-html\yahoo_finance_AA_1748536406.html
2025-05-30 00:33:26 [main] INFO  c.i.provider.YahooFinanceScraper - Read HTML content from file: C:\Users\<USER>\Downloads\yahoo-finance-html\yahoo_finance_AA_1748536406.html
2025-05-30 00:33:26 [main] INFO  c.i.provider.YahooFinanceScraper - Extracted 15602 OHLCV data points for AA
2025-05-30 00:33:30 [main] INFO  c.i.database.DatabaseManager - Saved 15602 OHLCV data points for AA
2025-05-30 00:33:30 [main] INFO  c.i.provider.YahooFinanceProvider - Downloaded and saved 15602 data points for AA
2025-05-30 00:33:30 [main] INFO  c.investment.MarketDataDownloaderApp - ✓ Successfully processed AA
2025-05-30 00:33:30 [main] INFO  c.investment.MarketDataDownloaderApp - Processing instrument 3/10009: AAAU (Goldman Sachs Physical Gold ETF)
2025-05-30 00:33:30 [main] INFO  c.investment.MarketDataDownloaderApp - Downloading data for AAAU from 1963-05-30 to 2025-05-30 (attempt 1/4)
2025-05-30 00:33:30 [main] INFO  c.i.provider.YahooFinanceProvider - Requesting data from Yahoo Finance for symbol: AAAU, from 1963-05-30 to 2025-05-30
2025-05-30 00:33:30 [main] INFO  c.i.provider.YahooFinanceScraper - Scraping data from Yahoo Finance: https://finance.yahoo.com/quote/AAAU/history/?period1=-*********&period2=**********&interval=1d
2025-05-30 00:33:32 [main] INFO  c.i.provider.YahooFinanceScraper - Saved HTML content to file: C:\Users\<USER>\Downloads\yahoo-finance-html\yahoo_finance_AAAU_1748536412.html
2025-05-30 00:33:32 [main] INFO  c.i.provider.YahooFinanceScraper - Read HTML content from file: C:\Users\<USER>\Downloads\yahoo-finance-html\yahoo_finance_AAAU_1748536412.html
2025-05-30 00:33:32 [main] INFO  c.i.provider.YahooFinanceScraper - Extracted 1706 OHLCV data points for AAAU
2025-05-30 00:33:32 [main] INFO  c.i.database.DatabaseManager - Saved 1706 OHLCV data points for AAAU
2025-05-30 00:33:32 [main] INFO  c.i.provider.YahooFinanceProvider - Downloaded and saved 1706 data points for AAAU
2025-05-30 00:33:32 [main] INFO  c.investment.MarketDataDownloaderApp - ✓ Successfully processed AAAU
2025-05-30 00:33:33 [main] INFO  c.investment.MarketDataDownloaderApp - Processing instrument 4/10009: AACB (Artius II Acquisition Inc.)
2025-05-30 00:33:33 [main] INFO  c.investment.MarketDataDownloaderApp - Downloading data for AACB from 1963-05-30 to 2025-05-30 (attempt 1/4)
2025-05-30 00:33:33 [main] INFO  c.i.provider.YahooFinanceProvider - Requesting data from Yahoo Finance for symbol: AACB, from 1963-05-30 to 2025-05-30
2025-05-30 00:33:33 [main] INFO  c.i.provider.YahooFinanceScraper - Scraping data from Yahoo Finance: https://finance.yahoo.com/quote/AACB/history/?period1=-*********&period2=**********&interval=1d
2025-05-30 00:33:35 [main] INFO  c.i.provider.YahooFinanceScraper - Saved HTML content to file: C:\Users\<USER>\Downloads\yahoo-finance-html\yahoo_finance_AACB_1748536415.html
2025-05-30 00:33:35 [main] INFO  c.i.provider.YahooFinanceScraper - Read HTML content from file: C:\Users\<USER>\Downloads\yahoo-finance-html\yahoo_finance_AACB_1748536415.html
2025-05-30 00:33:35 [main] INFO  c.i.provider.YahooFinanceScraper - Extracted 36 OHLCV data points for AACB
2025-05-30 00:33:35 [main] INFO  c.i.database.DatabaseManager - Saved 36 OHLCV data points for AACB
2025-05-30 00:33:35 [main] INFO  c.i.provider.YahooFinanceProvider - Downloaded and saved 36 data points for AACB
2025-05-30 00:33:35 [main] INFO  c.investment.MarketDataDownloaderApp - ✓ Successfully processed AACB
2025-05-30 00:33:35 [main] INFO  c.investment.MarketDataDownloaderApp - Processing instrument 5/10009: AACBR (Artius II Acquisition Inc.)
2025-05-30 00:33:35 [main] INFO  c.investment.MarketDataDownloaderApp - Downloading data for AACBR from 1963-05-30 to 2025-05-30 (attempt 1/4)
2025-05-30 00:33:35 [main] INFO  c.i.provider.YahooFinanceProvider - Requesting data from Yahoo Finance for symbol: AACBR, from 1963-05-30 to 2025-05-30
2025-05-30 00:33:35 [main] INFO  c.i.provider.YahooFinanceScraper - Scraping data from Yahoo Finance: https://finance.yahoo.com/quote/AACBR/history/?period1=-*********&period2=**********&interval=1d
2025-05-30 00:33:37 [main] INFO  c.i.provider.YahooFinanceScraper - Saved HTML content to file: C:\Users\<USER>\Downloads\yahoo-finance-html\yahoo_finance_AACBR_1748536417.html
2025-05-30 00:33:37 [main] INFO  c.i.provider.YahooFinanceScraper - Read HTML content from file: C:\Users\<USER>\Downloads\yahoo-finance-html\yahoo_finance_AACBR_1748536417.html
2025-05-30 00:33:37 [main] INFO  c.i.provider.YahooFinanceScraper - Extracted 1 OHLCV data points for AACBR
2025-05-30 00:33:37 [main] INFO  c.i.database.DatabaseManager - Saved 1 OHLCV data points for AACBR
2025-05-30 00:33:37 [main] INFO  c.i.provider.YahooFinanceProvider - Downloaded and saved 1 data points for AACBR
2025-05-30 00:33:37 [main] INFO  c.investment.MarketDataDownloaderApp - ✓ Successfully processed AACBR
2025-05-30 00:33:37 [main] INFO  c.investment.MarketDataDownloaderApp - Processing instrument 6/10009: AACBU (Artius II Acquisition Inc.)
2025-05-30 00:33:37 [main] INFO  c.investment.MarketDataDownloaderApp - Downloading data for AACBU from 1963-05-30 to 2025-05-30 (attempt 1/4)
2025-05-30 00:33:37 [main] INFO  c.i.provider.YahooFinanceProvider - Requesting data from Yahoo Finance for symbol: AACBU, from 1963-05-30 to 2025-05-30
2025-05-30 00:33:37 [main] INFO  c.i.provider.YahooFinanceScraper - Scraping data from Yahoo Finance: https://finance.yahoo.com/quote/AACBU/history/?period1=-*********&period2=**********&interval=1d
2025-05-30 00:33:38 [main] INFO  c.i.provider.YahooFinanceScraper - Saved HTML content to file: C:\Users\<USER>\Downloads\yahoo-finance-html\yahoo_finance_AACBU_1748536418.html
2025-05-30 00:33:38 [main] INFO  c.i.provider.YahooFinanceScraper - Read HTML content from file: C:\Users\<USER>\Downloads\yahoo-finance-html\yahoo_finance_AACBU_1748536418.html
2025-05-30 00:33:38 [main] INFO  c.i.provider.YahooFinanceScraper - Extracted 45 OHLCV data points for AACBU
2025-05-30 00:33:38 [main] INFO  c.i.database.DatabaseManager - Saved 45 OHLCV data points for AACBU
2025-05-30 00:33:38 [main] INFO  c.i.provider.YahooFinanceProvider - Downloaded and saved 45 data points for AACBU
2025-05-30 00:33:38 [main] INFO  c.investment.MarketDataDownloaderApp - ✓ Successfully processed AACBU
2025-05-30 00:33:39 [main] INFO  c.investment.MarketDataDownloaderApp - Processing instrument 7/10009: AACG (ATA Creativity Global)
2025-05-30 00:33:39 [main] INFO  c.investment.MarketDataDownloaderApp - Downloading data for AACG from 1963-05-30 to 2025-05-30 (attempt 1/4)
2025-05-30 00:33:39 [main] INFO  c.i.provider.YahooFinanceProvider - Requesting data from Yahoo Finance for symbol: AACG, from 1963-05-30 to 2025-05-30
2025-05-30 00:33:39 [main] INFO  c.i.provider.YahooFinanceScraper - Scraping data from Yahoo Finance: https://finance.yahoo.com/quote/AACG/history/?period1=-*********&period2=**********&interval=1d
2025-05-30 00:33:42 [main] INFO  c.i.provider.YahooFinanceScraper - Saved HTML content to file: C:\Users\<USER>\Downloads\yahoo-finance-html\yahoo_finance_AACG_1748536422.html
2025-05-30 00:33:42 [main] INFO  c.i.provider.YahooFinanceScraper - Read HTML content from file: C:\Users\<USER>\Downloads\yahoo-finance-html\yahoo_finance_AACG_1748536422.html
2025-05-30 00:33:42 [main] INFO  c.i.provider.YahooFinanceScraper - Extracted 4259 OHLCV data points for AACG
2025-05-30 00:33:43 [main] INFO  c.i.database.DatabaseManager - Saved 4259 OHLCV data points for AACG
2025-05-30 00:33:43 [main] INFO  c.i.provider.YahooFinanceProvider - Downloaded and saved 4259 data points for AACG
2025-05-30 00:33:43 [main] INFO  c.investment.MarketDataDownloaderApp - ✓ Successfully processed AACG
2025-05-30 00:33:44 [main] INFO  c.investment.MarketDataDownloaderApp - Processing instrument 8/10009: AACI (Armada Acquisition Corp. II)
2025-05-30 00:33:44 [main] INFO  c.investment.MarketDataDownloaderApp - Downloading data for AACI from 1963-05-30 to 2025-05-30 (attempt 1/4)
2025-05-30 00:33:44 [main] INFO  c.i.provider.YahooFinanceProvider - Requesting data from Yahoo Finance for symbol: AACI, from 1963-05-30 to 2025-05-30
2025-05-30 00:33:44 [main] INFO  c.i.provider.YahooFinanceScraper - Scraping data from Yahoo Finance: https://finance.yahoo.com/quote/AACI/history/?period1=-*********&period2=**********&interval=1d
2025-05-30 00:33:44 [main] INFO  c.i.provider.YahooFinanceScraper - Saved HTML content to file: C:\Users\<USER>\Downloads\yahoo-finance-html\yahoo_finance_AACI_1748536424.html
2025-05-30 00:33:44 [main] INFO  c.i.provider.YahooFinanceScraper - Read HTML content from file: C:\Users\<USER>\Downloads\yahoo-finance-html\yahoo_finance_AACI_1748536424.html
2025-05-30 00:33:44 [main] WARN  c.i.provider.YahooFinanceScraper - Could not find historical prices rows for AACI
2025-05-30 00:33:44 [main] INFO  c.i.provider.YahooFinanceProvider - Downloaded and saved 0 data points for AACI
2025-05-30 00:33:44 [main] INFO  c.investment.MarketDataDownloaderApp - ✓ Successfully processed AACI
2025-05-30 00:33:45 [main] INFO  c.investment.MarketDataDownloaderApp - Processing instrument 9/10009: AACIU (Armada Acquisition Corp. II)
2025-05-30 00:33:45 [main] INFO  c.investment.MarketDataDownloaderApp - Downloading data for AACIU from 1963-05-30 to 2025-05-30 (attempt 1/4)
2025-05-30 00:33:45 [main] INFO  c.i.provider.YahooFinanceProvider - Requesting data from Yahoo Finance for symbol: AACIU, from 1963-05-30 to 2025-05-30
2025-05-30 00:33:45 [main] INFO  c.i.provider.YahooFinanceScraper - Scraping data from Yahoo Finance: https://finance.yahoo.com/quote/AACIU/history/?period1=-*********&period2=**********&interval=1d
2025-05-30 00:33:46 [main] INFO  c.i.provider.YahooFinanceScraper - Saved HTML content to file: C:\Users\<USER>\Downloads\yahoo-finance-html\yahoo_finance_AACIU_1748536426.html
2025-05-30 00:33:46 [main] INFO  c.i.provider.YahooFinanceScraper - Read HTML content from file: C:\Users\<USER>\Downloads\yahoo-finance-html\yahoo_finance_AACIU_1748536426.html
2025-05-30 00:33:46 [main] INFO  c.i.provider.YahooFinanceScraper - Extracted 6 OHLCV data points for AACIU
2025-05-30 00:33:46 [main] INFO  c.i.database.DatabaseManager - Saved 6 OHLCV data points for AACIU
2025-05-30 00:33:46 [main] INFO  c.i.provider.YahooFinanceProvider - Downloaded and saved 6 data points for AACIU
2025-05-30 00:33:46 [main] INFO  c.investment.MarketDataDownloaderApp - ✓ Successfully processed AACIU
2025-05-30 00:33:47 [main] INFO  c.investment.MarketDataDownloaderApp - Processing instrument 10/10009: AACT (Ares Acquisition Corp II)
2025-05-30 00:33:47 [main] INFO  c.investment.MarketDataDownloaderApp - Downloading data for AACT from 1963-05-30 to 2025-05-30 (attempt 1/4)
2025-05-30 00:33:47 [main] INFO  c.i.provider.YahooFinanceProvider - Requesting data from Yahoo Finance for symbol: AACT, from 1963-05-30 to 2025-05-30
2025-05-30 00:33:47 [main] INFO  c.i.provider.YahooFinanceScraper - Scraping data from Yahoo Finance: https://finance.yahoo.com/quote/AACT/history/?period1=-*********&period2=**********&interval=1d
2025-05-30 00:33:48 [main] INFO  c.i.provider.YahooFinanceScraper - Saved HTML content to file: C:\Users\<USER>\Downloads\yahoo-finance-html\yahoo_finance_AACT_1748536428.html
2025-05-30 00:33:48 [main] INFO  c.i.provider.YahooFinanceScraper - Read HTML content from file: C:\Users\<USER>\Downloads\yahoo-finance-html\yahoo_finance_AACT_1748536428.html
2025-05-30 00:33:48 [main] INFO  c.i.provider.YahooFinanceScraper - Extracted 481 OHLCV data points for AACT
2025-05-30 00:33:48 [main] INFO  c.i.database.DatabaseManager - Saved 481 OHLCV data points for AACT
2025-05-30 00:33:48 [main] INFO  c.i.provider.YahooFinanceProvider - Downloaded and saved 481 data points for AACT
2025-05-30 00:33:48 [main] INFO  c.investment.MarketDataDownloaderApp - ✓ Successfully processed AACT
2025-05-30 00:33:49 [main] INFO  c.investment.MarketDataDownloaderApp - Processing instrument 11/10009: AACT-UN (Ares Acquisition Corp II)
2025-05-30 00:33:49 [main] INFO  c.investment.MarketDataDownloaderApp - Downloading data for AACT-UN from 1963-05-30 to 2025-05-30 (attempt 1/4)
2025-05-30 00:33:49 [main] INFO  c.i.provider.YahooFinanceProvider - Requesting data from Yahoo Finance for symbol: AACT-UN, from 1963-05-30 to 2025-05-30
2025-05-30 00:33:49 [main] INFO  c.i.provider.YahooFinanceScraper - Scraping data from Yahoo Finance: https://finance.yahoo.com/quote/AACT-UN/history/?period1=-*********&period2=**********&interval=1d
2025-05-30 00:33:50 [main] INFO  c.i.provider.YahooFinanceScraper - Saved HTML content to file: C:\Users\<USER>\Downloads\yahoo-finance-html\yahoo_finance_AACT-UN_1748536430.html
2025-05-30 00:33:50 [main] INFO  c.i.provider.YahooFinanceScraper - Read HTML content from file: C:\Users\<USER>\Downloads\yahoo-finance-html\yahoo_finance_AACT-UN_1748536430.html
2025-05-30 00:33:50 [main] INFO  c.i.provider.YahooFinanceScraper - Extracted 166 OHLCV data points for AACT-UN
2025-05-30 00:33:50 [main] INFO  c.i.database.DatabaseManager - Saved 166 OHLCV data points for AACT-UN
2025-05-30 00:33:50 [main] INFO  c.i.provider.YahooFinanceProvider - Downloaded and saved 166 data points for AACT-UN
2025-05-30 00:33:50 [main] INFO  c.investment.MarketDataDownloaderApp - ✓ Successfully processed AACT-UN
2025-05-30 00:33:50 [main] INFO  c.investment.MarketDataDownloaderApp - Processing instrument 12/10009: AACT-WT (Ares Acquisition Corp II)
2025-05-30 00:33:50 [main] INFO  c.investment.MarketDataDownloaderApp - Downloading data for AACT-WT from 1963-05-30 to 2025-05-30 (attempt 1/4)
2025-05-30 00:33:50 [main] INFO  c.i.provider.YahooFinanceProvider - Requesting data from Yahoo Finance for symbol: AACT-WT, from 1963-05-30 to 2025-05-30
2025-05-30 00:33:50 [main] INFO  c.i.provider.YahooFinanceScraper - Scraping data from Yahoo Finance: https://finance.yahoo.com/quote/AACT-WT/history/?period1=-*********&period2=**********&interval=1d
2025-05-30 00:33:51 [main] INFO  c.i.provider.YahooFinanceScraper - Saved HTML content to file: C:\Users\<USER>\Downloads\yahoo-finance-html\yahoo_finance_AACT-WT_1748536431.html
2025-05-30 00:33:51 [main] INFO  c.i.provider.YahooFinanceScraper - Read HTML content from file: C:\Users\<USER>\Downloads\yahoo-finance-html\yahoo_finance_AACT-WT_1748536431.html
2025-05-30 00:33:51 [main] INFO  c.i.provider.YahooFinanceScraper - Extracted 1 OHLCV data points for AACT-WT
2025-05-30 00:33:51 [main] INFO  c.i.database.DatabaseManager - Saved 1 OHLCV data points for AACT-WT
2025-05-30 00:33:51 [main] INFO  c.i.provider.YahooFinanceProvider - Downloaded and saved 1 data points for AACT-WT
2025-05-30 00:33:51 [main] INFO  c.investment.MarketDataDownloaderApp - ✓ Successfully processed AACT-WT
2025-05-30 00:33:52 [main] INFO  c.investment.MarketDataDownloaderApp - Processing instrument 13/10009: AAGH (America Great Health)
2025-05-30 00:33:52 [main] INFO  c.investment.MarketDataDownloaderApp - Downloading data for AAGH from 1963-05-30 to 2025-05-30 (attempt 1/4)
2025-05-30 00:33:52 [main] INFO  c.i.provider.YahooFinanceProvider - Requesting data from Yahoo Finance for symbol: AAGH, from 1963-05-30 to 2025-05-30
2025-05-30 00:33:52 [main] INFO  c.i.provider.YahooFinanceScraper - Scraping data from Yahoo Finance: https://finance.yahoo.com/quote/AAGH/history/?period1=-*********&period2=**********&interval=1d
2025-05-30 00:33:55 [main] INFO  c.i.provider.YahooFinanceScraper - Saved HTML content to file: C:\Users\<USER>\Downloads\yahoo-finance-html\yahoo_finance_AAGH_1748536435.html
2025-05-30 00:33:55 [main] INFO  c.i.provider.YahooFinanceScraper - Read HTML content from file: C:\Users\<USER>\Downloads\yahoo-finance-html\yahoo_finance_AAGH_1748536435.html
2025-05-30 00:33:55 [main] INFO  c.i.provider.YahooFinanceScraper - Extracted 2068 OHLCV data points for AAGH
2025-05-30 00:33:55 [main] INFO  c.i.database.DatabaseManager - Saved 2068 OHLCV data points for AAGH
2025-05-30 00:33:55 [main] INFO  c.i.provider.YahooFinanceProvider - Downloaded and saved 2068 data points for AAGH
2025-05-30 00:33:55 [main] INFO  c.investment.MarketDataDownloaderApp - ✓ Successfully processed AAGH
2025-05-30 00:33:56 [main] INFO  c.investment.MarketDataDownloaderApp - Processing instrument 14/10009: AAL (American Airlines Group Inc.)
2025-05-30 00:33:56 [main] INFO  c.investment.MarketDataDownloaderApp - Downloading data for AAL from 1963-05-30 to 2025-05-30 (attempt 1/4)
2025-05-30 00:33:56 [main] INFO  c.i.provider.YahooFinanceProvider - Requesting data from Yahoo Finance for symbol: AAL, from 1963-05-30 to 2025-05-30
2025-05-30 00:33:56 [main] INFO  c.i.provider.YahooFinanceScraper - Scraping data from Yahoo Finance: https://finance.yahoo.com/quote/AAL/history/?period1=-*********&period2=**********&interval=1d
2025-05-30 00:33:58 [main] INFO  c.i.provider.YahooFinanceScraper - Saved HTML content to file: C:\Users\<USER>\Downloads\yahoo-finance-html\yahoo_finance_AAL_1748536438.html
2025-05-30 00:33:58 [main] INFO  c.i.provider.YahooFinanceScraper - Read HTML content from file: C:\Users\<USER>\Downloads\yahoo-finance-html\yahoo_finance_AAL_1748536438.html
2025-05-30 00:33:58 [main] INFO  c.i.provider.YahooFinanceScraper - Extracted 4949 OHLCV data points for AAL
2025-05-30 00:34:00 [main] INFO  c.i.database.DatabaseManager - Saved 4949 OHLCV data points for AAL
2025-05-30 00:34:00 [main] INFO  c.i.provider.YahooFinanceProvider - Downloaded and saved 4949 data points for AAL
2025-05-30 00:34:00 [main] INFO  c.investment.MarketDataDownloaderApp - ✓ Successfully processed AAL
2025-05-30 00:34:00 [main] INFO  c.investment.MarketDataDownloaderApp - Processing instrument 15/10009: AAM (AA Mission Acquisition Corp.)
2025-05-30 00:34:00 [main] INFO  c.investment.MarketDataDownloaderApp - Downloading data for AAM from 1963-05-30 to 2025-05-30 (attempt 1/4)
2025-05-30 00:34:00 [main] INFO  c.i.provider.YahooFinanceProvider - Requesting data from Yahoo Finance for symbol: AAM, from 1963-05-30 to 2025-05-30
2025-05-30 00:34:00 [main] INFO  c.i.provider.YahooFinanceScraper - Scraping data from Yahoo Finance: https://finance.yahoo.com/quote/AAM/history/?period1=-*********&period2=**********&interval=1d
2025-05-30 00:34:02 [main] INFO  c.i.provider.YahooFinanceScraper - Saved HTML content to file: C:\Users\<USER>\Downloads\yahoo-finance-html\yahoo_finance_AAM_1748536442.html
2025-05-30 00:34:02 [main] INFO  c.i.provider.YahooFinanceScraper - Read HTML content from file: C:\Users\<USER>\Downloads\yahoo-finance-html\yahoo_finance_AAM_1748536442.html
2025-05-30 00:34:02 [main] INFO  c.i.provider.YahooFinanceScraper - Extracted 170 OHLCV data points for AAM
2025-05-30 00:34:02 [main] INFO  c.i.database.DatabaseManager - Saved 170 OHLCV data points for AAM
2025-05-30 00:34:02 [main] INFO  c.i.provider.YahooFinanceProvider - Downloaded and saved 170 data points for AAM
2025-05-30 00:34:02 [main] INFO  c.investment.MarketDataDownloaderApp - ✓ Successfully processed AAM
2025-05-30 00:34:02 [main] INFO  c.investment.MarketDataDownloaderApp - Processing instrument 16/10009: AAM-UN (AA Mission Acquisition Corp.)
2025-05-30 00:34:02 [main] INFO  c.investment.MarketDataDownloaderApp - Downloading data for AAM-UN from 1963-05-30 to 2025-05-30 (attempt 1/4)
2025-05-30 00:34:02 [main] INFO  c.i.provider.YahooFinanceProvider - Requesting data from Yahoo Finance for symbol: AAM-UN, from 1963-05-30 to 2025-05-30
2025-05-30 00:34:02 [main] INFO  c.i.provider.YahooFinanceScraper - Scraping data from Yahoo Finance: https://finance.yahoo.com/quote/AAM-UN/history/?period1=-*********&period2=**********&interval=1d
2025-05-30 00:34:04 [main] INFO  c.i.provider.YahooFinanceScraper - Saved HTML content to file: C:\Users\<USER>\Downloads\yahoo-finance-html\yahoo_finance_AAM-UN_1748536444.html
2025-05-30 00:34:04 [main] INFO  c.i.provider.YahooFinanceScraper - Read HTML content from file: C:\Users\<USER>\Downloads\yahoo-finance-html\yahoo_finance_AAM-UN_1748536444.html
2025-05-30 00:34:04 [main] INFO  c.i.provider.YahooFinanceScraper - Extracted 87 OHLCV data points for AAM-UN
2025-05-30 00:34:04 [main] INFO  c.i.database.DatabaseManager - Saved 87 OHLCV data points for AAM-UN
2025-05-30 00:34:04 [main] INFO  c.i.provider.YahooFinanceProvider - Downloaded and saved 87 data points for AAM-UN
2025-05-30 00:34:04 [main] INFO  c.investment.MarketDataDownloaderApp - ✓ Successfully processed AAM-UN
2025-05-30 00:34:05 [main] INFO  c.investment.MarketDataDownloaderApp - Processing instrument 17/10009: AAM-WT (AA Mission Acquisition Corp.)
2025-05-30 00:34:05 [main] INFO  c.investment.MarketDataDownloaderApp - Downloading data for AAM-WT from 1963-05-30 to 2025-05-30 (attempt 1/4)
2025-05-30 00:34:05 [main] INFO  c.i.provider.YahooFinanceProvider - Requesting data from Yahoo Finance for symbol: AAM-WT, from 1963-05-30 to 2025-05-30
2025-05-30 00:34:05 [main] INFO  c.i.provider.YahooFinanceScraper - Scraping data from Yahoo Finance: https://finance.yahoo.com/quote/AAM-WT/history/?period1=-*********&period2=**********&interval=1d
2025-05-30 00:34:06 [main] INFO  c.i.provider.YahooFinanceScraper - Saved HTML content to file: C:\Users\<USER>\Downloads\yahoo-finance-html\yahoo_finance_AAM-WT_1748536446.html
2025-05-30 00:34:06 [main] INFO  c.i.provider.YahooFinanceScraper - Read HTML content from file: C:\Users\<USER>\Downloads\yahoo-finance-html\yahoo_finance_AAM-WT_1748536446.html
2025-05-30 00:34:06 [main] INFO  c.i.provider.YahooFinanceScraper - Extracted 1 OHLCV data points for AAM-WT
2025-05-30 00:34:06 [main] INFO  c.i.database.DatabaseManager - Saved 1 OHLCV data points for AAM-WT
2025-05-30 00:34:06 [main] INFO  c.i.provider.YahooFinanceProvider - Downloaded and saved 1 data points for AAM-WT
2025-05-30 00:34:06 [main] INFO  c.investment.MarketDataDownloaderApp - ✓ Successfully processed AAM-WT
2025-05-30 00:34:07 [main] INFO  c.investment.MarketDataDownloaderApp - Processing instrument 18/10009: AAME (ATLANTIC AMERICAN CORP)
2025-05-30 00:34:07 [main] INFO  c.investment.MarketDataDownloaderApp - Downloading data for AAME from 1963-05-30 to 2025-05-30 (attempt 1/4)
2025-05-30 00:34:07 [main] INFO  c.i.provider.YahooFinanceProvider - Requesting data from Yahoo Finance for symbol: AAME, from 1963-05-30 to 2025-05-30
2025-05-30 00:34:07 [main] INFO  c.i.provider.YahooFinanceScraper - Scraping data from Yahoo Finance: https://finance.yahoo.com/quote/AAME/history/?period1=-*********&period2=**********&interval=1d
2025-05-30 00:34:15 [main] INFO  c.i.provider.YahooFinanceScraper - Saved HTML content to file: C:\Users\<USER>\Downloads\yahoo-finance-html\yahoo_finance_AAME_1748536455.html
2025-05-30 00:34:15 [main] INFO  c.i.provider.YahooFinanceScraper - Read HTML content from file: C:\Users\<USER>\Downloads\yahoo-finance-html\yahoo_finance_AAME_1748536455.html
2025-05-30 00:34:15 [main] INFO  c.i.provider.YahooFinanceScraper - Extracted 10446 OHLCV data points for AAME
2025-05-30 00:34:18 [main] INFO  c.i.database.DatabaseManager - Saved 10446 OHLCV data points for AAME
2025-05-30 00:34:18 [main] INFO  c.i.provider.YahooFinanceProvider - Downloaded and saved 10446 data points for AAME
2025-05-30 00:34:18 [main] INFO  c.investment.MarketDataDownloaderApp - ✓ Successfully processed AAME
2025-05-30 00:34:18 [main] INFO  c.investment.MarketDataDownloaderApp - Processing instrument 19/10009: AAMI (Acadian Asset Management Inc.)
2025-05-30 00:34:18 [main] INFO  c.investment.MarketDataDownloaderApp - Downloading data for AAMI from 1963-05-30 to 2025-05-30 (attempt 1/4)
2025-05-30 00:34:18 [main] INFO  c.i.provider.YahooFinanceProvider - Requesting data from Yahoo Finance for symbol: AAMI, from 1963-05-30 to 2025-05-30
2025-05-30 00:34:18 [main] INFO  c.i.provider.YahooFinanceScraper - Scraping data from Yahoo Finance: https://finance.yahoo.com/quote/AAMI/history/?period1=-*********&period2=**********&interval=1d
2025-05-30 00:34:22 [main] INFO  c.i.provider.YahooFinanceScraper - Saved HTML content to file: C:\Users\<USER>\Downloads\yahoo-finance-html\yahoo_finance_AAMI_1748536462.html
2025-05-30 00:34:22 [main] INFO  c.i.provider.YahooFinanceScraper - Read HTML content from file: C:\Users\<USER>\Downloads\yahoo-finance-html\yahoo_finance_AAMI_1748536462.html
2025-05-30 00:34:22 [main] INFO  c.i.provider.YahooFinanceScraper - Extracted 2675 OHLCV data points for AAMI
2025-05-30 00:34:23 [main] INFO  c.i.database.DatabaseManager - Saved 2675 OHLCV data points for AAMI
2025-05-30 00:34:23 [main] INFO  c.i.provider.YahooFinanceProvider - Downloaded and saved 2675 data points for AAMI
2025-05-30 00:34:23 [main] INFO  c.investment.MarketDataDownloaderApp - ✓ Successfully processed AAMI
2025-05-30 00:34:23 [main] INFO  c.investment.MarketDataDownloaderApp - Processing instrument 20/10009: AAMTF (Armada Mercantile Ltd)
2025-05-30 00:34:23 [main] INFO  c.investment.MarketDataDownloaderApp - Downloading data for AAMTF from 1963-05-30 to 2025-05-30 (attempt 1/4)
2025-05-30 00:34:23 [main] INFO  c.i.provider.YahooFinanceProvider - Requesting data from Yahoo Finance for symbol: AAMTF, from 1963-05-30 to 2025-05-30
2025-05-30 00:34:23 [main] INFO  c.i.provider.YahooFinanceScraper - Scraping data from Yahoo Finance: https://finance.yahoo.com/quote/AAMTF/history/?period1=-*********&period2=**********&interval=1d
2025-05-30 00:34:27 [main] INFO  c.i.provider.YahooFinanceScraper - Saved HTML content to file: C:\Users\<USER>\Downloads\yahoo-finance-html\yahoo_finance_AAMTF_1748536467.html
2025-05-30 00:34:27 [main] INFO  c.i.provider.YahooFinanceScraper - Read HTML content from file: C:\Users\<USER>\Downloads\yahoo-finance-html\yahoo_finance_AAMTF_1748536467.html
2025-05-30 00:34:27 [main] INFO  c.i.provider.YahooFinanceScraper - Extracted 953 OHLCV data points for AAMTF
2025-05-30 00:34:27 [main] INFO  c.i.database.DatabaseManager - Saved 953 OHLCV data points for AAMTF
2025-05-30 00:34:27 [main] INFO  c.i.provider.YahooFinanceProvider - Downloaded and saved 953 data points for AAMTF
2025-05-30 00:34:27 [main] INFO  c.investment.MarketDataDownloaderApp - ✓ Successfully processed AAMTF
2025-05-30 00:34:28 [main] INFO  c.investment.MarketDataDownloaderApp - Processing instrument 21/10009: AAOI (APPLIED OPTOELECTRONICS, INC.)
2025-05-30 00:34:28 [main] INFO  c.investment.MarketDataDownloaderApp - Downloading data for AAOI from 1963-05-30 to 2025-05-30 (attempt 1/4)
2025-05-30 00:34:28 [main] INFO  c.i.provider.YahooFinanceProvider - Requesting data from Yahoo Finance for symbol: AAOI, from 1963-05-30 to 2025-05-30
2025-05-30 00:34:28 [main] INFO  c.i.provider.YahooFinanceScraper - Scraping data from Yahoo Finance: https://finance.yahoo.com/quote/AAOI/history/?period1=-*********&period2=**********&interval=1d
2025-05-30 00:34:31 [main] INFO  c.i.provider.YahooFinanceScraper - Saved HTML content to file: C:\Users\<USER>\Downloads\yahoo-finance-html\yahoo_finance_AAOI_1748536471.html
2025-05-30 00:34:31 [main] INFO  c.i.provider.YahooFinanceScraper - Read HTML content from file: C:\Users\<USER>\Downloads\yahoo-finance-html\yahoo_finance_AAOI_1748536471.html
2025-05-30 00:34:31 [main] INFO  c.i.provider.YahooFinanceScraper - Extracted 2936 OHLCV data points for AAOI
2025-05-30 00:34:31 [main] INFO  c.i.database.DatabaseManager - Saved 2936 OHLCV data points for AAOI
2025-05-30 00:34:31 [main] INFO  c.i.provider.YahooFinanceProvider - Downloaded and saved 2936 data points for AAOI
2025-05-30 00:34:31 [main] INFO  c.investment.MarketDataDownloaderApp - ✓ Successfully processed AAOI
2025-05-30 00:34:32 [main] INFO  c.investment.MarketDataDownloaderApp - Processing instrument 22/10009: AAON (AAON, INC.)
2025-05-30 00:34:32 [main] INFO  c.investment.MarketDataDownloaderApp - Downloading data for AAON from 1963-05-30 to 2025-05-30 (attempt 1/4)
2025-05-30 00:34:32 [main] INFO  c.i.provider.YahooFinanceProvider - Requesting data from Yahoo Finance for symbol: AAON, from 1963-05-30 to 2025-05-30
2025-05-30 00:34:32 [main] INFO  c.i.provider.YahooFinanceScraper - Scraping data from Yahoo Finance: https://finance.yahoo.com/quote/AAON/history/?period1=-*********&period2=**********&interval=1d
2025-05-30 00:34:37 [main] INFO  c.i.provider.YahooFinanceScraper - Saved HTML content to file: C:\Users\<USER>\Downloads\yahoo-finance-html\yahoo_finance_AAON_1748536477.html
2025-05-30 00:34:37 [main] INFO  c.i.provider.YahooFinanceScraper - Read HTML content from file: C:\Users\<USER>\Downloads\yahoo-finance-html\yahoo_finance_AAON_1748536477.html
2025-05-30 00:34:37 [main] INFO  c.i.provider.YahooFinanceScraper - Extracted 8156 OHLCV data points for AAON
2025-05-30 00:34:39 [main] INFO  c.i.database.DatabaseManager - Saved 8156 OHLCV data points for AAON
2025-05-30 00:34:39 [main] INFO  c.i.provider.YahooFinanceProvider - Downloaded and saved 8156 data points for AAON
2025-05-30 00:34:39 [main] INFO  c.investment.MarketDataDownloaderApp - ✓ Successfully processed AAON
2025-05-30 00:34:39 [main] INFO  c.investment.MarketDataDownloaderApp - Processing instrument 23/10009: AAP (ADVANCE AUTO PARTS INC)
2025-05-30 00:34:39 [main] INFO  c.investment.MarketDataDownloaderApp - Downloading data for AAP from 1963-05-30 to 2025-05-30 (attempt 1/4)
2025-05-30 00:34:39 [main] INFO  c.i.provider.YahooFinanceProvider - Requesting data from Yahoo Finance for symbol: AAP, from 1963-05-30 to 2025-05-30
2025-05-30 00:34:39 [main] INFO  c.i.provider.YahooFinanceScraper - Scraping data from Yahoo Finance: https://finance.yahoo.com/quote/AAP/history/?period1=-*********&period2=**********&interval=1d
2025-05-30 00:34:43 [main] INFO  c.i.provider.YahooFinanceScraper - Saved HTML content to file: C:\Users\<USER>\Downloads\yahoo-finance-html\yahoo_finance_AAP_1748536483.html
2025-05-30 00:34:43 [main] INFO  c.i.provider.YahooFinanceScraper - Read HTML content from file: C:\Users\<USER>\Downloads\yahoo-finance-html\yahoo_finance_AAP_1748536483.html
2025-05-30 00:34:43 [main] INFO  c.i.provider.YahooFinanceScraper - Extracted 5912 OHLCV data points for AAP
2025-05-30 00:34:45 [main] INFO  c.i.database.DatabaseManager - Saved 5912 OHLCV data points for AAP
2025-05-30 00:34:45 [main] INFO  c.i.provider.YahooFinanceProvider - Downloaded and saved 5912 data points for AAP
2025-05-30 00:34:45 [main] INFO  c.investment.MarketDataDownloaderApp - ✓ Successfully processed AAP
2025-05-30 00:34:45 [main] INFO  c.investment.MarketDataDownloaderApp - Processing instrument 24/10009: AAPG (ASCENTAGE PHARMA GROUP INTERNATIONAL)
2025-05-30 00:34:45 [main] INFO  c.investment.MarketDataDownloaderApp - Downloading data for AAPG from 1963-05-30 to 2025-05-30 (attempt 1/4)
2025-05-30 00:34:45 [main] INFO  c.i.provider.YahooFinanceProvider - Requesting data from Yahoo Finance for symbol: AAPG, from 1963-05-30 to 2025-05-30
2025-05-30 00:34:45 [main] INFO  c.i.provider.YahooFinanceScraper - Scraping data from Yahoo Finance: https://finance.yahoo.com/quote/AAPG/history/?period1=-*********&period2=**********&interval=1d
2025-05-30 00:34:46 [main] INFO  c.i.provider.YahooFinanceScraper - Saved HTML content to file: C:\Users\<USER>\Downloads\yahoo-finance-html\yahoo_finance_AAPG_1748536486.html
2025-05-30 00:34:46 [main] INFO  c.i.provider.YahooFinanceScraper - Read HTML content from file: C:\Users\<USER>\Downloads\yahoo-finance-html\yahoo_finance_AAPG_1748536486.html
2025-05-30 00:34:46 [main] INFO  c.i.provider.YahooFinanceScraper - Extracted 87 OHLCV data points for AAPG
2025-05-30 00:34:46 [main] INFO  c.i.database.DatabaseManager - Saved 87 OHLCV data points for AAPG
2025-05-30 00:34:46 [main] INFO  c.i.provider.YahooFinanceProvider - Downloaded and saved 87 data points for AAPG
2025-05-30 00:34:46 [main] INFO  c.investment.MarketDataDownloaderApp - ✓ Successfully processed AAPG
2025-05-30 00:34:47 [main] INFO  c.investment.MarketDataDownloaderApp - Processing instrument 25/10009: AAPI (Apple iSports Group, Inc.)
2025-05-30 00:34:47 [main] INFO  c.investment.MarketDataDownloaderApp - Downloading data for AAPI from 1963-05-30 to 2025-05-30 (attempt 1/4)
2025-05-30 00:34:47 [main] INFO  c.i.provider.YahooFinanceProvider - Requesting data from Yahoo Finance for symbol: AAPI, from 1963-05-30 to 2025-05-30
2025-05-30 00:34:47 [main] INFO  c.i.provider.YahooFinanceScraper - Scraping data from Yahoo Finance: https://finance.yahoo.com/quote/AAPI/history/?period1=-*********&period2=**********&interval=1d
2025-05-30 00:34:50 [main] INFO  c.i.provider.YahooFinanceScraper - Saved HTML content to file: C:\Users\<USER>\Downloads\yahoo-finance-html\yahoo_finance_AAPI_1748536490.html
2025-05-30 00:34:50 [main] INFO  c.i.provider.YahooFinanceScraper - Read HTML content from file: C:\Users\<USER>\Downloads\yahoo-finance-html\yahoo_finance_AAPI_1748536490.html
2025-05-30 00:34:50 [main] INFO  c.i.provider.YahooFinanceScraper - Extracted 981 OHLCV data points for AAPI
2025-05-30 00:34:50 [main] INFO  c.i.database.DatabaseManager - Saved 981 OHLCV data points for AAPI
2025-05-30 00:34:50 [main] INFO  c.i.provider.YahooFinanceProvider - Downloaded and saved 981 data points for AAPI
2025-05-30 00:34:50 [main] INFO  c.investment.MarketDataDownloaderApp - ✓ Successfully processed AAPI
2025-05-30 00:34:51 [main] INFO  c.investment.MarketDataDownloaderApp - Processing instrument 26/10009: AAPL (Apple Inc.)
2025-05-30 00:34:51 [main] INFO  c.investment.MarketDataDownloaderApp - Downloading data for AAPL from 2025-05-22 to 2025-05-30 (attempt 1/4)
2025-05-30 00:34:51 [main] INFO  c.i.provider.YahooFinanceProvider - Requesting data from Yahoo Finance for symbol: AAPL, from 2025-05-22 to 2025-05-30
2025-05-30 00:34:51 [main] INFO  c.i.provider.YahooFinanceScraper - Scraping data from Yahoo Finance: https://finance.yahoo.com/quote/AAPL/history/?period1=**********&period2=**********&interval=1d
2025-05-30 00:34:51 [main] INFO  c.i.provider.YahooFinanceScraper - Saved HTML content to file: C:\Users\<USER>\Downloads\yahoo-finance-html\yahoo_finance_AAPL_1748536491.html
2025-05-30 00:34:51 [main] INFO  c.i.provider.YahooFinanceScraper - Read HTML content from file: C:\Users\<USER>\Downloads\yahoo-finance-html\yahoo_finance_AAPL_1748536491.html
2025-05-30 00:34:51 [main] INFO  c.i.provider.YahooFinanceScraper - Extracted 5 OHLCV data points for AAPL
2025-05-30 00:34:51 [main] INFO  c.i.database.DatabaseManager - Saved 5 OHLCV data points for AAPL
2025-05-30 00:34:51 [main] INFO  c.i.provider.YahooFinanceProvider - Downloaded and saved 5 data points for AAPL
2025-05-30 00:34:51 [main] INFO  c.investment.MarketDataDownloaderApp - ✓ Successfully processed AAPL
2025-05-30 00:34:52 [main] INFO  c.investment.MarketDataDownloaderApp - Processing instrument 27/10009: AAQL (Antiaging Quantum Living Inc.)
2025-05-30 00:34:52 [main] INFO  c.investment.MarketDataDownloaderApp - Downloading data for AAQL from 1963-05-30 to 2025-05-30 (attempt 1/4)
2025-05-30 00:34:52 [main] INFO  c.i.provider.YahooFinanceProvider - Requesting data from Yahoo Finance for symbol: AAQL, from 1963-05-30 to 2025-05-30
2025-05-30 00:34:52 [main] INFO  c.i.provider.YahooFinanceScraper - Scraping data from Yahoo Finance: https://finance.yahoo.com/quote/AAQL/history/?period1=-*********&period2=**********&interval=1d
2025-05-30 00:34:54 [main] INFO  c.i.provider.YahooFinanceScraper - Saved HTML content to file: C:\Users\<USER>\Downloads\yahoo-finance-html\yahoo_finance_AAQL_1748536494.html
2025-05-30 00:34:54 [main] INFO  c.i.provider.YahooFinanceScraper - Read HTML content from file: C:\Users\<USER>\Downloads\yahoo-finance-html\yahoo_finance_AAQL_1748536494.html
2025-05-30 00:34:54 [main] INFO  c.i.provider.YahooFinanceScraper - Extracted 137 OHLCV data points for AAQL
2025-05-30 00:34:54 [main] INFO  c.i.database.DatabaseManager - Saved 137 OHLCV data points for AAQL
2025-05-30 00:34:54 [main] INFO  c.i.provider.YahooFinanceProvider - Downloaded and saved 137 data points for AAQL
2025-05-30 00:34:54 [main] INFO  c.investment.MarketDataDownloaderApp - ✓ Successfully processed AAQL
2025-05-30 00:34:54 [main] INFO  c.investment.MarketDataDownloaderApp - Processing instrument 28/10009: AARD (Aardvark Therapeutics, Inc.)
2025-05-30 00:34:54 [main] INFO  c.investment.MarketDataDownloaderApp - Downloading data for AARD from 1963-05-30 to 2025-05-30 (attempt 1/4)
2025-05-30 00:34:54 [main] INFO  c.i.provider.YahooFinanceProvider - Requesting data from Yahoo Finance for symbol: AARD, from 1963-05-30 to 2025-05-30
2025-05-30 00:34:54 [main] INFO  c.i.provider.YahooFinanceScraper - Scraping data from Yahoo Finance: https://finance.yahoo.com/quote/AARD/history/?period1=-*********&period2=**********&interval=1d
2025-05-30 00:34:56 [main] INFO  c.i.provider.YahooFinanceScraper - Saved HTML content to file: C:\Users\<USER>\Downloads\yahoo-finance-html\yahoo_finance_AARD_1748536496.html
2025-05-30 00:34:56 [main] INFO  c.i.provider.YahooFinanceScraper - Read HTML content from file: C:\Users\<USER>\Downloads\yahoo-finance-html\yahoo_finance_AARD_1748536496.html
2025-05-30 00:34:56 [main] INFO  c.i.provider.YahooFinanceScraper - Extracted 73 OHLCV data points for AARD
2025-05-30 00:34:56 [main] INFO  c.i.database.DatabaseManager - Saved 73 OHLCV data points for AARD
2025-05-30 00:34:56 [main] INFO  c.i.provider.YahooFinanceProvider - Downloaded and saved 73 data points for AARD
2025-05-30 00:34:56 [main] INFO  c.investment.MarketDataDownloaderApp - ✓ Successfully processed AARD
2025-05-30 00:34:56 [main] INFO  c.investment.MarketDataDownloaderApp - Processing instrument 29/10009: AAS (Antharas Inc)
2025-05-30 00:34:56 [main] INFO  c.investment.MarketDataDownloaderApp - Downloading data for AAS from 1963-05-30 to 2025-05-30 (attempt 1/4)
2025-05-30 00:34:56 [main] INFO  c.i.provider.YahooFinanceProvider - Requesting data from Yahoo Finance for symbol: AAS, from 1963-05-30 to 2025-05-30
2025-05-30 00:34:56 [main] INFO  c.i.provider.YahooFinanceScraper - Scraping data from Yahoo Finance: https://finance.yahoo.com/quote/AAS/history/?period1=-*********&period2=**********&interval=1d
2025-05-30 00:34:57 [main] INFO  c.i.provider.YahooFinanceScraper - Saved HTML content to file: C:\Users\<USER>\Downloads\yahoo-finance-html\yahoo_finance_AAS_1748536497.html
2025-05-30 00:34:57 [main] INFO  c.i.provider.YahooFinanceScraper - Read HTML content from file: C:\Users\<USER>\Downloads\yahoo-finance-html\yahoo_finance_AAS_1748536497.html
2025-05-30 00:34:57 [main] INFO  c.i.provider.YahooFinanceScraper - Extracted 0 OHLCV data points for AAS
2025-05-30 00:34:57 [main] INFO  c.i.provider.YahooFinanceProvider - Downloaded and saved 0 data points for AAS
2025-05-30 00:34:57 [main] INFO  c.investment.MarketDataDownloaderApp - ✓ Successfully processed AAS
2025-05-30 00:34:57 [main] INFO  c.investment.MarketDataDownloaderApp - Processing instrument 30/10009: AASP (Agassi Sports Entertainment Corp.)
2025-05-30 00:34:57 [main] INFO  c.investment.MarketDataDownloaderApp - Downloading data for AASP from 1963-05-30 to 2025-05-30 (attempt 1/4)
2025-05-30 00:34:57 [main] INFO  c.i.provider.YahooFinanceProvider - Requesting data from Yahoo Finance for symbol: AASP, from 1963-05-30 to 2025-05-30
2025-05-30 00:34:57 [main] INFO  c.i.provider.YahooFinanceScraper - Scraping data from Yahoo Finance: https://finance.yahoo.com/quote/AASP/history/?period1=-*********&period2=**********&interval=1d
2025-05-30 00:35:02 [main] INFO  c.i.provider.YahooFinanceScraper - Saved HTML content to file: C:\Users\<USER>\Downloads\yahoo-finance-html\yahoo_finance_AASP_1748536502.html
2025-05-30 00:35:02 [main] INFO  c.i.provider.YahooFinanceScraper - Read HTML content from file: C:\Users\<USER>\Downloads\yahoo-finance-html\yahoo_finance_AASP_1748536502.html
2025-05-30 00:35:02 [main] INFO  c.i.provider.YahooFinanceScraper - Extracted 2131 OHLCV data points for AASP
2025-05-30 00:35:03 [main] INFO  c.i.database.DatabaseManager - Saved 2131 OHLCV data points for AASP
2025-05-30 00:35:03 [main] INFO  c.i.provider.YahooFinanceProvider - Downloaded and saved 2131 data points for AASP
2025-05-30 00:35:03 [main] INFO  c.investment.MarketDataDownloaderApp - ✓ Successfully processed AASP
2025-05-30 00:35:03 [main] INFO  c.investment.MarketDataDownloaderApp - Processing instrument 31/10009: AAT (American Assets Trust, Inc.)
2025-05-30 00:35:03 [main] INFO  c.investment.MarketDataDownloaderApp - Downloading data for AAT from 1963-05-30 to 2025-05-30 (attempt 1/4)
2025-05-30 00:35:03 [main] INFO  c.i.provider.YahooFinanceProvider - Requesting data from Yahoo Finance for symbol: AAT, from 1963-05-30 to 2025-05-30
2025-05-30 00:35:03 [main] INFO  c.i.provider.YahooFinanceScraper - Scraping data from Yahoo Finance: https://finance.yahoo.com/quote/AAT/history/?period1=-*********&period2=**********&interval=1d
2025-05-30 00:35:06 [main] INFO  c.i.provider.YahooFinanceScraper - Saved HTML content to file: C:\Users\<USER>\Downloads\yahoo-finance-html\yahoo_finance_AAT_1748536506.html
2025-05-30 00:35:06 [main] INFO  c.i.provider.YahooFinanceScraper - Read HTML content from file: C:\Users\<USER>\Downloads\yahoo-finance-html\yahoo_finance_AAT_1748536506.html
2025-05-30 00:35:06 [main] INFO  c.i.provider.YahooFinanceScraper - Extracted 3615 OHLCV data points for AAT
2025-05-30 00:35:07 [main] INFO  c.i.database.DatabaseManager - Saved 3615 OHLCV data points for AAT
2025-05-30 00:35:07 [main] INFO  c.i.provider.YahooFinanceProvider - Downloaded and saved 3615 data points for AAT
2025-05-30 00:35:07 [main] INFO  c.investment.MarketDataDownloaderApp - ✓ Successfully processed AAT
2025-05-30 00:35:08 [main] INFO  c.investment.MarketDataDownloaderApp - Processing instrument 32/10009: AATC (AUTOSCOPE TECHNOLOGIES CORP)
2025-05-30 00:35:08 [main] INFO  c.investment.MarketDataDownloaderApp - Downloading data for AATC from 1963-05-30 to 2025-05-30 (attempt 1/4)
2025-05-30 00:35:08 [main] INFO  c.i.provider.YahooFinanceProvider - Requesting data from Yahoo Finance for symbol: AATC, from 1963-05-30 to 2025-05-30
2025-05-30 00:35:08 [main] INFO  c.i.provider.YahooFinanceScraper - Scraping data from Yahoo Finance: https://finance.yahoo.com/quote/AATC/history/?period1=-*********&period2=**********&interval=1d
2025-05-30 00:35:13 [main] INFO  c.i.provider.YahooFinanceScraper - Saved HTML content to file: C:\Users\<USER>\Downloads\yahoo-finance-html\yahoo_finance_AATC_1748536513.html
2025-05-30 00:35:13 [main] INFO  c.i.provider.YahooFinanceScraper - Read HTML content from file: C:\Users\<USER>\Downloads\yahoo-finance-html\yahoo_finance_AATC_1748536513.html
2025-05-30 00:35:13 [main] INFO  c.i.provider.YahooFinanceScraper - Extracted 6957 OHLCV data points for AATC
2025-05-30 00:35:15 [main] INFO  c.i.database.DatabaseManager - Saved 6957 OHLCV data points for AATC
2025-05-30 00:35:15 [main] INFO  c.i.provider.YahooFinanceProvider - Downloaded and saved 6957 data points for AATC
2025-05-30 00:35:15 [main] INFO  c.investment.MarketDataDownloaderApp - ✓ Successfully processed AATC
2025-05-30 00:35:15 [main] INFO  c.investment.MarketDataDownloaderApp - Processing instrument 33/10009: AAUAF (ALMADEN MINERALS LTD)
2025-05-30 00:35:15 [main] INFO  c.investment.MarketDataDownloaderApp - Downloading data for AAUAF from 1963-05-30 to 2025-05-30 (attempt 1/4)
2025-05-30 00:35:15 [main] INFO  c.i.provider.YahooFinanceProvider - Requesting data from Yahoo Finance for symbol: AAUAF, from 1963-05-30 to 2025-05-30
2025-05-30 00:35:15 [main] INFO  c.i.provider.YahooFinanceScraper - Scraping data from Yahoo Finance: https://finance.yahoo.com/quote/AAUAF/history/?period1=-*********&period2=**********&interval=1d
2025-05-30 00:35:19 [main] INFO  c.i.provider.YahooFinanceScraper - Saved HTML content to file: C:\Users\<USER>\Downloads\yahoo-finance-html\yahoo_finance_AAUAF_1748536519.html
2025-05-30 00:35:19 [main] INFO  c.i.provider.YahooFinanceScraper - Read HTML content from file: C:\Users\<USER>\Downloads\yahoo-finance-html\yahoo_finance_AAUAF_1748536519.html
2025-05-30 00:35:19 [main] INFO  c.i.provider.YahooFinanceScraper - Extracted 4890 OHLCV data points for AAUAF
2025-05-30 00:35:21 [main] INFO  c.i.database.DatabaseManager - Saved 4890 OHLCV data points for AAUAF
2025-05-30 00:35:21 [main] INFO  c.i.provider.YahooFinanceProvider - Downloaded and saved 4890 data points for AAUAF
2025-05-30 00:35:21 [main] INFO  c.investment.MarketDataDownloaderApp - ✓ Successfully processed AAUAF
2025-05-30 00:35:21 [main] INFO  c.investment.MarketDataDownloaderApp - Processing instrument 34/10009: AAUCD (Allied Gold Corp)
2025-05-30 00:35:21 [main] INFO  c.investment.MarketDataDownloaderApp - Downloading data for AAUCD from 1963-05-30 to 2025-05-30 (attempt 1/4)
2025-05-30 00:35:21 [main] INFO  c.i.provider.YahooFinanceProvider - Requesting data from Yahoo Finance for symbol: AAUCD, from 1963-05-30 to 2025-05-30
2025-05-30 00:35:21 [main] INFO  c.i.provider.YahooFinanceScraper - Scraping data from Yahoo Finance: https://finance.yahoo.com/quote/AAUCD/history/?period1=-*********&period2=**********&interval=1d
2025-05-30 00:35:23 [main] INFO  c.i.provider.YahooFinanceScraper - Saved HTML content to file: C:\Users\<USER>\Downloads\yahoo-finance-html\yahoo_finance_AAUCD_1748536523.html
2025-05-30 00:35:23 [main] INFO  c.i.provider.YahooFinanceScraper - Read HTML content from file: C:\Users\<USER>\Downloads\yahoo-finance-html\yahoo_finance_AAUCD_1748536523.html
2025-05-30 00:35:23 [main] INFO  c.i.provider.YahooFinanceScraper - Extracted 194 OHLCV data points for AAUCD
2025-05-30 00:35:23 [main] INFO  c.i.database.DatabaseManager - Saved 194 OHLCV data points for AAUCD
2025-05-30 00:35:23 [main] INFO  c.i.provider.YahooFinanceProvider - Downloaded and saved 194 data points for AAUCD
2025-05-30 00:35:23 [main] INFO  c.investment.MarketDataDownloaderApp - ✓ Successfully processed AAUCD
2025-05-30 00:35:24 [main] INFO  c.investment.MarketDataDownloaderApp - Processing instrument 35/10009: AAUGF (AERO ENERGY Ltd)
2025-05-30 00:35:24 [main] INFO  c.investment.MarketDataDownloaderApp - Downloading data for AAUGF from 1963-05-30 to 2025-05-30 (attempt 1/4)
2025-05-30 00:35:24 [main] INFO  c.i.provider.YahooFinanceProvider - Requesting data from Yahoo Finance for symbol: AAUGF, from 1963-05-30 to 2025-05-30
2025-05-30 00:35:24 [main] INFO  c.i.provider.YahooFinanceScraper - Scraping data from Yahoo Finance: https://finance.yahoo.com/quote/AAUGF/history/?period1=-*********&period2=**********&interval=1d
2025-05-30 00:35:26 [main] INFO  c.i.provider.YahooFinanceScraper - Saved HTML content to file: C:\Users\<USER>\Downloads\yahoo-finance-html\yahoo_finance_AAUGF_1748536526.html
2025-05-30 00:35:26 [main] INFO  c.i.provider.YahooFinanceScraper - Read HTML content from file: C:\Users\<USER>\Downloads\yahoo-finance-html\yahoo_finance_AAUGF_1748536526.html
2025-05-30 00:35:26 [main] INFO  c.i.provider.YahooFinanceScraper - Extracted 505 OHLCV data points for AAUGF
2025-05-30 00:35:26 [main] INFO  c.i.database.DatabaseManager - Saved 505 OHLCV data points for AAUGF
2025-05-30 00:35:26 [main] INFO  c.i.provider.YahooFinanceProvider - Downloaded and saved 505 data points for AAUGF
2025-05-30 00:35:26 [main] INFO  c.investment.MarketDataDownloaderApp - ✓ Successfully processed AAUGF
2025-05-30 00:35:26 [main] INFO  c.investment.MarketDataDownloaderApp - Processing instrument 36/10009: AAVXF (Abivax S.A.)
2025-05-30 00:35:26 [main] INFO  c.investment.MarketDataDownloaderApp - Downloading data for AAVXF from 1963-05-30 to 2025-05-30 (attempt 1/4)
2025-05-30 00:35:26 [main] INFO  c.i.provider.YahooFinanceProvider - Requesting data from Yahoo Finance for symbol: AAVXF, from 1963-05-30 to 2025-05-30
2025-05-30 00:35:26 [main] INFO  c.i.provider.YahooFinanceScraper - Scraping data from Yahoo Finance: https://finance.yahoo.com/quote/AAVXF/history/?period1=-*********&period2=**********&interval=1d
2025-05-30 00:35:29 [main] INFO  c.i.provider.YahooFinanceScraper - Saved HTML content to file: C:\Users\<USER>\Downloads\yahoo-finance-html\yahoo_finance_AAVXF_1748536529.html
2025-05-30 00:35:29 [main] INFO  c.i.provider.YahooFinanceScraper - Read HTML content from file: C:\Users\<USER>\Downloads\yahoo-finance-html\yahoo_finance_AAVXF_1748536529.html
2025-05-30 00:35:29 [main] INFO  c.i.provider.YahooFinanceScraper - Extracted 508 OHLCV data points for AAVXF
2025-05-30 00:35:29 [main] INFO  c.i.database.DatabaseManager - Saved 508 OHLCV data points for AAVXF
2025-05-30 00:35:29 [main] INFO  c.i.provider.YahooFinanceProvider - Downloaded and saved 508 data points for AAVXF
2025-05-30 00:35:29 [main] INFO  c.investment.MarketDataDownloaderApp - ✓ Successfully processed AAVXF
2025-05-30 00:35:29 [main] INFO  c.investment.MarketDataDownloaderApp - Processing instrument 37/10009: AAWH (Ascend Wellness Holdings, Inc.)
2025-05-30 00:35:29 [main] INFO  c.investment.MarketDataDownloaderApp - Downloading data for AAWH from 1963-05-30 to 2025-05-30 (attempt 1/4)
2025-05-30 00:35:29 [main] INFO  c.i.provider.YahooFinanceProvider - Requesting data from Yahoo Finance for symbol: AAWH, from 1963-05-30 to 2025-05-30
2025-05-30 00:35:29 [main] INFO  c.i.provider.YahooFinanceScraper - Scraping data from Yahoo Finance: https://finance.yahoo.com/quote/AAWH/history/?period1=-*********&period2=**********&interval=1d
2025-05-30 00:35:31 [main] INFO  c.i.provider.YahooFinanceScraper - Saved HTML content to file: C:\Users\<USER>\Downloads\yahoo-finance-html\yahoo_finance_AAWH_1748536531.html
2025-05-30 00:35:31 [main] INFO  c.i.provider.YahooFinanceScraper - Read HTML content from file: C:\Users\<USER>\Downloads\yahoo-finance-html\yahoo_finance_AAWH_1748536531.html
2025-05-30 00:35:31 [main] INFO  c.i.provider.YahooFinanceScraper - Extracted 1007 OHLCV data points for AAWH
2025-05-30 00:35:32 [main] INFO  c.i.database.DatabaseManager - Saved 1007 OHLCV data points for AAWH
2025-05-30 00:35:32 [main] INFO  c.i.provider.YahooFinanceProvider - Downloaded and saved 1007 data points for AAWH
2025-05-30 00:35:32 [main] INFO  c.investment.MarketDataDownloaderApp - ✓ Successfully processed AAWH
2025-05-30 00:35:32 [main] INFO  c.investment.MarketDataDownloaderApp - Processing instrument 38/10009: AB (ALLIANCEBERNSTEIN HOLDING L.P.)
2025-05-30 00:35:32 [main] INFO  c.investment.MarketDataDownloaderApp - Downloading data for AB from 1963-05-30 to 2025-05-30 (attempt 1/4)
2025-05-30 00:35:32 [main] INFO  c.i.provider.YahooFinanceProvider - Requesting data from Yahoo Finance for symbol: AB, from 1963-05-30 to 2025-05-30
2025-05-30 00:35:32 [main] INFO  c.i.provider.YahooFinanceScraper - Scraping data from Yahoo Finance: https://finance.yahoo.com/quote/AB/history/?period1=-*********&period2=**********&interval=1d
2025-05-30 00:35:40 [main] INFO  c.i.provider.YahooFinanceScraper - Saved HTML content to file: C:\Users\<USER>\Downloads\yahoo-finance-html\yahoo_finance_AB_1748536540.html
2025-05-30 00:35:40 [main] INFO  c.i.provider.YahooFinanceScraper - Read HTML content from file: C:\Users\<USER>\Downloads\yahoo-finance-html\yahoo_finance_AB_1748536540.html
2025-05-30 00:35:40 [main] INFO  c.i.provider.YahooFinanceScraper - Extracted 9352 OHLCV data points for AB
2025-05-30 00:35:43 [main] INFO  c.i.database.DatabaseManager - Saved 9352 OHLCV data points for AB
2025-05-30 00:35:43 [main] INFO  c.i.provider.YahooFinanceProvider - Downloaded and saved 9352 data points for AB
2025-05-30 00:35:43 [main] INFO  c.investment.MarketDataDownloaderApp - ✓ Successfully processed AB
2025-05-30 00:35:43 [main] INFO  c.investment.MarketDataDownloaderApp - Processing instrument 39/10009: ABAKF (ABRDN ASIA-PACIFIC INCOME FUND, INC.)
2025-05-30 00:35:43 [main] INFO  c.investment.MarketDataDownloaderApp - Downloading data for ABAKF from 1963-05-30 to 2025-05-30 (attempt 1/4)
2025-05-30 00:35:43 [main] INFO  c.i.provider.YahooFinanceProvider - Requesting data from Yahoo Finance for symbol: ABAKF, from 1963-05-30 to 2025-05-30
2025-05-30 00:35:43 [main] INFO  c.i.provider.YahooFinanceScraper - Scraping data from Yahoo Finance: https://finance.yahoo.com/quote/ABAKF/history/?period1=-*********&period2=**********&interval=1d
2025-05-30 00:35:46 [main] INFO  c.i.provider.YahooFinanceScraper - Saved HTML content to file: C:\Users\<USER>\Downloads\yahoo-finance-html\yahoo_finance_ABAKF_1748536546.html
2025-05-30 00:35:46 [main] INFO  c.i.provider.YahooFinanceScraper - Read HTML content from file: C:\Users\<USER>\Downloads\yahoo-finance-html\yahoo_finance_ABAKF_1748536546.html
2025-05-30 00:35:46 [main] INFO  c.i.provider.YahooFinanceScraper - Extracted 276 OHLCV data points for ABAKF
2025-05-30 00:35:46 [main] INFO  c.i.database.DatabaseManager - Saved 276 OHLCV data points for ABAKF
2025-05-30 00:35:46 [main] INFO  c.i.provider.YahooFinanceProvider - Downloaded and saved 276 data points for ABAKF
2025-05-30 00:35:46 [main] INFO  c.investment.MarketDataDownloaderApp - ✓ Successfully processed ABAKF
2025-05-30 00:35:46 [main] INFO  c.investment.MarketDataDownloaderApp - Processing instrument 40/10009: ABAT (AMERICAN BATTERY TECHNOLOGY Co)
2025-05-30 00:35:46 [main] INFO  c.investment.MarketDataDownloaderApp - Downloading data for ABAT from 1963-05-30 to 2025-05-30 (attempt 1/4)
2025-05-30 00:35:46 [main] INFO  c.i.provider.YahooFinanceProvider - Requesting data from Yahoo Finance for symbol: ABAT, from 1963-05-30 to 2025-05-30
2025-05-30 00:35:46 [main] INFO  c.i.provider.YahooFinanceScraper - Scraping data from Yahoo Finance: https://finance.yahoo.com/quote/ABAT/history/?period1=-*********&period2=**********&interval=1d
2025-05-30 00:35:49 [main] INFO  c.i.provider.YahooFinanceScraper - Saved HTML content to file: C:\Users\<USER>\Downloads\yahoo-finance-html\yahoo_finance_ABAT_1748536549.html
2025-05-30 00:35:49 [main] INFO  c.i.provider.YahooFinanceScraper - Read HTML content from file: C:\Users\<USER>\Downloads\yahoo-finance-html\yahoo_finance_ABAT_1748536549.html
2025-05-30 00:35:49 [main] INFO  c.i.provider.YahooFinanceScraper - Extracted 2265 OHLCV data points for ABAT
2025-05-30 00:35:49 [main] INFO  c.i.database.DatabaseManager - Saved 2265 OHLCV data points for ABAT
2025-05-30 00:35:49 [main] INFO  c.i.provider.YahooFinanceProvider - Downloaded and saved 2265 data points for ABAT
2025-05-30 00:35:49 [main] INFO  c.investment.MarketDataDownloaderApp - ✓ Successfully processed ABAT
2025-05-30 00:35:50 [main] INFO  c.investment.MarketDataDownloaderApp - Processing instrument 41/10009: ABBNY (ABB LTD)
2025-05-30 00:35:50 [main] INFO  c.investment.MarketDataDownloaderApp - Downloading data for ABBNY from 1963-05-30 to 2025-05-30 (attempt 1/4)
2025-05-30 00:35:50 [main] INFO  c.i.provider.YahooFinanceProvider - Requesting data from Yahoo Finance for symbol: ABBNY, from 1963-05-30 to 2025-05-30
2025-05-30 00:35:50 [main] INFO  c.i.provider.YahooFinanceScraper - Scraping data from Yahoo Finance: https://finance.yahoo.com/quote/ABBNY/history/?period1=-*********&period2=**********&interval=1d
2025-05-30 00:35:54 [main] INFO  c.i.provider.YahooFinanceScraper - Saved HTML content to file: C:\Users\<USER>\Downloads\yahoo-finance-html\yahoo_finance_ABBNY_1748536554.html
2025-05-30 00:35:54 [main] INFO  c.i.provider.YahooFinanceScraper - Read HTML content from file: C:\Users\<USER>\Downloads\yahoo-finance-html\yahoo_finance_ABBNY_1748536554.html
2025-05-30 00:35:54 [main] INFO  c.i.provider.YahooFinanceScraper - Extracted 6072 OHLCV data points for ABBNY
2025-05-30 00:35:55 [main] INFO  c.i.database.DatabaseManager - Saved 6072 OHLCV data points for ABBNY
2025-05-30 00:35:55 [main] INFO  c.i.provider.YahooFinanceProvider - Downloaded and saved 6072 data points for ABBNY
2025-05-30 00:35:55 [main] INFO  c.investment.MarketDataDownloaderApp - ✓ Successfully processed ABBNY
2025-05-30 00:35:56 [main] INFO  c.investment.MarketDataDownloaderApp - Processing instrument 42/10009: ABBV (AbbVie Inc.)
2025-05-30 00:35:56 [main] INFO  c.investment.MarketDataDownloaderApp - Downloading data for ABBV from 1963-05-30 to 2025-05-30 (attempt 1/4)
2025-05-30 00:35:56 [main] INFO  c.i.provider.YahooFinanceProvider - Requesting data from Yahoo Finance for symbol: ABBV, from 1963-05-30 to 2025-05-30
2025-05-30 00:35:56 [main] INFO  c.i.provider.YahooFinanceScraper - Scraping data from Yahoo Finance: https://finance.yahoo.com/quote/ABBV/history/?period1=-*********&period2=**********&interval=1d
2025-05-30 00:35:59 [main] INFO  c.i.provider.YahooFinanceScraper - Saved HTML content to file: C:\Users\<USER>\Downloads\yahoo-finance-html\yahoo_finance_ABBV_1748536559.html
2025-05-30 00:35:59 [main] INFO  c.i.provider.YahooFinanceScraper - Read HTML content from file: C:\Users\<USER>\Downloads\yahoo-finance-html\yahoo_finance_ABBV_1748536559.html
2025-05-30 00:35:59 [main] INFO  c.i.provider.YahooFinanceScraper - Extracted 3121 OHLCV data points for ABBV
2025-05-30 00:35:59 [main] INFO  c.i.database.DatabaseManager - Saved 3121 OHLCV data points for ABBV
2025-05-30 00:35:59 [main] INFO  c.i.provider.YahooFinanceProvider - Downloaded and saved 3121 data points for ABBV
2025-05-30 00:35:59 [main] INFO  c.investment.MarketDataDownloaderApp - ✓ Successfully processed ABBV
2025-05-30 00:36:00 [main] INFO  c.investment.MarketDataDownloaderApp - Processing instrument 43/10009: ABCB (Ameris Bancorp)
2025-05-30 00:36:00 [main] INFO  c.investment.MarketDataDownloaderApp - Downloading data for ABCB from 1963-05-30 to 2025-05-30 (attempt 1/4)
2025-05-30 00:36:00 [main] INFO  c.i.provider.YahooFinanceProvider - Requesting data from Yahoo Finance for symbol: ABCB, from 1963-05-30 to 2025-05-30
2025-05-30 00:36:00 [main] INFO  c.i.provider.YahooFinanceScraper - Scraping data from Yahoo Finance: https://finance.yahoo.com/quote/ABCB/history/?period1=-*********&period2=**********&interval=1d
2025-05-30 00:36:08 [main] INFO  c.i.provider.YahooFinanceScraper - Saved HTML content to file: C:\Users\<USER>\Downloads\yahoo-finance-html\yahoo_finance_ABCB_1748536568.html
2025-05-30 00:36:08 [main] INFO  c.i.provider.YahooFinanceScraper - Read HTML content from file: C:\Users\<USER>\Downloads\yahoo-finance-html\yahoo_finance_ABCB_1748536568.html
2025-05-30 00:36:08 [main] INFO  c.i.provider.YahooFinanceScraper - Extracted 7615 OHLCV data points for ABCB
2025-05-30 00:36:10 [main] INFO  c.i.database.DatabaseManager - Saved 7615 OHLCV data points for ABCB
2025-05-30 00:36:10 [main] INFO  c.i.provider.YahooFinanceProvider - Downloaded and saved 7615 data points for ABCB
2025-05-30 00:36:10 [main] INFO  c.investment.MarketDataDownloaderApp - ✓ Successfully processed ABCB
2025-05-30 00:36:10 [main] INFO  c.investment.MarketDataDownloaderApp - Processing instrument 44/10009: ABCFF (ABACUS MINING & EXPLORATION CORP)
2025-05-30 00:36:10 [main] INFO  c.investment.MarketDataDownloaderApp - Downloading data for ABCFF from 1963-05-30 to 2025-05-30 (attempt 1/4)
2025-05-30 00:36:10 [main] INFO  c.i.provider.YahooFinanceProvider - Requesting data from Yahoo Finance for symbol: ABCFF, from 1963-05-30 to 2025-05-30
2025-05-30 00:36:10 [main] INFO  c.i.provider.YahooFinanceScraper - Scraping data from Yahoo Finance: https://finance.yahoo.com/quote/ABCFF/history/?period1=-*********&period2=**********&interval=1d
2025-05-30 00:36:14 [main] INFO  c.i.provider.YahooFinanceScraper - Saved HTML content to file: C:\Users\<USER>\Downloads\yahoo-finance-html\yahoo_finance_ABCFF_1748536574.html
2025-05-30 00:36:14 [main] INFO  c.i.provider.YahooFinanceScraper - Read HTML content from file: C:\Users\<USER>\Downloads\yahoo-finance-html\yahoo_finance_ABCFF_1748536574.html
2025-05-30 00:36:14 [main] INFO  c.i.provider.YahooFinanceScraper - Extracted 2728 OHLCV data points for ABCFF
2025-05-30 00:36:14 [main] INFO  c.i.database.DatabaseManager - Saved 2728 OHLCV data points for ABCFF
2025-05-30 00:36:14 [main] INFO  c.i.provider.YahooFinanceProvider - Downloaded and saved 2728 data points for ABCFF
2025-05-30 00:36:14 [main] INFO  c.investment.MarketDataDownloaderApp - ✓ Successfully processed ABCFF
2025-05-30 00:36:15 [main] INFO  c.investment.MarketDataDownloaderApp - Processing instrument 45/10009: ABCL (AbCellera Biologics Inc.)
2025-05-30 00:36:15 [main] INFO  c.investment.MarketDataDownloaderApp - Downloading data for ABCL from 1963-05-30 to 2025-05-30 (attempt 1/4)
2025-05-30 00:36:15 [main] INFO  c.i.provider.YahooFinanceProvider - Requesting data from Yahoo Finance for symbol: ABCL, from 1963-05-30 to 2025-05-30
2025-05-30 00:36:15 [main] INFO  c.i.provider.YahooFinanceScraper - Scraping data from Yahoo Finance: https://finance.yahoo.com/quote/ABCL/history/?period1=-*********&period2=**********&interval=1d
2025-05-30 00:36:17 [main] INFO  c.i.provider.YahooFinanceScraper - Saved HTML content to file: C:\Users\<USER>\Downloads\yahoo-finance-html\yahoo_finance_ABCL_1748536577.html
2025-05-30 00:36:17 [main] INFO  c.i.provider.YahooFinanceScraper - Read HTML content from file: C:\Users\<USER>\Downloads\yahoo-finance-html\yahoo_finance_ABCL_1748536577.html
2025-05-30 00:36:17 [main] INFO  c.i.provider.YahooFinanceScraper - Extracted 1120 OHLCV data points for ABCL
2025-05-30 00:36:17 [main] INFO  c.i.database.DatabaseManager - Saved 1120 OHLCV data points for ABCL
2025-05-30 00:36:17 [main] INFO  c.i.provider.YahooFinanceProvider - Downloaded and saved 1120 data points for ABCL
2025-05-30 00:36:17 [main] INFO  c.investment.MarketDataDownloaderApp - ✓ Successfully processed ABCL
2025-05-30 00:36:18 [main] INFO  c.investment.MarketDataDownloaderApp - Processing instrument 46/10009: ABCP (AmBase Corp)
2025-05-30 00:36:18 [main] INFO  c.investment.MarketDataDownloaderApp - Downloading data for ABCP from 1963-05-30 to 2025-05-30 (attempt 1/4)
2025-05-30 00:36:18 [main] INFO  c.i.provider.YahooFinanceProvider - Requesting data from Yahoo Finance for symbol: ABCP, from 1963-05-30 to 2025-05-30
2025-05-30 00:36:18 [main] INFO  c.i.provider.YahooFinanceScraper - Scraping data from Yahoo Finance: https://finance.yahoo.com/quote/ABCP/history/?period1=-*********&period2=**********&interval=1d
2025-05-30 00:36:24 [main] INFO  c.i.provider.YahooFinanceScraper - Saved HTML content to file: C:\Users\<USER>\Downloads\yahoo-finance-html\yahoo_finance_ABCP_1748536584.html
2025-05-30 00:36:24 [main] INFO  c.i.provider.YahooFinanceScraper - Read HTML content from file: C:\Users\<USER>\Downloads\yahoo-finance-html\yahoo_finance_ABCP_1748536584.html
2025-05-30 00:36:24 [main] INFO  c.i.provider.YahooFinanceScraper - Extracted 8414 OHLCV data points for ABCP
2025-05-30 00:36:26 [main] INFO  c.i.database.DatabaseManager - Saved 8414 OHLCV data points for ABCP
2025-05-30 00:36:26 [main] INFO  c.i.provider.YahooFinanceProvider - Downloaded and saved 8414 data points for ABCP
2025-05-30 00:36:26 [main] INFO  c.investment.MarketDataDownloaderApp - ✓ Successfully processed ABCP
2025-05-30 00:36:26 [main] INFO  c.investment.MarketDataDownloaderApp - Processing instrument 47/10009: ABEO (ABEONA THERAPEUTICS INC.)
2025-05-30 00:36:26 [main] INFO  c.investment.MarketDataDownloaderApp - Downloading data for ABEO from 1963-05-30 to 2025-05-30 (attempt 1/4)
2025-05-30 00:36:26 [main] INFO  c.i.provider.YahooFinanceProvider - Requesting data from Yahoo Finance for symbol: ABEO, from 1963-05-30 to 2025-05-30
2025-05-30 00:36:26 [main] INFO  c.i.provider.YahooFinanceScraper - Scraping data from Yahoo Finance: https://finance.yahoo.com/quote/ABEO/history/?period1=-*********&period2=**********&interval=1d
2025-05-30 00:36:32 [main] INFO  c.i.provider.YahooFinanceScraper - Saved HTML content to file: C:\Users\<USER>\Downloads\yahoo-finance-html\yahoo_finance_ABEO_1748536592.html
2025-05-30 00:36:32 [main] INFO  c.i.provider.YahooFinanceScraper - Read HTML content from file: C:\Users\<USER>\Downloads\yahoo-finance-html\yahoo_finance_ABEO_1748536592.html
2025-05-30 00:36:32 [main] INFO  c.i.provider.YahooFinanceScraper - Extracted 6701 OHLCV data points for ABEO
2025-05-30 00:36:34 [main] INFO  c.i.database.DatabaseManager - Saved 6701 OHLCV data points for ABEO
2025-05-30 00:36:34 [main] INFO  c.i.provider.YahooFinanceProvider - Downloaded and saved 6701 data points for ABEO
2025-05-30 00:36:34 [main] INFO  c.investment.MarketDataDownloaderApp - ✓ Successfully processed ABEO
2025-05-30 00:36:35 [main] INFO  c.investment.MarketDataDownloaderApp - Processing instrument 48/10009: ABEV (AMBEV S.A.)
2025-05-30 00:36:35 [main] INFO  c.investment.MarketDataDownloaderApp - Downloading data for ABEV from 1963-05-30 to 2025-05-30 (attempt 1/4)
2025-05-30 00:36:35 [main] INFO  c.i.provider.YahooFinanceProvider - Requesting data from Yahoo Finance for symbol: ABEV, from 1963-05-30 to 2025-05-30
2025-05-30 00:36:35 [main] INFO  c.i.provider.YahooFinanceScraper - Scraping data from Yahoo Finance: https://finance.yahoo.com/quote/ABEV/history/?period1=-*********&period2=**********&interval=1d
2025-05-30 00:36:40 [main] INFO  c.i.provider.YahooFinanceScraper - Saved HTML content to file: C:\Users\<USER>\Downloads\yahoo-finance-html\yahoo_finance_ABEV_1748536600.html
2025-05-30 00:36:40 [main] INFO  c.i.provider.YahooFinanceScraper - Read HTML content from file: C:\Users\<USER>\Downloads\yahoo-finance-html\yahoo_finance_ABEV_1748536600.html
2025-05-30 00:36:40 [main] INFO  c.i.provider.YahooFinanceScraper - Extracted 5456 OHLCV data points for ABEV
2025-05-30 00:36:42 [main] INFO  c.i.database.DatabaseManager - Saved 5456 OHLCV data points for ABEV
2025-05-30 00:36:42 [main] INFO  c.i.provider.YahooFinanceProvider - Downloaded and saved 5456 data points for ABEV
2025-05-30 00:36:42 [main] INFO  c.investment.MarketDataDownloaderApp - ✓ Successfully processed ABEV
2025-05-30 00:36:42 [main] INFO  c.investment.MarketDataDownloaderApp - Processing instrument 49/10009: ABG (ASBURY AUTOMOTIVE GROUP INC)
2025-05-30 00:36:42 [main] INFO  c.investment.MarketDataDownloaderApp - Downloading data for ABG from 1963-05-30 to 2025-05-30 (attempt 1/4)
2025-05-30 00:36:42 [main] INFO  c.i.provider.YahooFinanceProvider - Requesting data from Yahoo Finance for symbol: ABG, from 1963-05-30 to 2025-05-30
2025-05-30 00:36:42 [main] INFO  c.i.provider.YahooFinanceScraper - Scraping data from Yahoo Finance: https://finance.yahoo.com/quote/ABG/history/?period1=-*********&period2=**********&interval=1d
2025-05-30 00:36:47 [main] INFO  c.i.provider.YahooFinanceScraper - Saved HTML content to file: C:\Users\<USER>\Downloads\yahoo-finance-html\yahoo_finance_ABG_1748536607.html
2025-05-30 00:36:47 [main] INFO  c.i.provider.YahooFinanceScraper - Read HTML content from file: C:\Users\<USER>\Downloads\yahoo-finance-html\yahoo_finance_ABG_1748536607.html
2025-05-30 00:36:47 [main] INFO  c.i.provider.YahooFinanceScraper - Extracted 5836 OHLCV data points for ABG
2025-05-30 00:36:49 [main] INFO  c.i.database.DatabaseManager - Saved 5836 OHLCV data points for ABG
2025-05-30 00:36:49 [main] INFO  c.i.provider.YahooFinanceProvider - Downloaded and saved 5836 data points for ABG
2025-05-30 00:36:49 [main] INFO  c.investment.MarketDataDownloaderApp - ✓ Successfully processed ABG
2025-05-30 00:36:49 [main] INFO  c.investment.MarketDataDownloaderApp - Processing instrument 50/10009: ABIT (Athena Bitcoin Global)
2025-05-30 00:36:49 [main] INFO  c.investment.MarketDataDownloaderApp - Downloading data for ABIT from 1963-05-30 to 2025-05-30 (attempt 1/4)
2025-05-30 00:36:49 [main] INFO  c.i.provider.YahooFinanceProvider - Requesting data from Yahoo Finance for symbol: ABIT, from 1963-05-30 to 2025-05-30
2025-05-30 00:36:49 [main] INFO  c.i.provider.YahooFinanceScraper - Scraping data from Yahoo Finance: https://finance.yahoo.com/quote/ABIT/history/?period1=-*********&period2=**********&interval=1d
2025-05-30 00:36:52 [main] INFO  c.i.provider.YahooFinanceScraper - Saved HTML content to file: C:\Users\<USER>\Downloads\yahoo-finance-html\yahoo_finance_ABIT_1748536612.html
2025-05-30 00:36:52 [main] INFO  c.i.provider.YahooFinanceScraper - Read HTML content from file: C:\Users\<USER>\Downloads\yahoo-finance-html\yahoo_finance_ABIT_1748536612.html
2025-05-30 00:36:52 [main] INFO  c.i.provider.YahooFinanceScraper - Extracted 1075 OHLCV data points for ABIT
2025-05-30 00:36:52 [main] INFO  c.i.database.DatabaseManager - Saved 1075 OHLCV data points for ABIT
2025-05-30 00:36:52 [main] INFO  c.i.provider.YahooFinanceProvider - Downloaded and saved 1075 data points for ABIT
2025-05-30 00:36:52 [main] INFO  c.investment.MarketDataDownloaderApp - ✓ Successfully processed ABIT
2025-05-30 00:36:52 [main] INFO  c.investment.MarketDataDownloaderApp - Processing instrument 51/10009: ABL (Abacus Global Management, Inc.)
2025-05-30 00:36:52 [main] INFO  c.investment.MarketDataDownloaderApp - Downloading data for ABL from 1963-05-30 to 2025-05-30 (attempt 1/4)
2025-05-30 00:36:52 [main] INFO  c.i.provider.YahooFinanceProvider - Requesting data from Yahoo Finance for symbol: ABL, from 1963-05-30 to 2025-05-30
2025-05-30 00:36:52 [main] INFO  c.i.provider.YahooFinanceScraper - Scraping data from Yahoo Finance: https://finance.yahoo.com/quote/ABL/history/?period1=-*********&period2=**********&interval=1d
2025-05-30 00:36:55 [main] INFO  c.i.provider.YahooFinanceScraper - Saved HTML content to file: C:\Users\<USER>\Downloads\yahoo-finance-html\yahoo_finance_ABL_1748536615.html
2025-05-30 00:36:55 [main] INFO  c.i.provider.YahooFinanceScraper - Read HTML content from file: C:\Users\<USER>\Downloads\yahoo-finance-html\yahoo_finance_ABL_1748536615.html
2025-05-30 00:36:55 [main] INFO  c.i.provider.YahooFinanceScraper - Extracted 1133 OHLCV data points for ABL
2025-05-30 00:36:55 [main] INFO  c.i.database.DatabaseManager - Saved 1133 OHLCV data points for ABL
2025-05-30 00:36:55 [main] INFO  c.i.provider.YahooFinanceProvider - Downloaded and saved 1133 data points for ABL
2025-05-30 00:36:55 [main] INFO  c.investment.MarketDataDownloaderApp - ✓ Successfully processed ABL
2025-05-30 00:36:56 [main] INFO  c.investment.MarketDataDownloaderApp - Processing instrument 52/10009: ABLLL (Abacus Global Management, Inc.)
2025-05-30 00:36:56 [main] INFO  c.investment.MarketDataDownloaderApp - Downloading data for ABLLL from 1963-05-30 to 2025-05-30 (attempt 1/4)
2025-05-30 00:36:56 [main] INFO  c.i.provider.YahooFinanceProvider - Requesting data from Yahoo Finance for symbol: ABLLL, from 1963-05-30 to 2025-05-30
2025-05-30 00:36:56 [main] INFO  c.i.provider.YahooFinanceScraper - Scraping data from Yahoo Finance: https://finance.yahoo.com/quote/ABLLL/history/?period1=-*********&period2=**********&interval=1d
2025-05-30 00:36:57 [main] INFO  c.i.provider.YahooFinanceScraper - Saved HTML content to file: C:\Users\<USER>\Downloads\yahoo-finance-html\yahoo_finance_ABLLL_1748536617.html
2025-05-30 00:36:57 [main] INFO  c.i.provider.YahooFinanceScraper - Read HTML content from file: C:\Users\<USER>\Downloads\yahoo-finance-html\yahoo_finance_ABLLL_1748536617.html
2025-05-30 00:36:57 [main] INFO  c.i.provider.YahooFinanceScraper - Extracted 367 OHLCV data points for ABLLL
2025-05-30 00:36:57 [main] INFO  c.i.database.DatabaseManager - Saved 367 OHLCV data points for ABLLL
2025-05-30 00:36:57 [main] INFO  c.i.provider.YahooFinanceProvider - Downloaded and saved 367 data points for ABLLL
2025-05-30 00:36:57 [main] INFO  c.investment.MarketDataDownloaderApp - ✓ Successfully processed ABLLL
2025-05-30 00:36:58 [main] INFO  c.investment.MarketDataDownloaderApp - Processing instrument 53/10009: ABLLW (Abacus Global Management, Inc.)
2025-05-30 00:36:58 [main] INFO  c.investment.MarketDataDownloaderApp - Downloading data for ABLLW from 1963-05-30 to 2025-05-30 (attempt 1/4)
2025-05-30 00:36:58 [main] INFO  c.i.provider.YahooFinanceProvider - Requesting data from Yahoo Finance for symbol: ABLLW, from 1963-05-30 to 2025-05-30
2025-05-30 00:36:58 [main] INFO  c.i.provider.YahooFinanceScraper - Scraping data from Yahoo Finance: https://finance.yahoo.com/quote/ABLLW/history/?period1=-*********&period2=**********&interval=1d
2025-05-30 00:37:00 [main] INFO  c.i.provider.YahooFinanceScraper - Saved HTML content to file: C:\Users\<USER>\Downloads\yahoo-finance-html\yahoo_finance_ABLLW_1748536620.html
2025-05-30 00:37:00 [main] INFO  c.i.provider.YahooFinanceScraper - Read HTML content from file: C:\Users\<USER>\Downloads\yahoo-finance-html\yahoo_finance_ABLLW_1748536620.html
2025-05-30 00:37:00 [main] INFO  c.i.provider.YahooFinanceScraper - Extracted 1 OHLCV data points for ABLLW
2025-05-30 00:37:00 [main] INFO  c.i.database.DatabaseManager - Saved 1 OHLCV data points for ABLLW
2025-05-30 00:37:00 [main] INFO  c.i.provider.YahooFinanceProvider - Downloaded and saved 1 data points for ABLLW
2025-05-30 00:37:00 [main] INFO  c.investment.MarketDataDownloaderApp - ✓ Successfully processed ABLLW
2025-05-30 00:37:00 [main] INFO  c.investment.MarketDataDownloaderApp - Processing instrument 54/10009: ABLV (Able View Global Inc.)
2025-05-30 00:37:00 [main] INFO  c.investment.MarketDataDownloaderApp - Downloading data for ABLV from 1963-05-30 to 2025-05-30 (attempt 1/4)
2025-05-30 00:37:00 [main] INFO  c.i.provider.YahooFinanceProvider - Requesting data from Yahoo Finance for symbol: ABLV, from 1963-05-30 to 2025-05-30
2025-05-30 00:37:00 [main] INFO  c.i.provider.YahooFinanceScraper - Scraping data from Yahoo Finance: https://finance.yahoo.com/quote/ABLV/history/?period1=-*********&period2=**********&interval=1d
2025-05-30 00:37:02 [main] INFO  c.i.provider.YahooFinanceScraper - Saved HTML content to file: C:\Users\<USER>\Downloads\yahoo-finance-html\yahoo_finance_ABLV_1748536622.html
2025-05-30 00:37:02 [main] INFO  c.i.provider.YahooFinanceScraper - Read HTML content from file: C:\Users\<USER>\Downloads\yahoo-finance-html\yahoo_finance_ABLV_1748536622.html
2025-05-30 00:37:02 [main] INFO  c.i.provider.YahooFinanceScraper - Extracted 429 OHLCV data points for ABLV
2025-05-30 00:37:02 [main] INFO  c.i.database.DatabaseManager - Saved 429 OHLCV data points for ABLV
2025-05-30 00:37:02 [main] INFO  c.i.provider.YahooFinanceProvider - Downloaded and saved 429 data points for ABLV
2025-05-30 00:37:02 [main] INFO  c.investment.MarketDataDownloaderApp - ✓ Successfully processed ABLV
2025-05-30 00:37:03 [main] INFO  c.investment.MarketDataDownloaderApp - Processing instrument 55/10009: ABLVW (Able View Global Inc.)
2025-05-30 00:37:03 [main] INFO  c.investment.MarketDataDownloaderApp - Downloading data for ABLVW from 1963-05-30 to 2025-05-30 (attempt 1/4)
2025-05-30 00:37:03 [main] INFO  c.i.provider.YahooFinanceProvider - Requesting data from Yahoo Finance for symbol: ABLVW, from 1963-05-30 to 2025-05-30
2025-05-30 00:37:03 [main] INFO  c.i.provider.YahooFinanceScraper - Scraping data from Yahoo Finance: https://finance.yahoo.com/quote/ABLVW/history/?period1=-*********&period2=**********&interval=1d
2025-05-30 00:37:04 [main] INFO  c.i.provider.YahooFinanceScraper - Saved HTML content to file: C:\Users\<USER>\Downloads\yahoo-finance-html\yahoo_finance_ABLVW_1748536624.html
2025-05-30 00:37:04 [main] INFO  c.i.provider.YahooFinanceScraper - Read HTML content from file: C:\Users\<USER>\Downloads\yahoo-finance-html\yahoo_finance_ABLVW_1748536624.html
2025-05-30 00:37:04 [main] INFO  c.i.provider.YahooFinanceScraper - Extracted 1 OHLCV data points for ABLVW
2025-05-30 00:37:04 [main] INFO  c.i.database.DatabaseManager - Saved 1 OHLCV data points for ABLVW
2025-05-30 00:37:04 [main] INFO  c.i.provider.YahooFinanceProvider - Downloaded and saved 1 data points for ABLVW
2025-05-30 00:37:04 [main] INFO  c.investment.MarketDataDownloaderApp - ✓ Successfully processed ABLVW
2025-05-30 00:37:05 [main] INFO  c.investment.MarketDataDownloaderApp - Processing instrument 56/10009: ABLZF (ABB LTD)
2025-05-30 00:37:05 [main] INFO  c.investment.MarketDataDownloaderApp - Downloading data for ABLZF from 1963-05-30 to 2025-05-30 (attempt 1/4)
2025-05-30 00:37:05 [main] INFO  c.i.provider.YahooFinanceProvider - Requesting data from Yahoo Finance for symbol: ABLZF, from 1963-05-30 to 2025-05-30
2025-05-30 00:37:05 [main] INFO  c.i.provider.YahooFinanceScraper - Scraping data from Yahoo Finance: https://finance.yahoo.com/quote/ABLZF/history/?period1=-*********&period2=**********&interval=1d
2025-05-30 00:37:08 [main] INFO  c.i.provider.YahooFinanceScraper - Saved HTML content to file: C:\Users\<USER>\Downloads\yahoo-finance-html\yahoo_finance_ABLZF_1748536628.html
2025-05-30 00:37:08 [main] INFO  c.i.provider.YahooFinanceScraper - Read HTML content from file: C:\Users\<USER>\Downloads\yahoo-finance-html\yahoo_finance_ABLZF_1748536628.html
2025-05-30 00:37:08 [main] INFO  c.i.provider.YahooFinanceScraper - Extracted 3189 OHLCV data points for ABLZF
2025-05-30 00:37:09 [main] INFO  c.i.database.DatabaseManager - Saved 3189 OHLCV data points for ABLZF
2025-05-30 00:37:09 [main] INFO  c.i.provider.YahooFinanceProvider - Downloaded and saved 3189 data points for ABLZF
2025-05-30 00:37:09 [main] INFO  c.investment.MarketDataDownloaderApp - ✓ Successfully processed ABLZF
2025-05-30 00:37:09 [main] INFO  c.investment.MarketDataDownloaderApp - Processing instrument 57/10009: ABM (ABM INDUSTRIES INC /DE/)
2025-05-30 00:37:09 [main] INFO  c.investment.MarketDataDownloaderApp - Downloading data for ABM from 1963-05-30 to 2025-05-30 (attempt 1/4)
2025-05-30 00:37:09 [main] INFO  c.i.provider.YahooFinanceProvider - Requesting data from Yahoo Finance for symbol: ABM, from 1963-05-30 to 2025-05-30
2025-05-30 00:37:09 [main] INFO  c.i.provider.YahooFinanceScraper - Scraping data from Yahoo Finance: https://finance.yahoo.com/quote/ABM/history/?period1=-*********&period2=**********&interval=1d
2025-05-30 00:37:14 [main] INFO  c.i.provider.YahooFinanceScraper - Saved HTML content to file: C:\Users\<USER>\Downloads\yahoo-finance-html\yahoo_finance_ABM_1748536634.html
2025-05-30 00:37:15 [main] INFO  c.i.provider.YahooFinanceScraper - Read HTML content from file: C:\Users\<USER>\Downloads\yahoo-finance-html\yahoo_finance_ABM_1748536634.html
2025-05-30 00:37:15 [main] INFO  c.i.provider.YahooFinanceScraper - Extracted 11350 OHLCV data points for ABM
2025-05-30 00:37:18 [main] INFO  c.i.database.DatabaseManager - Saved 11350 OHLCV data points for ABM
2025-05-30 00:37:18 [main] INFO  c.i.provider.YahooFinanceProvider - Downloaded and saved 11350 data points for ABM
2025-05-30 00:37:18 [main] INFO  c.investment.MarketDataDownloaderApp - ✓ Successfully processed ABM
2025-05-30 00:37:18 [main] INFO  c.investment.MarketDataDownloaderApp - Processing instrument 58/10009: ABNB (Airbnb, Inc.)
2025-05-30 00:37:18 [main] INFO  c.investment.MarketDataDownloaderApp - Downloading data for ABNB from 1963-05-30 to 2025-05-30 (attempt 1/4)
2025-05-30 00:37:18 [main] INFO  c.i.provider.YahooFinanceProvider - Requesting data from Yahoo Finance for symbol: ABNB, from 1963-05-30 to 2025-05-30
2025-05-30 00:37:18 [main] INFO  c.i.provider.YahooFinanceScraper - Scraping data from Yahoo Finance: https://finance.yahoo.com/quote/ABNB/history/?period1=-*********&period2=**********&interval=1d
2025-05-30 00:37:19 [main] INFO  c.i.provider.YahooFinanceScraper - Saved HTML content to file: C:\Users\<USER>\Downloads\yahoo-finance-html\yahoo_finance_ABNB_1748536639.html
2025-05-30 00:37:20 [main] INFO  c.i.provider.YahooFinanceScraper - Read HTML content from file: C:\Users\<USER>\Downloads\yahoo-finance-html\yahoo_finance_ABNB_1748536639.html
2025-05-30 00:37:20 [main] INFO  c.i.provider.YahooFinanceScraper - Extracted 1121 OHLCV data points for ABNB
2025-05-30 00:37:20 [main] INFO  c.i.database.DatabaseManager - Saved 1121 OHLCV data points for ABNB
2025-05-30 00:37:20 [main] INFO  c.i.provider.YahooFinanceProvider - Downloaded and saved 1121 data points for ABNB
2025-05-30 00:37:20 [main] INFO  c.investment.MarketDataDownloaderApp - ✓ Successfully processed ABNB
2025-05-30 00:37:20 [main] INFO  c.investment.MarketDataDownloaderApp - Processing instrument 59/10009: ABOS (Acumen Pharmaceuticals, Inc.)
2025-05-30 00:37:20 [main] INFO  c.investment.MarketDataDownloaderApp - Downloading data for ABOS from 1963-05-30 to 2025-05-30 (attempt 1/4)
2025-05-30 00:37:20 [main] INFO  c.i.provider.YahooFinanceProvider - Requesting data from Yahoo Finance for symbol: ABOS, from 1963-05-30 to 2025-05-30
2025-05-30 00:37:20 [main] INFO  c.i.provider.YahooFinanceScraper - Scraping data from Yahoo Finance: https://finance.yahoo.com/quote/ABOS/history/?period1=-*********&period2=**********&interval=1d
2025-05-30 00:37:21 [main] INFO  c.i.provider.YahooFinanceScraper - Saved HTML content to file: C:\Users\<USER>\Downloads\yahoo-finance-html\yahoo_finance_ABOS_1748536641.html
2025-05-30 00:37:21 [main] INFO  c.i.provider.YahooFinanceScraper - Read HTML content from file: C:\Users\<USER>\Downloads\yahoo-finance-html\yahoo_finance_ABOS_1748536641.html
2025-05-30 00:37:21 [main] INFO  c.i.provider.YahooFinanceScraper - Extracted 982 OHLCV data points for ABOS
2025-05-30 00:37:22 [main] INFO  c.i.database.DatabaseManager - Saved 982 OHLCV data points for ABOS
2025-05-30 00:37:22 [main] INFO  c.i.provider.YahooFinanceProvider - Downloaded and saved 982 data points for ABOS
2025-05-30 00:37:22 [main] INFO  c.investment.MarketDataDownloaderApp - ✓ Successfully processed ABOS
2025-05-30 00:37:22 [main] INFO  c.investment.MarketDataDownloaderApp - Processing instrument 60/10009: ABP (Abpro Holdings, Inc.)
2025-05-30 00:37:22 [main] INFO  c.investment.MarketDataDownloaderApp - Downloading data for ABP from 1963-05-30 to 2025-05-30 (attempt 1/4)
2025-05-30 00:37:22 [main] INFO  c.i.provider.YahooFinanceProvider - Requesting data from Yahoo Finance for symbol: ABP, from 1963-05-30 to 2025-05-30
2025-05-30 00:37:22 [main] INFO  c.i.provider.YahooFinanceScraper - Scraping data from Yahoo Finance: https://finance.yahoo.com/quote/ABP/history/?period1=-*********&period2=**********&interval=1d
2025-05-30 00:37:24 [main] INFO  c.i.provider.YahooFinanceScraper - Saved HTML content to file: C:\Users\<USER>\Downloads\yahoo-finance-html\yahoo_finance_ABP_1748536644.html
2025-05-30 00:37:24 [main] INFO  c.i.provider.YahooFinanceScraper - Read HTML content from file: C:\Users\<USER>\Downloads\yahoo-finance-html\yahoo_finance_ABP_1748536644.html
2025-05-30 00:37:24 [main] INFO  c.i.provider.YahooFinanceScraper - Extracted 545 OHLCV data points for ABP
2025-05-30 00:37:24 [main] INFO  c.i.database.DatabaseManager - Saved 545 OHLCV data points for ABP
2025-05-30 00:37:24 [main] INFO  c.i.provider.YahooFinanceProvider - Downloaded and saved 545 data points for ABP
2025-05-30 00:37:24 [main] INFO  c.investment.MarketDataDownloaderApp - ✓ Successfully processed ABP
2025-05-30 00:37:24 [main] INFO  c.investment.MarketDataDownloaderApp - Processing instrument 61/10009: ABPWW (Abpro Holdings, Inc.)
2025-05-30 00:37:24 [main] INFO  c.investment.MarketDataDownloaderApp - Downloading data for ABPWW from 1963-05-30 to 2025-05-30 (attempt 1/4)
2025-05-30 00:37:24 [main] INFO  c.i.provider.YahooFinanceProvider - Requesting data from Yahoo Finance for symbol: ABPWW, from 1963-05-30 to 2025-05-30
2025-05-30 00:37:24 [main] INFO  c.i.provider.YahooFinanceScraper - Scraping data from Yahoo Finance: https://finance.yahoo.com/quote/ABPWW/history/?period1=-*********&period2=**********&interval=1d
2025-05-30 00:37:26 [main] INFO  c.i.provider.YahooFinanceScraper - Saved HTML content to file: C:\Users\<USER>\Downloads\yahoo-finance-html\yahoo_finance_ABPWW_1748536646.html
2025-05-30 00:37:26 [main] INFO  c.i.provider.YahooFinanceScraper - Read HTML content from file: C:\Users\<USER>\Downloads\yahoo-finance-html\yahoo_finance_ABPWW_1748536646.html
2025-05-30 00:37:26 [main] INFO  c.i.provider.YahooFinanceScraper - Extracted 1 OHLCV data points for ABPWW
2025-05-30 00:37:26 [main] INFO  c.i.database.DatabaseManager - Saved 1 OHLCV data points for ABPWW
2025-05-30 00:37:26 [main] INFO  c.i.provider.YahooFinanceProvider - Downloaded and saved 1 data points for ABPWW
2025-05-30 00:37:26 [main] INFO  c.investment.MarketDataDownloaderApp - ✓ Successfully processed ABPWW
2025-05-30 00:37:26 [main] INFO  c.investment.MarketDataDownloaderApp - Processing instrument 62/10009: ABQQ (AB INTERNATIONAL GROUP CORP.)
2025-05-30 00:37:26 [main] INFO  c.investment.MarketDataDownloaderApp - Downloading data for ABQQ from 1963-05-30 to 2025-05-30 (attempt 1/4)
2025-05-30 00:37:26 [main] INFO  c.i.provider.YahooFinanceProvider - Requesting data from Yahoo Finance for symbol: ABQQ, from 1963-05-30 to 2025-05-30
2025-05-30 00:37:26 [main] INFO  c.i.provider.YahooFinanceScraper - Scraping data from Yahoo Finance: https://finance.yahoo.com/quote/ABQQ/history/?period1=-*********&period2=**********&interval=1d
2025-05-30 00:37:30 [main] INFO  c.i.provider.YahooFinanceScraper - Saved HTML content to file: C:\Users\<USER>\Downloads\yahoo-finance-html\yahoo_finance_ABQQ_1748536650.html
2025-05-30 00:37:30 [main] INFO  c.i.provider.YahooFinanceScraper - Read HTML content from file: C:\Users\<USER>\Downloads\yahoo-finance-html\yahoo_finance_ABQQ_1748536650.html
2025-05-30 00:37:30 [main] INFO  c.i.provider.YahooFinanceScraper - Extracted 1368 OHLCV data points for ABQQ
2025-05-30 00:37:30 [main] INFO  c.i.database.DatabaseManager - Saved 1368 OHLCV data points for ABQQ
2025-05-30 00:37:30 [main] INFO  c.i.provider.YahooFinanceProvider - Downloaded and saved 1368 data points for ABQQ
2025-05-30 00:37:30 [main] INFO  c.investment.MarketDataDownloaderApp - ✓ Successfully processed ABQQ
2025-05-30 00:37:30 [main] INFO  c.investment.MarketDataDownloaderApp - Processing instrument 63/10009: ABR (ARBOR REALTY TRUST INC)
2025-05-30 00:37:30 [main] INFO  c.investment.MarketDataDownloaderApp - Downloading data for ABR from 1963-05-30 to 2025-05-30 (attempt 1/4)
2025-05-30 00:37:30 [main] INFO  c.i.provider.YahooFinanceProvider - Requesting data from Yahoo Finance for symbol: ABR, from 1963-05-30 to 2025-05-30
2025-05-30 00:37:30 [main] INFO  c.i.provider.YahooFinanceScraper - Scraping data from Yahoo Finance: https://finance.yahoo.com/quote/ABR/history/?period1=-*********&period2=**********&interval=1d
2025-05-30 00:37:34 [main] INFO  c.i.provider.YahooFinanceScraper - Saved HTML content to file: C:\Users\<USER>\Downloads\yahoo-finance-html\yahoo_finance_ABR_1748536654.html
2025-05-30 00:37:34 [main] INFO  c.i.provider.YahooFinanceScraper - Read HTML content from file: C:\Users\<USER>\Downloads\yahoo-finance-html\yahoo_finance_ABR_1748536654.html
2025-05-30 00:37:34 [main] INFO  c.i.provider.YahooFinanceScraper - Extracted 5320 OHLCV data points for ABR
2025-05-30 00:37:36 [main] INFO  c.i.database.DatabaseManager - Saved 5320 OHLCV data points for ABR
2025-05-30 00:37:36 [main] INFO  c.i.provider.YahooFinanceProvider - Downloaded and saved 5320 data points for ABR
2025-05-30 00:37:36 [main] INFO  c.investment.MarketDataDownloaderApp - ✓ Successfully processed ABR
2025-05-30 00:37:36 [main] INFO  c.investment.MarketDataDownloaderApp - Processing instrument 64/10009: ABR-PD (ARBOR REALTY TRUST INC)
2025-05-30 00:37:36 [main] INFO  c.investment.MarketDataDownloaderApp - Downloading data for ABR-PD from 1963-05-30 to 2025-05-30 (attempt 1/4)
2025-05-30 00:37:36 [main] INFO  c.i.provider.YahooFinanceProvider - Requesting data from Yahoo Finance for symbol: ABR-PD, from 1963-05-30 to 2025-05-30
2025-05-30 00:37:36 [main] INFO  c.i.provider.YahooFinanceScraper - Scraping data from Yahoo Finance: https://finance.yahoo.com/quote/ABR-PD/history/?period1=-*********&period2=**********&interval=1d
2025-05-30 00:37:38 [main] INFO  c.i.provider.YahooFinanceScraper - Saved HTML content to file: C:\Users\<USER>\Downloads\yahoo-finance-html\yahoo_finance_ABR-PD_1748536658.html
2025-05-30 00:37:38 [main] INFO  c.i.provider.YahooFinanceScraper - Read HTML content from file: C:\Users\<USER>\Downloads\yahoo-finance-html\yahoo_finance_ABR-PD_1748536658.html
2025-05-30 00:37:38 [main] INFO  c.i.provider.YahooFinanceScraper - Extracted 1005 OHLCV data points for ABR-PD
2025-05-30 00:37:39 [main] INFO  c.i.database.DatabaseManager - Saved 1005 OHLCV data points for ABR-PD
2025-05-30 00:37:39 [main] INFO  c.i.provider.YahooFinanceProvider - Downloaded and saved 1005 data points for ABR-PD
2025-05-30 00:37:39 [main] INFO  c.investment.MarketDataDownloaderApp - ✓ Successfully processed ABR-PD
2025-05-30 00:37:39 [main] INFO  c.investment.MarketDataDownloaderApp - Processing instrument 65/10009: ABR-PE (ARBOR REALTY TRUST INC)
2025-05-30 00:37:39 [main] INFO  c.investment.MarketDataDownloaderApp - Downloading data for ABR-PE from 1963-05-30 to 2025-05-30 (attempt 1/4)
2025-05-30 00:37:39 [main] INFO  c.i.provider.YahooFinanceProvider - Requesting data from Yahoo Finance for symbol: ABR-PE, from 1963-05-30 to 2025-05-30
2025-05-30 00:37:39 [main] INFO  c.i.provider.YahooFinanceScraper - Scraping data from Yahoo Finance: https://finance.yahoo.com/quote/ABR-PE/history/?period1=-*********&period2=**********&interval=1d
2025-05-30 00:37:41 [main] INFO  c.i.provider.YahooFinanceScraper - Saved HTML content to file: C:\Users\<USER>\Downloads\yahoo-finance-html\yahoo_finance_ABR-PE_1748536661.html
2025-05-30 00:37:41 [main] INFO  c.i.provider.YahooFinanceScraper - Read HTML content from file: C:\Users\<USER>\Downloads\yahoo-finance-html\yahoo_finance_ABR-PE_1748536661.html
2025-05-30 00:37:41 [main] INFO  c.i.provider.YahooFinanceScraper - Extracted 955 OHLCV data points for ABR-PE
2025-05-30 00:37:42 [main] INFO  c.i.database.DatabaseManager - Saved 955 OHLCV data points for ABR-PE
2025-05-30 00:37:42 [main] INFO  c.i.provider.YahooFinanceProvider - Downloaded and saved 955 data points for ABR-PE
2025-05-30 00:37:42 [main] INFO  c.investment.MarketDataDownloaderApp - ✓ Successfully processed ABR-PE
2025-05-30 00:37:42 [main] INFO  c.investment.MarketDataDownloaderApp - Processing instrument 66/10009: ABR-PF (ARBOR REALTY TRUST INC)
2025-05-30 00:37:42 [main] INFO  c.investment.MarketDataDownloaderApp - Downloading data for ABR-PF from 1963-05-30 to 2025-05-30 (attempt 1/4)
2025-05-30 00:37:42 [main] INFO  c.i.provider.YahooFinanceProvider - Requesting data from Yahoo Finance for symbol: ABR-PF, from 1963-05-30 to 2025-05-30
2025-05-30 00:37:42 [main] INFO  c.i.provider.YahooFinanceScraper - Scraping data from Yahoo Finance: https://finance.yahoo.com/quote/ABR-PF/history/?period1=-*********&period2=**********&interval=1d
2025-05-30 00:37:44 [main] INFO  c.i.provider.YahooFinanceScraper - Saved HTML content to file: C:\Users\<USER>\Downloads\yahoo-finance-html\yahoo_finance_ABR-PF_1748536664.html
2025-05-30 00:37:44 [main] INFO  c.i.provider.YahooFinanceScraper - Read HTML content from file: C:\Users\<USER>\Downloads\yahoo-finance-html\yahoo_finance_ABR-PF_1748536664.html
2025-05-30 00:37:44 [main] INFO  c.i.provider.YahooFinanceScraper - Extracted 857 OHLCV data points for ABR-PF
2025-05-30 00:37:44 [main] INFO  c.i.database.DatabaseManager - Saved 857 OHLCV data points for ABR-PF
2025-05-30 00:37:44 [main] INFO  c.i.provider.YahooFinanceProvider - Downloaded and saved 857 data points for ABR-PF
2025-05-30 00:37:44 [main] INFO  c.investment.MarketDataDownloaderApp - ✓ Successfully processed ABR-PF
2025-05-30 00:37:45 [main] INFO  c.investment.MarketDataDownloaderApp - Processing instrument 67/10009: ABSI (Absci Corp)
2025-05-30 00:37:45 [main] INFO  c.investment.MarketDataDownloaderApp - Downloading data for ABSI from 1963-05-30 to 2025-05-30 (attempt 1/4)
2025-05-30 00:37:45 [main] INFO  c.i.provider.YahooFinanceProvider - Requesting data from Yahoo Finance for symbol: ABSI, from 1963-05-30 to 2025-05-30
2025-05-30 00:37:45 [main] INFO  c.i.provider.YahooFinanceScraper - Scraping data from Yahoo Finance: https://finance.yahoo.com/quote/ABSI/history/?period1=-*********&period2=**********&interval=1d
2025-05-30 00:37:46 [main] INFO  c.i.provider.YahooFinanceScraper - Saved HTML content to file: C:\Users\<USER>\Downloads\yahoo-finance-html\yahoo_finance_ABSI_1748536666.html
2025-05-30 00:37:46 [main] INFO  c.i.provider.YahooFinanceScraper - Read HTML content from file: C:\Users\<USER>\Downloads\yahoo-finance-html\yahoo_finance_ABSI_1748536666.html
2025-05-30 00:37:46 [main] INFO  c.i.provider.YahooFinanceScraper - Extracted 968 OHLCV data points for ABSI
2025-05-30 00:37:46 [main] INFO  c.i.database.DatabaseManager - Saved 968 OHLCV data points for ABSI
2025-05-30 00:37:46 [main] INFO  c.i.provider.YahooFinanceProvider - Downloaded and saved 968 data points for ABSI
2025-05-30 00:37:46 [main] INFO  c.investment.MarketDataDownloaderApp - ✓ Successfully processed ABSI
2025-05-30 00:37:47 [main] INFO  c.investment.MarketDataDownloaderApp - Processing instrument 68/10009: ABT (ABBOTT LABORATORIES)
2025-05-30 00:37:47 [main] INFO  c.investment.MarketDataDownloaderApp - Downloading data for ABT from 1963-05-30 to 2025-05-30 (attempt 1/4)
2025-05-30 00:37:47 [main] INFO  c.i.provider.YahooFinanceProvider - Requesting data from Yahoo Finance for symbol: ABT, from 1963-05-30 to 2025-05-30
2025-05-30 00:37:47 [main] INFO  c.i.provider.YahooFinanceScraper - Scraping data from Yahoo Finance: https://finance.yahoo.com/quote/ABT/history/?period1=-*********&period2=**********&interval=1d
2025-05-30 00:37:55 [main] INFO  c.i.provider.YahooFinanceScraper - Saved HTML content to file: C:\Users\<USER>\Downloads\yahoo-finance-html\yahoo_finance_ABT_1748536675.html
2025-05-30 00:37:55 [main] INFO  c.i.provider.YahooFinanceScraper - Read HTML content from file: C:\Users\<USER>\Downloads\yahoo-finance-html\yahoo_finance_ABT_1748536675.html
2025-05-30 00:37:55 [main] INFO  c.i.provider.YahooFinanceScraper - Extracted 11394 OHLCV data points for ABT
2025-05-30 00:37:58 [main] INFO  c.i.database.DatabaseManager - Saved 11394 OHLCV data points for ABT
2025-05-30 00:37:58 [main] INFO  c.i.provider.YahooFinanceProvider - Downloaded and saved 11394 data points for ABT
2025-05-30 00:37:58 [main] INFO  c.investment.MarketDataDownloaderApp - ✓ Successfully processed ABT
2025-05-30 00:37:59 [main] INFO  c.investment.MarketDataDownloaderApp - Processing instrument 69/10009: ABTS (Abits Group Inc)
2025-05-30 00:37:59 [main] INFO  c.investment.MarketDataDownloaderApp - Downloading data for ABTS from 1963-05-30 to 2025-05-30 (attempt 1/4)
2025-05-30 00:37:59 [main] INFO  c.i.provider.YahooFinanceProvider - Requesting data from Yahoo Finance for symbol: ABTS, from 1963-05-30 to 2025-05-30
2025-05-30 00:37:59 [main] INFO  c.i.provider.YahooFinanceScraper - Scraping data from Yahoo Finance: https://finance.yahoo.com/quote/ABTS/history/?period1=-*********&period2=**********&interval=1d
2025-05-30 00:38:02 [main] INFO  c.i.provider.YahooFinanceScraper - Saved HTML content to file: C:\Users\<USER>\Downloads\yahoo-finance-html\yahoo_finance_ABTS_1748536682.html
2025-05-30 00:38:02 [main] INFO  c.i.provider.YahooFinanceScraper - Read HTML content from file: C:\Users\<USER>\Downloads\yahoo-finance-html\yahoo_finance_ABTS_1748536682.html
2025-05-30 00:38:02 [main] INFO  c.i.provider.YahooFinanceScraper - Extracted 2445 OHLCV data points for ABTS
2025-05-30 00:38:03 [main] INFO  c.i.database.DatabaseManager - Saved 2445 OHLCV data points for ABTS
2025-05-30 00:38:03 [main] INFO  c.i.provider.YahooFinanceProvider - Downloaded and saved 2445 data points for ABTS
2025-05-30 00:38:03 [main] INFO  c.investment.MarketDataDownloaderApp - ✓ Successfully processed ABTS
2025-05-30 00:38:03 [main] INFO  c.investment.MarketDataDownloaderApp - Processing instrument 70/10009: ABUS (Arbutus Biopharma Corp)
2025-05-30 00:38:03 [main] INFO  c.investment.MarketDataDownloaderApp - Downloading data for ABUS from 1963-05-30 to 2025-05-30 (attempt 1/4)
2025-05-30 00:38:03 [main] INFO  c.i.provider.YahooFinanceProvider - Requesting data from Yahoo Finance for symbol: ABUS, from 1963-05-30 to 2025-05-30
2025-05-30 00:38:03 [main] INFO  c.i.provider.YahooFinanceScraper - Scraping data from Yahoo Finance: https://finance.yahoo.com/quote/ABUS/history/?period1=-*********&period2=**********&interval=1d
2025-05-30 00:38:08 [main] INFO  c.i.provider.YahooFinanceScraper - Saved HTML content to file: C:\Users\<USER>\Downloads\yahoo-finance-html\yahoo_finance_ABUS_1748536688.html
2025-05-30 00:38:08 [main] INFO  c.i.provider.YahooFinanceScraper - Read HTML content from file: C:\Users\<USER>\Downloads\yahoo-finance-html\yahoo_finance_ABUS_1748536688.html
2025-05-30 00:38:08 [main] INFO  c.i.provider.YahooFinanceScraper - Extracted 4340 OHLCV data points for ABUS
2025-05-30 00:38:09 [main] INFO  c.i.database.DatabaseManager - Saved 4340 OHLCV data points for ABUS
2025-05-30 00:38:09 [main] INFO  c.i.provider.YahooFinanceProvider - Downloaded and saved 4340 data points for ABUS
2025-05-30 00:38:09 [main] INFO  c.investment.MarketDataDownloaderApp - ✓ Successfully processed ABUS
2025-05-30 00:38:10 [main] INFO  c.investment.MarketDataDownloaderApp - Processing instrument 71/10009: ABVC (ABVC BIOPHARMA, INC.)
2025-05-30 00:38:10 [main] INFO  c.investment.MarketDataDownloaderApp - Downloading data for ABVC from 1963-05-30 to 2025-05-30 (attempt 1/4)
2025-05-30 00:38:10 [main] INFO  c.i.provider.YahooFinanceProvider - Requesting data from Yahoo Finance for symbol: ABVC, from 1963-05-30 to 2025-05-30
2025-05-30 00:38:10 [main] INFO  c.i.provider.YahooFinanceScraper - Scraping data from Yahoo Finance: https://finance.yahoo.com/quote/ABVC/history/?period1=-*********&period2=**********&interval=1d
2025-05-30 00:38:14 [main] INFO  c.i.provider.YahooFinanceScraper - Saved HTML content to file: C:\Users\<USER>\Downloads\yahoo-finance-html\yahoo_finance_ABVC_1748536694.html
2025-05-30 00:38:14 [main] INFO  c.i.provider.YahooFinanceScraper - Read HTML content from file: C:\Users\<USER>\Downloads\yahoo-finance-html\yahoo_finance_ABVC_1748536694.html
2025-05-30 00:38:14 [main] INFO  c.i.provider.YahooFinanceScraper - Extracted 1514 OHLCV data points for ABVC
2025-05-30 00:38:14 [main] INFO  c.i.database.DatabaseManager - Saved 1514 OHLCV data points for ABVC
2025-05-30 00:38:14 [main] INFO  c.i.provider.YahooFinanceProvider - Downloaded and saved 1514 data points for ABVC
2025-05-30 00:38:14 [main] INFO  c.investment.MarketDataDownloaderApp - ✓ Successfully processed ABVC
2025-05-30 00:38:15 [main] INFO  c.investment.MarketDataDownloaderApp - Processing instrument 72/10009: ABVE (Above Food Ingredients Inc.)
2025-05-30 00:38:15 [main] INFO  c.investment.MarketDataDownloaderApp - Downloading data for ABVE from 1963-05-30 to 2025-05-30 (attempt 1/4)
2025-05-30 00:38:15 [main] INFO  c.i.provider.YahooFinanceProvider - Requesting data from Yahoo Finance for symbol: ABVE, from 1963-05-30 to 2025-05-30
2025-05-30 00:38:15 [main] INFO  c.i.provider.YahooFinanceScraper - Scraping data from Yahoo Finance: https://finance.yahoo.com/quote/ABVE/history/?period1=-*********&period2=**********&interval=1d
2025-05-30 00:38:16 [main] INFO  c.i.provider.YahooFinanceScraper - Saved HTML content to file: C:\Users\<USER>\Downloads\yahoo-finance-html\yahoo_finance_ABVE_1748536696.html
2025-05-30 00:38:16 [main] INFO  c.i.provider.YahooFinanceScraper - Read HTML content from file: C:\Users\<USER>\Downloads\yahoo-finance-html\yahoo_finance_ABVE_1748536696.html
2025-05-30 00:38:16 [main] INFO  c.i.provider.YahooFinanceScraper - Extracted 228 OHLCV data points for ABVE
2025-05-30 00:38:17 [main] INFO  c.i.database.DatabaseManager - Saved 228 OHLCV data points for ABVE
2025-05-30 00:38:17 [main] INFO  c.i.provider.YahooFinanceProvider - Downloaded and saved 228 data points for ABVE
2025-05-30 00:38:17 [main] INFO  c.investment.MarketDataDownloaderApp - ✓ Successfully processed ABVE
2025-05-30 00:38:17 [main] INFO  c.investment.MarketDataDownloaderApp - Processing instrument 73/10009: ABVEW (Above Food Ingredients Inc.)
2025-05-30 00:38:17 [main] INFO  c.investment.MarketDataDownloaderApp - Downloading data for ABVEW from 1963-05-30 to 2025-05-30 (attempt 1/4)
2025-05-30 00:38:17 [main] INFO  c.i.provider.YahooFinanceProvider - Requesting data from Yahoo Finance for symbol: ABVEW, from 1963-05-30 to 2025-05-30
2025-05-30 00:38:17 [main] INFO  c.i.provider.YahooFinanceScraper - Scraping data from Yahoo Finance: https://finance.yahoo.com/quote/ABVEW/history/?period1=-*********&period2=**********&interval=1d
2025-05-30 00:38:19 [main] INFO  c.i.provider.YahooFinanceScraper - Saved HTML content to file: C:\Users\<USER>\Downloads\yahoo-finance-html\yahoo_finance_ABVEW_1748536699.html
2025-05-30 00:38:19 [main] INFO  c.i.provider.YahooFinanceScraper - Read HTML content from file: C:\Users\<USER>\Downloads\yahoo-finance-html\yahoo_finance_ABVEW_1748536699.html
2025-05-30 00:38:19 [main] INFO  c.i.provider.YahooFinanceScraper - Extracted 1 OHLCV data points for ABVEW
2025-05-30 00:38:19 [main] INFO  c.i.database.DatabaseManager - Saved 1 OHLCV data points for ABVEW
2025-05-30 00:38:19 [main] INFO  c.i.provider.YahooFinanceProvider - Downloaded and saved 1 data points for ABVEW
2025-05-30 00:38:19 [main] INFO  c.investment.MarketDataDownloaderApp - ✓ Successfully processed ABVEW
2025-05-30 00:38:19 [main] INFO  c.investment.MarketDataDownloaderApp - Processing instrument 74/10009: ABVX (Abivax S.A.)
2025-05-30 00:38:19 [main] INFO  c.investment.MarketDataDownloaderApp - Downloading data for ABVX from 1963-05-30 to 2025-05-30 (attempt 1/4)
2025-05-30 00:38:19 [main] INFO  c.i.provider.YahooFinanceProvider - Requesting data from Yahoo Finance for symbol: ABVX, from 1963-05-30 to 2025-05-30
2025-05-30 00:38:19 [main] INFO  c.i.provider.YahooFinanceScraper - Scraping data from Yahoo Finance: https://finance.yahoo.com/quote/ABVX/history/?period1=-*********&period2=**********&interval=1d
2025-05-30 00:38:21 [main] INFO  c.i.provider.YahooFinanceScraper - Saved HTML content to file: C:\Users\<USER>\Downloads\yahoo-finance-html\yahoo_finance_ABVX_1748536701.html
2025-05-30 00:38:21 [main] INFO  c.i.provider.YahooFinanceScraper - Read HTML content from file: C:\Users\<USER>\Downloads\yahoo-finance-html\yahoo_finance_ABVX_1748536701.html
2025-05-30 00:38:21 [main] INFO  c.i.provider.YahooFinanceScraper - Extracted 402 OHLCV data points for ABVX
2025-05-30 00:38:21 [main] INFO  c.i.database.DatabaseManager - Saved 402 OHLCV data points for ABVX
2025-05-30 00:38:21 [main] INFO  c.i.provider.YahooFinanceProvider - Downloaded and saved 402 data points for ABVX
2025-05-30 00:38:21 [main] INFO  c.investment.MarketDataDownloaderApp - ✓ Successfully processed ABVX
2025-05-30 00:38:22 [main] INFO  c.investment.MarketDataDownloaderApp - Processing instrument 75/10009: ABXXF (Abaxx Technologies Inc.)
2025-05-30 00:38:22 [main] INFO  c.investment.MarketDataDownloaderApp - Downloading data for ABXXF from 1963-05-30 to 2025-05-30 (attempt 1/4)
2025-05-30 00:38:22 [main] INFO  c.i.provider.YahooFinanceProvider - Requesting data from Yahoo Finance for symbol: ABXXF, from 1963-05-30 to 2025-05-30
2025-05-30 00:38:22 [main] INFO  c.i.provider.YahooFinanceScraper - Scraping data from Yahoo Finance: https://finance.yahoo.com/quote/ABXXF/history/?period1=-*********&period2=**********&interval=1d
2025-05-30 00:38:24 [main] INFO  c.i.provider.YahooFinanceScraper - Saved HTML content to file: C:\Users\<USER>\Downloads\yahoo-finance-html\yahoo_finance_ABXXF_1748536704.html
2025-05-30 00:38:24 [main] INFO  c.i.provider.YahooFinanceScraper - Read HTML content from file: C:\Users\<USER>\Downloads\yahoo-finance-html\yahoo_finance_ABXXF_1748536704.html
2025-05-30 00:38:24 [main] INFO  c.i.provider.YahooFinanceScraper - Extracted 1115 OHLCV data points for ABXXF
2025-05-30 00:38:24 [main] INFO  c.i.database.DatabaseManager - Saved 1115 OHLCV data points for ABXXF
2025-05-30 00:38:24 [main] INFO  c.i.provider.YahooFinanceProvider - Downloaded and saved 1115 data points for ABXXF
2025-05-30 00:38:24 [main] INFO  c.investment.MarketDataDownloaderApp - ✓ Successfully processed ABXXF
2025-05-30 00:38:25 [main] INFO  c.investment.MarketDataDownloaderApp - Processing instrument 76/10009: AC (Associated Capital Group, Inc.)
2025-05-30 00:38:25 [main] INFO  c.investment.MarketDataDownloaderApp - Downloading data for AC from 1963-05-30 to 2025-05-30 (attempt 1/4)
2025-05-30 00:38:25 [main] INFO  c.i.provider.YahooFinanceProvider - Requesting data from Yahoo Finance for symbol: AC, from 1963-05-30 to 2025-05-30
2025-05-30 00:38:25 [main] INFO  c.i.provider.YahooFinanceScraper - Scraping data from Yahoo Finance: https://finance.yahoo.com/quote/AC/history/?period1=-*********&period2=**********&interval=1d
2025-05-30 00:38:27 [main] INFO  c.i.provider.YahooFinanceScraper - Saved HTML content to file: C:\Users\<USER>\Downloads\yahoo-finance-html\yahoo_finance_AC_1748536707.html
2025-05-30 00:38:27 [main] INFO  c.i.provider.YahooFinanceScraper - Read HTML content from file: C:\Users\<USER>\Downloads\yahoo-finance-html\yahoo_finance_AC_1748536707.html
2025-05-30 00:38:27 [main] INFO  c.i.provider.YahooFinanceScraper - Extracted 2390 OHLCV data points for AC
2025-05-30 00:38:27 [main] INFO  c.i.database.DatabaseManager - Saved 2390 OHLCV data points for AC
2025-05-30 00:38:27 [main] INFO  c.i.provider.YahooFinanceProvider - Downloaded and saved 2390 data points for AC
2025-05-30 00:38:27 [main] INFO  c.investment.MarketDataDownloaderApp - ✓ Successfully processed AC
2025-05-30 00:38:28 [main] INFO  c.investment.MarketDataDownloaderApp - Processing instrument 77/10009: ACA (Arcosa, Inc.)
2025-05-30 00:38:28 [main] INFO  c.investment.MarketDataDownloaderApp - Downloading data for ACA from 1963-05-30 to 2025-05-30 (attempt 1/4)
2025-05-30 00:38:28 [main] INFO  c.i.provider.YahooFinanceProvider - Requesting data from Yahoo Finance for symbol: ACA, from 1963-05-30 to 2025-05-30
2025-05-30 00:38:28 [main] INFO  c.i.provider.YahooFinanceScraper - Scraping data from Yahoo Finance: https://finance.yahoo.com/quote/ACA/history/?period1=-*********&period2=**********&interval=1d
2025-05-30 00:38:30 [main] INFO  c.i.provider.YahooFinanceScraper - Saved HTML content to file: C:\Users\<USER>\Downloads\yahoo-finance-html\yahoo_finance_ACA_1748536710.html
2025-05-30 00:38:30 [main] INFO  c.i.provider.YahooFinanceScraper - Read HTML content from file: C:\Users\<USER>\Downloads\yahoo-finance-html\yahoo_finance_ACA_1748536710.html
2025-05-30 00:38:30 [main] INFO  c.i.provider.YahooFinanceScraper - Extracted 1649 OHLCV data points for ACA
2025-05-30 00:38:31 [main] INFO  c.i.database.DatabaseManager - Saved 1649 OHLCV data points for ACA
2025-05-30 00:38:31 [main] INFO  c.i.provider.YahooFinanceProvider - Downloaded and saved 1649 data points for ACA
2025-05-30 00:38:31 [main] INFO  c.investment.MarketDataDownloaderApp - ✓ Successfully processed ACA
2025-05-30 00:38:31 [main] INFO  c.investment.MarketDataDownloaderApp - Processing instrument 78/10009: ACAD (ACADIA PHARMACEUTICALS INC)
2025-05-30 00:38:31 [main] INFO  c.investment.MarketDataDownloaderApp - Downloading data for ACAD from 1963-05-30 to 2025-05-30 (attempt 1/4)
2025-05-30 00:38:31 [main] INFO  c.i.provider.YahooFinanceProvider - Requesting data from Yahoo Finance for symbol: ACAD, from 1963-05-30 to 2025-05-30
2025-05-30 00:38:31 [main] INFO  c.i.provider.YahooFinanceScraper - Scraping data from Yahoo Finance: https://finance.yahoo.com/quote/ACAD/history/?period1=-*********&period2=**********&interval=1d
2025-05-30 00:38:35 [main] INFO  c.i.provider.YahooFinanceScraper - Saved HTML content to file: C:\Users\<USER>\Downloads\yahoo-finance-html\yahoo_finance_ACAD_1748536715.html
2025-05-30 00:38:35 [main] INFO  c.i.provider.YahooFinanceScraper - Read HTML content from file: C:\Users\<USER>\Downloads\yahoo-finance-html\yahoo_finance_ACAD_1748536715.html
2025-05-30 00:38:35 [main] INFO  c.i.provider.YahooFinanceScraper - Extracted 5282 OHLCV data points for ACAD
2025-05-30 00:38:36 [main] INFO  c.i.database.DatabaseManager - Saved 5282 OHLCV data points for ACAD
2025-05-30 00:38:36 [main] INFO  c.i.provider.YahooFinanceProvider - Downloaded and saved 5282 data points for ACAD
2025-05-30 00:38:36 [main] INFO  c.investment.MarketDataDownloaderApp - ✓ Successfully processed ACAD
2025-05-30 00:38:37 [main] INFO  c.investment.MarketDataDownloaderApp - Processing instrument 79/10009: ACAT (NP Life Sciences Health Industry Group Inc.)
2025-05-30 00:38:37 [main] INFO  c.investment.MarketDataDownloaderApp - Downloading data for ACAT from 1963-05-30 to 2025-05-30 (attempt 1/4)
2025-05-30 00:38:37 [main] INFO  c.i.provider.YahooFinanceProvider - Requesting data from Yahoo Finance for symbol: ACAT, from 1963-05-30 to 2025-05-30
2025-05-30 00:38:37 [main] INFO  c.i.provider.YahooFinanceScraper - Scraping data from Yahoo Finance: https://finance.yahoo.com/quote/ACAT/history/?period1=-*********&period2=**********&interval=1d
2025-05-30 00:38:39 [main] INFO  c.i.provider.YahooFinanceScraper - Saved HTML content to file: C:\Users\<USER>\Downloads\yahoo-finance-html\yahoo_finance_ACAT_1748536719.html
2025-05-30 00:38:39 [main] INFO  c.i.provider.YahooFinanceScraper - Read HTML content from file: C:\Users\<USER>\Downloads\yahoo-finance-html\yahoo_finance_ACAT_1748536719.html
2025-05-30 00:38:39 [main] INFO  c.i.provider.YahooFinanceScraper - Extracted 40 OHLCV data points for ACAT
2025-05-30 00:38:39 [main] INFO  c.i.database.DatabaseManager - Saved 40 OHLCV data points for ACAT
2025-05-30 00:38:39 [main] INFO  c.i.provider.YahooFinanceProvider - Downloaded and saved 40 data points for ACAT
2025-05-30 00:38:39 [main] INFO  c.investment.MarketDataDownloaderApp - ✓ Successfully processed ACAT
2025-05-30 00:38:39 [main] INFO  c.investment.MarketDataDownloaderApp - Processing instrument 80/10009: ACB (AURORA CANNABIS INC)
2025-05-30 00:38:39 [main] INFO  c.investment.MarketDataDownloaderApp - Downloading data for ACB from 1963-05-30 to 2025-05-30 (attempt 1/4)
2025-05-30 00:38:39 [main] INFO  c.i.provider.YahooFinanceProvider - Requesting data from Yahoo Finance for symbol: ACB, from 1963-05-30 to 2025-05-30
2025-05-30 00:38:39 [main] INFO  c.i.provider.YahooFinanceScraper - Scraping data from Yahoo Finance: https://finance.yahoo.com/quote/ACB/history/?period1=-*********&period2=**********&interval=1d
2025-05-30 00:38:41 [main] INFO  c.i.provider.YahooFinanceScraper - Saved HTML content to file: C:\Users\<USER>\Downloads\yahoo-finance-html\yahoo_finance_ACB_1748536721.html
2025-05-30 00:38:41 [main] INFO  c.i.provider.YahooFinanceScraper - Read HTML content from file: C:\Users\<USER>\Downloads\yahoo-finance-html\yahoo_finance_ACB_1748536721.html
2025-05-30 00:38:41 [main] INFO  c.i.provider.YahooFinanceScraper - Extracted 2481 OHLCV data points for ACB
2025-05-30 00:38:42 [main] INFO  c.i.database.DatabaseManager - Saved 2481 OHLCV data points for ACB
2025-05-30 00:38:42 [main] INFO  c.i.provider.YahooFinanceProvider - Downloaded and saved 2481 data points for ACB
2025-05-30 00:38:42 [main] INFO  c.investment.MarketDataDownloaderApp - ✓ Successfully processed ACB
2025-05-30 00:38:42 [main] INFO  c.investment.MarketDataDownloaderApp - Processing instrument 81/10009: ACCO (ACCO BRANDS Corp)
2025-05-30 00:38:42 [main] INFO  c.investment.MarketDataDownloaderApp - Downloading data for ACCO from 1963-05-30 to 2025-05-30 (attempt 1/4)
2025-05-30 00:38:42 [main] INFO  c.i.provider.YahooFinanceProvider - Requesting data from Yahoo Finance for symbol: ACCO, from 1963-05-30 to 2025-05-30
2025-05-30 00:38:42 [main] INFO  c.i.provider.YahooFinanceScraper - Scraping data from Yahoo Finance: https://finance.yahoo.com/quote/ACCO/history/?period1=-*********&period2=**********&interval=1d
2025-05-30 00:38:45 [main] INFO  c.i.provider.YahooFinanceScraper - Saved HTML content to file: C:\Users\<USER>\Downloads\yahoo-finance-html\yahoo_finance_ACCO_1748536725.html
2025-05-30 00:38:45 [main] INFO  c.i.provider.YahooFinanceScraper - Read HTML content from file: C:\Users\<USER>\Downloads\yahoo-finance-html\yahoo_finance_ACCO_1748536725.html
2025-05-30 00:38:45 [main] INFO  c.i.provider.YahooFinanceScraper - Extracted 4977 OHLCV data points for ACCO
2025-05-30 00:38:47 [main] INFO  c.i.database.DatabaseManager - Saved 4977 OHLCV data points for ACCO
2025-05-30 00:38:47 [main] INFO  c.i.provider.YahooFinanceProvider - Downloaded and saved 4977 data points for ACCO
2025-05-30 00:38:47 [main] INFO  c.investment.MarketDataDownloaderApp - ✓ Successfully processed ACCO
2025-05-30 00:38:47 [main] INFO  c.investment.MarketDataDownloaderApp - Processing instrument 82/10009: ACCS (ACCESS Newswire Inc.)
2025-05-30 00:38:47 [main] INFO  c.investment.MarketDataDownloaderApp - Downloading data for ACCS from 1963-05-30 to 2025-05-30 (attempt 1/4)
2025-05-30 00:38:47 [main] INFO  c.i.provider.YahooFinanceProvider - Requesting data from Yahoo Finance for symbol: ACCS, from 1963-05-30 to 2025-05-30
2025-05-30 00:38:47 [main] INFO  c.i.provider.YahooFinanceScraper - Scraping data from Yahoo Finance: https://finance.yahoo.com/quote/ACCS/history/?period1=-*********&period2=**********&interval=1d
2025-05-30 00:38:50 [main] INFO  c.i.provider.YahooFinanceScraper - Saved HTML content to file: C:\Users\<USER>\Downloads\yahoo-finance-html\yahoo_finance_ACCS_1748536730.html
2025-05-30 00:38:50 [main] INFO  c.i.provider.YahooFinanceScraper - Read HTML content from file: C:\Users\<USER>\Downloads\yahoo-finance-html\yahoo_finance_ACCS_1748536730.html
2025-05-30 00:38:50 [main] INFO  c.i.provider.YahooFinanceScraper - Extracted 3523 OHLCV data points for ACCS
2025-05-30 00:38:51 [main] INFO  c.i.database.DatabaseManager - Saved 3523 OHLCV data points for ACCS
2025-05-30 00:38:51 [main] INFO  c.i.provider.YahooFinanceProvider - Downloaded and saved 3523 data points for ACCS
2025-05-30 00:38:51 [main] INFO  c.investment.MarketDataDownloaderApp - ✓ Successfully processed ACCS
2025-05-30 00:38:52 [main] INFO  c.investment.MarketDataDownloaderApp - Processing instrument 83/10009: ACDC (ProFrac Holding Corp.)
2025-05-30 00:38:52 [main] INFO  c.investment.MarketDataDownloaderApp - Downloading data for ACDC from 1963-05-30 to 2025-05-30 (attempt 1/4)
2025-05-30 00:38:52 [main] INFO  c.i.provider.YahooFinanceProvider - Requesting data from Yahoo Finance for symbol: ACDC, from 1963-05-30 to 2025-05-30
2025-05-30 00:38:52 [main] INFO  c.i.provider.YahooFinanceScraper - Scraping data from Yahoo Finance: https://finance.yahoo.com/quote/ACDC/history/?period1=-*********&period2=**********&interval=1d
2025-05-30 00:38:53 [main] INFO  c.i.provider.YahooFinanceScraper - Saved HTML content to file: C:\Users\<USER>\Downloads\yahoo-finance-html\yahoo_finance_ACDC_1748536733.html
2025-05-30 00:38:53 [main] INFO  c.i.provider.YahooFinanceScraper - Read HTML content from file: C:\Users\<USER>\Downloads\yahoo-finance-html\yahoo_finance_ACDC_1748536733.html
2025-05-30 00:38:53 [main] INFO  c.i.provider.YahooFinanceScraper - Extracted 763 OHLCV data points for ACDC
2025-05-30 00:38:53 [main] INFO  c.i.database.DatabaseManager - Saved 763 OHLCV data points for ACDC
2025-05-30 00:38:53 [main] INFO  c.i.provider.YahooFinanceProvider - Downloaded and saved 763 data points for ACDC
2025-05-30 00:38:53 [main] INFO  c.investment.MarketDataDownloaderApp - ✓ Successfully processed ACDC
2025-05-30 00:38:54 [main] INFO  c.investment.MarketDataDownloaderApp - Processing instrument 84/10009: ACEL (Accel Entertainment, Inc.)
2025-05-30 00:38:54 [main] INFO  c.investment.MarketDataDownloaderApp - Downloading data for ACEL from 1963-05-30 to 2025-05-30 (attempt 1/4)
2025-05-30 00:38:54 [main] INFO  c.i.provider.YahooFinanceProvider - Requesting data from Yahoo Finance for symbol: ACEL, from 1963-05-30 to 2025-05-30
2025-05-30 00:38:54 [main] INFO  c.i.provider.YahooFinanceScraper - Scraping data from Yahoo Finance: https://finance.yahoo.com/quote/ACEL/history/?period1=-*********&period2=**********&interval=1d
2025-05-30 00:38:57 [main] INFO  c.i.provider.YahooFinanceScraper - Saved HTML content to file: C:\Users\<USER>\Downloads\yahoo-finance-html\yahoo_finance_ACEL_1748536736.html
2025-05-30 00:38:57 [main] INFO  c.i.provider.YahooFinanceScraper - Read HTML content from file: C:\Users\<USER>\Downloads\yahoo-finance-html\yahoo_finance_ACEL_1748536736.html
2025-05-30 00:38:57 [main] INFO  c.i.provider.YahooFinanceScraper - Extracted 1794 OHLCV data points for ACEL
2025-05-30 00:38:57 [main] INFO  c.i.database.DatabaseManager - Saved 1794 OHLCV data points for ACEL
2025-05-30 00:38:57 [main] INFO  c.i.provider.YahooFinanceProvider - Downloaded and saved 1794 data points for ACEL
2025-05-30 00:38:57 [main] INFO  c.investment.MarketDataDownloaderApp - ✓ Successfully processed ACEL
2025-05-30 00:38:57 [main] INFO  c.investment.MarketDataDownloaderApp - Processing instrument 85/10009: ACET (Adicet Bio, Inc.)
2025-05-30 00:38:57 [main] INFO  c.investment.MarketDataDownloaderApp - Downloading data for ACET from 1963-05-30 to 2025-05-30 (attempt 1/4)
2025-05-30 00:38:58 [main] INFO  c.i.provider.YahooFinanceProvider - Requesting data from Yahoo Finance for symbol: ACET, from 1963-05-30 to 2025-05-30
2025-05-30 00:38:58 [main] INFO  c.i.provider.YahooFinanceScraper - Scraping data from Yahoo Finance: https://finance.yahoo.com/quote/ACET/history/?period1=-*********&period2=**********&interval=1d
2025-05-30 00:38:59 [main] INFO  c.i.provider.YahooFinanceScraper - Saved HTML content to file: C:\Users\<USER>\Downloads\yahoo-finance-html\yahoo_finance_ACET_1748536739.html
2025-05-30 00:38:59 [main] INFO  c.i.provider.YahooFinanceScraper - Read HTML content from file: C:\Users\<USER>\Downloads\yahoo-finance-html\yahoo_finance_ACET_1748536739.html
2025-05-30 00:38:59 [main] INFO  c.i.provider.YahooFinanceScraper - Extracted 1845 OHLCV data points for ACET
2025-05-30 00:39:00 [main] INFO  c.i.database.DatabaseManager - Saved 1845 OHLCV data points for ACET
2025-05-30 00:39:00 [main] INFO  c.i.provider.YahooFinanceProvider - Downloaded and saved 1845 data points for ACET
2025-05-30 00:39:00 [main] INFO  c.investment.MarketDataDownloaderApp - ✓ Successfully processed ACET
2025-05-30 00:39:00 [main] INFO  c.investment.MarketDataDownloaderApp - Processing instrument 86/10009: ACFN (ACORN ENERGY, INC.)
2025-05-30 00:39:00 [main] INFO  c.investment.MarketDataDownloaderApp - Downloading data for ACFN from 1963-05-30 to 2025-05-30 (attempt 1/4)
2025-05-30 00:39:00 [main] INFO  c.i.provider.YahooFinanceProvider - Requesting data from Yahoo Finance for symbol: ACFN, from 1963-05-30 to 2025-05-30
2025-05-30 00:39:00 [main] INFO  c.i.provider.YahooFinanceScraper - Scraping data from Yahoo Finance: https://finance.yahoo.com/quote/ACFN/history/?period1=-*********&period2=**********&interval=1d
2025-05-30 00:39:05 [main] INFO  c.i.provider.YahooFinanceScraper - Saved HTML content to file: C:\Users\<USER>\Downloads\yahoo-finance-html\yahoo_finance_ACFN_1748536745.html
2025-05-30 00:39:05 [main] INFO  c.i.provider.YahooFinanceScraper - Read HTML content from file: C:\Users\<USER>\Downloads\yahoo-finance-html\yahoo_finance_ACFN_1748536745.html
2025-05-30 00:39:05 [main] INFO  c.i.provider.YahooFinanceScraper - Extracted 8192 OHLCV data points for ACFN
2025-05-30 00:39:07 [main] INFO  c.i.database.DatabaseManager - Saved 8192 OHLCV data points for ACFN
2025-05-30 00:39:07 [main] INFO  c.i.provider.YahooFinanceProvider - Downloaded and saved 8192 data points for ACFN
2025-05-30 00:39:07 [main] INFO  c.investment.MarketDataDownloaderApp - ✓ Successfully processed ACFN
2025-05-30 00:39:08 [main] INFO  c.investment.MarketDataDownloaderApp - Processing instrument 87/10009: ACGL (ARCH CAPITAL GROUP LTD.)
2025-05-30 00:39:08 [main] INFO  c.investment.MarketDataDownloaderApp - Downloading data for ACGL from 1963-05-30 to 2025-05-30 (attempt 1/4)
2025-05-30 00:39:08 [main] INFO  c.i.provider.YahooFinanceProvider - Requesting data from Yahoo Finance for symbol: ACGL, from 1963-05-30 to 2025-05-30
2025-05-30 00:39:08 [main] INFO  c.i.provider.YahooFinanceScraper - Scraping data from Yahoo Finance: https://finance.yahoo.com/quote/ACGL/history/?period1=-*********&period2=**********&interval=1d
2025-05-30 00:39:12 [main] INFO  c.i.provider.YahooFinanceScraper - Saved HTML content to file: C:\Users\<USER>\Downloads\yahoo-finance-html\yahoo_finance_ACGL_1748536752.html
2025-05-30 00:39:12 [main] INFO  c.i.provider.YahooFinanceScraper - Read HTML content from file: C:\Users\<USER>\Downloads\yahoo-finance-html\yahoo_finance_ACGL_1748536752.html
2025-05-30 00:39:12 [main] INFO  c.i.provider.YahooFinanceScraper - Extracted 7435 OHLCV data points for ACGL
2025-05-30 00:39:14 [main] INFO  c.i.database.DatabaseManager - Saved 7435 OHLCV data points for ACGL
2025-05-30 00:39:14 [main] INFO  c.i.provider.YahooFinanceProvider - Downloaded and saved 7435 data points for ACGL
2025-05-30 00:39:14 [main] INFO  c.investment.MarketDataDownloaderApp - ✓ Successfully processed ACGL
2025-05-30 00:39:15 [main] INFO  c.investment.MarketDataDownloaderApp - Processing instrument 88/10009: ACGLN (ARCH CAPITAL GROUP LTD.)
2025-05-30 00:39:15 [main] INFO  c.investment.MarketDataDownloaderApp - Downloading data for ACGLN from 1963-05-30 to 2025-05-30 (attempt 1/4)
2025-05-30 00:39:15 [main] INFO  c.i.provider.YahooFinanceProvider - Requesting data from Yahoo Finance for symbol: ACGLN, from 1963-05-30 to 2025-05-30
2025-05-30 00:39:15 [main] INFO  c.i.provider.YahooFinanceScraper - Scraping data from Yahoo Finance: https://finance.yahoo.com/quote/ACGLN/history/?period1=-*********&period2=**********&interval=1d
2025-05-30 00:39:17 [main] INFO  c.i.provider.YahooFinanceScraper - Saved HTML content to file: C:\Users\<USER>\Downloads\yahoo-finance-html\yahoo_finance_ACGLN_1748536757.html
2025-05-30 00:39:17 [main] INFO  c.i.provider.YahooFinanceScraper - Read HTML content from file: C:\Users\<USER>\Downloads\yahoo-finance-html\yahoo_finance_ACGLN_1748536757.html
2025-05-30 00:39:17 [main] INFO  c.i.provider.YahooFinanceScraper - Extracted 1002 OHLCV data points for ACGLN
2025-05-30 00:39:17 [main] INFO  c.i.database.DatabaseManager - Saved 1002 OHLCV data points for ACGLN
2025-05-30 00:39:17 [main] INFO  c.i.provider.YahooFinanceProvider - Downloaded and saved 1002 data points for ACGLN
2025-05-30 00:39:17 [main] INFO  c.investment.MarketDataDownloaderApp - ✓ Successfully processed ACGLN
2025-05-30 00:39:18 [main] INFO  c.investment.MarketDataDownloaderApp - Processing instrument 89/10009: ACGLO (ARCH CAPITAL GROUP LTD.)
2025-05-30 00:39:18 [main] INFO  c.investment.MarketDataDownloaderApp - Downloading data for ACGLO from 1963-05-30 to 2025-05-30 (attempt 1/4)
2025-05-30 00:39:18 [main] INFO  c.i.provider.YahooFinanceProvider - Requesting data from Yahoo Finance for symbol: ACGLO, from 1963-05-30 to 2025-05-30
2025-05-30 00:39:18 [main] INFO  c.i.provider.YahooFinanceScraper - Scraping data from Yahoo Finance: https://finance.yahoo.com/quote/ACGLO/history/?period1=-*********&period2=**********&interval=1d
2025-05-30 00:39:20 [main] INFO  c.i.provider.YahooFinanceScraper - Saved HTML content to file: C:\Users\<USER>\Downloads\yahoo-finance-html\yahoo_finance_ACGLO_1748536760.html
2025-05-30 00:39:20 [main] INFO  c.i.provider.YahooFinanceScraper - Read HTML content from file: C:\Users\<USER>\Downloads\yahoo-finance-html\yahoo_finance_ACGLO_1748536760.html
2025-05-30 00:39:20 [main] INFO  c.i.provider.YahooFinanceScraper - Extracted 1954 OHLCV data points for ACGLO
2025-05-30 00:39:21 [main] INFO  c.i.database.DatabaseManager - Saved 1954 OHLCV data points for ACGLO
2025-05-30 00:39:21 [main] INFO  c.i.provider.YahooFinanceProvider - Downloaded and saved 1954 data points for ACGLO
2025-05-30 00:39:21 [main] INFO  c.investment.MarketDataDownloaderApp - ✓ Successfully processed ACGLO
2025-05-30 00:39:22 [main] INFO  c.investment.MarketDataDownloaderApp - Processing instrument 90/10009: ACHC (Acadia Healthcare Company, Inc.)
2025-05-30 00:39:22 [main] INFO  c.investment.MarketDataDownloaderApp - Downloading data for ACHC from 1963-05-30 to 2025-05-30 (attempt 1/4)
2025-05-30 00:39:22 [main] INFO  c.i.provider.YahooFinanceProvider - Requesting data from Yahoo Finance for symbol: ACHC, from 1963-05-30 to 2025-05-30
2025-05-30 00:39:22 [main] INFO  c.i.provider.YahooFinanceScraper - Scraping data from Yahoo Finance: https://finance.yahoo.com/quote/ACHC/history/?period1=-*********&period2=**********&interval=1d
2025-05-30 00:39:28 [main] INFO  c.i.provider.YahooFinanceScraper - Saved HTML content to file: C:\Users\<USER>\Downloads\yahoo-finance-html\yahoo_finance_ACHC_1748536768.html
2025-05-30 00:39:29 [main] INFO  c.i.provider.YahooFinanceScraper - Read HTML content from file: C:\Users\<USER>\Downloads\yahoo-finance-html\yahoo_finance_ACHC_1748536768.html
2025-05-30 00:39:29 [main] INFO  c.i.provider.YahooFinanceScraper - Extracted 7735 OHLCV data points for ACHC
2025-05-30 00:39:31 [main] INFO  c.i.database.DatabaseManager - Saved 7735 OHLCV data points for ACHC
2025-05-30 00:39:31 [main] INFO  c.i.provider.YahooFinanceProvider - Downloaded and saved 7735 data points for ACHC
2025-05-30 00:39:31 [main] INFO  c.investment.MarketDataDownloaderApp - ✓ Successfully processed ACHC
2025-05-30 00:39:31 [main] INFO  c.investment.MarketDataDownloaderApp - Processing instrument 91/10009: ACHR (Archer Aviation Inc.)
2025-05-30 00:39:31 [main] INFO  c.investment.MarketDataDownloaderApp - Downloading data for ACHR from 1963-05-30 to 2025-05-30 (attempt 1/4)
2025-05-30 00:39:31 [main] INFO  c.i.provider.YahooFinanceProvider - Requesting data from Yahoo Finance for symbol: ACHR, from 1963-05-30 to 2025-05-30
2025-05-30 00:39:31 [main] INFO  c.i.provider.YahooFinanceScraper - Scraping data from Yahoo Finance: https://finance.yahoo.com/quote/ACHR/history/?period1=-*********&period2=**********&interval=1d
2025-05-30 00:39:33 [main] INFO  c.i.provider.YahooFinanceScraper - Saved HTML content to file: C:\Users\<USER>\Downloads\yahoo-finance-html\yahoo_finance_ACHR_1748536773.html
2025-05-30 00:39:33 [main] INFO  c.i.provider.YahooFinanceScraper - Read HTML content from file: C:\Users\<USER>\Downloads\yahoo-finance-html\yahoo_finance_ACHR_1748536773.html
2025-05-30 00:39:33 [main] INFO  c.i.provider.YahooFinanceScraper - Extracted 1115 OHLCV data points for ACHR
2025-05-30 00:39:33 [main] INFO  c.i.database.DatabaseManager - Saved 1115 OHLCV data points for ACHR
2025-05-30 00:39:33 [main] INFO  c.i.provider.YahooFinanceProvider - Downloaded and saved 1115 data points for ACHR
2025-05-30 00:39:33 [main] INFO  c.investment.MarketDataDownloaderApp - ✓ Successfully processed ACHR
2025-05-30 00:39:34 [main] INFO  c.investment.MarketDataDownloaderApp - Processing instrument 92/10009: ACHR-WT (Archer Aviation Inc.)
2025-05-30 00:39:34 [main] INFO  c.investment.MarketDataDownloaderApp - Downloading data for ACHR-WT from 1963-05-30 to 2025-05-30 (attempt 1/4)
2025-05-30 00:39:34 [main] INFO  c.i.provider.YahooFinanceProvider - Requesting data from Yahoo Finance for symbol: ACHR-WT, from 1963-05-30 to 2025-05-30
2025-05-30 00:39:34 [main] INFO  c.i.provider.YahooFinanceScraper - Scraping data from Yahoo Finance: https://finance.yahoo.com/quote/ACHR-WT/history/?period1=-*********&period2=**********&interval=1d
2025-05-30 00:39:35 [main] INFO  c.i.provider.YahooFinanceScraper - Saved HTML content to file: C:\Users\<USER>\Downloads\yahoo-finance-html\yahoo_finance_ACHR-WT_1748536775.html
2025-05-30 00:39:35 [main] INFO  c.i.provider.YahooFinanceScraper - Read HTML content from file: C:\Users\<USER>\Downloads\yahoo-finance-html\yahoo_finance_ACHR-WT_1748536775.html
2025-05-30 00:39:35 [main] INFO  c.i.provider.YahooFinanceScraper - Extracted 1 OHLCV data points for ACHR-WT
2025-05-30 00:39:35 [main] INFO  c.i.database.DatabaseManager - Saved 1 OHLCV data points for ACHR-WT
2025-05-30 00:39:35 [main] INFO  c.i.provider.YahooFinanceProvider - Downloaded and saved 1 data points for ACHR-WT
2025-05-30 00:39:35 [main] INFO  c.investment.MarketDataDownloaderApp - ✓ Successfully processed ACHR-WT
2025-05-30 00:39:36 [main] INFO  c.investment.MarketDataDownloaderApp - Processing instrument 93/10009: ACHV (ACHIEVE LIFE SCIENCES, INC.)
2025-05-30 00:39:36 [main] INFO  c.investment.MarketDataDownloaderApp - Downloading data for ACHV from 1963-05-30 to 2025-05-30 (attempt 1/4)
2025-05-30 00:39:36 [main] INFO  c.i.provider.YahooFinanceProvider - Requesting data from Yahoo Finance for symbol: ACHV, from 1963-05-30 to 2025-05-30
2025-05-30 00:39:36 [main] INFO  c.i.provider.YahooFinanceScraper - Scraping data from Yahoo Finance: https://finance.yahoo.com/quote/ACHV/history/?period1=-*********&period2=**********&interval=1d
2025-05-30 00:39:42 [main] INFO  c.i.provider.YahooFinanceScraper - Saved HTML content to file: C:\Users\<USER>\Downloads\yahoo-finance-html\yahoo_finance_ACHV_1748536782.html
2025-05-30 00:39:42 [main] INFO  c.i.provider.YahooFinanceScraper - Read HTML content from file: C:\Users\<USER>\Downloads\yahoo-finance-html\yahoo_finance_ACHV_1748536782.html
2025-05-30 00:39:42 [main] INFO  c.i.provider.YahooFinanceScraper - Extracted 7011 OHLCV data points for ACHV
2025-05-30 00:39:44 [main] INFO  c.i.database.DatabaseManager - Saved 7011 OHLCV data points for ACHV
2025-05-30 00:39:44 [main] INFO  c.i.provider.YahooFinanceProvider - Downloaded and saved 7011 data points for ACHV
2025-05-30 00:39:44 [main] INFO  c.investment.MarketDataDownloaderApp - ✓ Successfully processed ACHV
2025-05-30 00:39:44 [main] INFO  c.investment.MarketDataDownloaderApp - Processing instrument 94/10009: ACI (Albertsons Companies, Inc.)
2025-05-30 00:39:44 [main] INFO  c.investment.MarketDataDownloaderApp - Downloading data for ACI from 1963-05-30 to 2025-05-30 (attempt 1/4)
2025-05-30 00:39:44 [main] INFO  c.i.provider.YahooFinanceProvider - Requesting data from Yahoo Finance for symbol: ACI, from 1963-05-30 to 2025-05-30
2025-05-30 00:39:44 [main] INFO  c.i.provider.YahooFinanceScraper - Scraping data from Yahoo Finance: https://finance.yahoo.com/quote/ACI/history/?period1=-*********&period2=**********&interval=1d
2025-05-30 00:39:46 [main] INFO  c.i.provider.YahooFinanceScraper - Saved HTML content to file: C:\Users\<USER>\Downloads\yahoo-finance-html\yahoo_finance_ACI_1748536786.html
2025-05-30 00:39:46 [main] INFO  c.i.provider.YahooFinanceScraper - Read HTML content from file: C:\Users\<USER>\Downloads\yahoo-finance-html\yahoo_finance_ACI_1748536786.html
2025-05-30 00:39:46 [main] INFO  c.i.provider.YahooFinanceScraper - Extracted 1237 OHLCV data points for ACI
2025-05-30 00:39:47 [main] INFO  c.i.database.DatabaseManager - Saved 1237 OHLCV data points for ACI
2025-05-30 00:39:47 [main] INFO  c.i.provider.YahooFinanceProvider - Downloaded and saved 1237 data points for ACI
2025-05-30 00:39:47 [main] INFO  c.investment.MarketDataDownloaderApp - ✓ Successfully processed ACI
2025-05-30 00:39:47 [main] INFO  c.investment.MarketDataDownloaderApp - Processing instrument 95/10009: ACIC (AMERICAN COASTAL INSURANCE Corp)
2025-05-30 00:39:47 [main] INFO  c.investment.MarketDataDownloaderApp - Downloading data for ACIC from 1963-05-30 to 2025-05-30 (attempt 1/4)
2025-05-30 00:39:47 [main] INFO  c.i.provider.YahooFinanceProvider - Requesting data from Yahoo Finance for symbol: ACIC, from 1963-05-30 to 2025-05-30
2025-05-30 00:39:47 [main] INFO  c.i.provider.YahooFinanceScraper - Scraping data from Yahoo Finance: https://finance.yahoo.com/quote/ACIC/history/?period1=-*********&period2=**********&interval=1d
2025-05-30 00:39:50 [main] INFO  c.i.provider.YahooFinanceScraper - Saved HTML content to file: C:\Users\<USER>\Downloads\yahoo-finance-html\yahoo_finance_ACIC_1748536790.html
2025-05-30 00:39:50 [main] INFO  c.i.provider.YahooFinanceScraper - Read HTML content from file: C:\Users\<USER>\Downloads\yahoo-finance-html\yahoo_finance_ACIC_1748536790.html
2025-05-30 00:39:50 [main] INFO  c.i.provider.YahooFinanceScraper - Extracted 3470 OHLCV data points for ACIC
2025-05-30 00:39:51 [main] INFO  c.i.database.DatabaseManager - Saved 3470 OHLCV data points for ACIC
2025-05-30 00:39:51 [main] INFO  c.i.provider.YahooFinanceProvider - Downloaded and saved 3470 data points for ACIC
2025-05-30 00:39:51 [main] INFO  c.investment.MarketDataDownloaderApp - ✓ Successfully processed ACIC
2025-05-30 00:39:52 [main] INFO  c.investment.MarketDataDownloaderApp - Processing instrument 96/10009: ACIU (AC Immune SA)
2025-05-30 00:39:52 [main] INFO  c.investment.MarketDataDownloaderApp - Downloading data for ACIU from 1963-05-30 to 2025-05-30 (attempt 1/4)
2025-05-30 00:39:52 [main] INFO  c.i.provider.YahooFinanceProvider - Requesting data from Yahoo Finance for symbol: ACIU, from 1963-05-30 to 2025-05-30
2025-05-30 00:39:52 [main] INFO  c.i.provider.YahooFinanceScraper - Scraping data from Yahoo Finance: https://finance.yahoo.com/quote/ACIU/history/?period1=-*********&period2=**********&interval=1d
2025-05-30 00:39:54 [main] INFO  c.i.provider.YahooFinanceScraper - Saved HTML content to file: C:\Users\<USER>\Downloads\yahoo-finance-html\yahoo_finance_ACIU_1748536794.html
2025-05-30 00:39:54 [main] INFO  c.i.provider.YahooFinanceScraper - Read HTML content from file: C:\Users\<USER>\Downloads\yahoo-finance-html\yahoo_finance_ACIU_1748536794.html
2025-05-30 00:39:54 [main] INFO  c.i.provider.YahooFinanceScraper - Extracted 2182 OHLCV data points for ACIU
2025-05-30 00:39:55 [main] INFO  c.i.database.DatabaseManager - Saved 2182 OHLCV data points for ACIU
2025-05-30 00:39:55 [main] INFO  c.i.provider.YahooFinanceProvider - Downloaded and saved 2182 data points for ACIU
2025-05-30 00:39:55 [main] INFO  c.investment.MarketDataDownloaderApp - ✓ Successfully processed ACIU
2025-05-30 00:39:55 [main] INFO  c.investment.MarketDataDownloaderApp - Processing instrument 97/10009: ACIW (ACI WORLDWIDE, INC.)
2025-05-30 00:39:55 [main] INFO  c.investment.MarketDataDownloaderApp - Downloading data for ACIW from 1963-05-30 to 2025-05-30 (attempt 1/4)
2025-05-30 00:39:55 [main] INFO  c.i.provider.YahooFinanceProvider - Requesting data from Yahoo Finance for symbol: ACIW, from 1963-05-30 to 2025-05-30
2025-05-30 00:39:55 [main] INFO  c.i.provider.YahooFinanceScraper - Scraping data from Yahoo Finance: https://finance.yahoo.com/quote/ACIW/history/?period1=-*********&period2=**********&interval=1d
2025-05-30 00:40:02 [main] INFO  c.i.provider.YahooFinanceScraper - Saved HTML content to file: C:\Users\<USER>\Downloads\yahoo-finance-html\yahoo_finance_ACIW_1748536802.html
2025-05-30 00:40:02 [main] INFO  c.i.provider.YahooFinanceScraper - Read HTML content from file: C:\Users\<USER>\Downloads\yahoo-finance-html\yahoo_finance_ACIW_1748536802.html
2025-05-30 00:40:02 [main] INFO  c.i.provider.YahooFinanceScraper - Extracted 7616 OHLCV data points for ACIW
2025-05-30 00:40:04 [main] INFO  c.i.database.DatabaseManager - Saved 7616 OHLCV data points for ACIW
2025-05-30 00:40:04 [main] INFO  c.i.provider.YahooFinanceProvider - Downloaded and saved 7616 data points for ACIW
2025-05-30 00:40:04 [main] INFO  c.investment.MarketDataDownloaderApp - ✓ Successfully processed ACIW
2025-05-30 00:40:04 [main] INFO  c.investment.MarketDataDownloaderApp - Processing instrument 98/10009: ACKRF (American Creek Resources Ltd.)
2025-05-30 00:40:04 [main] INFO  c.investment.MarketDataDownloaderApp - Downloading data for ACKRF from 1963-05-30 to 2025-05-30 (attempt 1/4)
2025-05-30 00:40:04 [main] INFO  c.i.provider.YahooFinanceProvider - Requesting data from Yahoo Finance for symbol: ACKRF, from 1963-05-30 to 2025-05-30
2025-05-30 00:40:04 [main] INFO  c.i.provider.YahooFinanceScraper - Scraping data from Yahoo Finance: https://finance.yahoo.com/quote/ACKRF/history/?period1=-*********&period2=**********&interval=1d
2025-05-30 00:40:07 [main] INFO  c.i.provider.YahooFinanceScraper - Saved HTML content to file: C:\Users\<USER>\Downloads\yahoo-finance-html\yahoo_finance_ACKRF_1748536807.html
2025-05-30 00:40:07 [main] INFO  c.i.provider.YahooFinanceScraper - Read HTML content from file: C:\Users\<USER>\Downloads\yahoo-finance-html\yahoo_finance_ACKRF_1748536807.html
2025-05-30 00:40:07 [main] INFO  c.i.provider.YahooFinanceScraper - Extracted 2427 OHLCV data points for ACKRF
2025-05-30 00:40:08 [main] INFO  c.i.database.DatabaseManager - Saved 2427 OHLCV data points for ACKRF
2025-05-30 00:40:08 [main] INFO  c.i.provider.YahooFinanceProvider - Downloaded and saved 2427 data points for ACKRF
2025-05-30 00:40:08 [main] INFO  c.investment.MarketDataDownloaderApp - ✓ Successfully processed ACKRF
2025-05-30 00:40:09 [main] INFO  c.investment.MarketDataDownloaderApp - Processing instrument 99/10009: ACLEW (Alternus Clean Energy, Inc.)
2025-05-30 00:40:09 [main] INFO  c.investment.MarketDataDownloaderApp - Downloading data for ACLEW from 1963-05-30 to 2025-05-30 (attempt 1/4)
2025-05-30 00:40:09 [main] INFO  c.i.provider.YahooFinanceProvider - Requesting data from Yahoo Finance for symbol: ACLEW, from 1963-05-30 to 2025-05-30
2025-05-30 00:40:09 [main] INFO  c.i.provider.YahooFinanceScraper - Scraping data from Yahoo Finance: https://finance.yahoo.com/quote/ACLEW/history/?period1=-*********&period2=**********&interval=1d
2025-05-30 00:40:10 [main] INFO  c.i.provider.YahooFinanceScraper - Saved HTML content to file: C:\Users\<USER>\Downloads\yahoo-finance-html\yahoo_finance_ACLEW_1748536810.html
2025-05-30 00:40:10 [main] INFO  c.i.provider.YahooFinanceScraper - Read HTML content from file: C:\Users\<USER>\Downloads\yahoo-finance-html\yahoo_finance_ACLEW_1748536810.html
2025-05-30 00:40:10 [main] WARN  c.i.provider.YahooFinanceScraper - Could not find historical prices rows for ACLEW
2025-05-30 00:40:10 [main] INFO  c.i.provider.YahooFinanceProvider - Downloaded and saved 0 data points for ACLEW
2025-05-30 00:40:10 [main] INFO  c.investment.MarketDataDownloaderApp - ✓ Successfully processed ACLEW
2025-05-30 00:40:10 [main] INFO  c.investment.MarketDataDownloaderApp - Processing instrument 100/10009: ACLS (AXCELIS TECHNOLOGIES INC)
2025-05-30 00:40:10 [main] INFO  c.investment.MarketDataDownloaderApp - Downloading data for ACLS from 1963-05-30 to 2025-05-30 (attempt 1/4)
2025-05-30 00:40:10 [main] INFO  c.i.provider.YahooFinanceProvider - Requesting data from Yahoo Finance for symbol: ACLS, from 1963-05-30 to 2025-05-30
2025-05-30 00:40:10 [main] INFO  c.i.provider.YahooFinanceScraper - Scraping data from Yahoo Finance: https://finance.yahoo.com/quote/ACLS/history/?period1=-*********&period2=**********&interval=1d
2025-05-30 00:40:15 [main] INFO  c.i.provider.YahooFinanceScraper - Saved HTML content to file: C:\Users\<USER>\Downloads\yahoo-finance-html\yahoo_finance_ACLS_1748536815.html
2025-05-30 00:40:15 [main] INFO  c.i.provider.YahooFinanceScraper - Read HTML content from file: C:\Users\<USER>\Downloads\yahoo-finance-html\yahoo_finance_ACLS_1748536815.html
2025-05-30 00:40:15 [main] INFO  c.i.provider.YahooFinanceScraper - Extracted 6259 OHLCV data points for ACLS
2025-05-30 00:40:17 [main] INFO  c.i.database.DatabaseManager - Saved 6259 OHLCV data points for ACLS
2025-05-30 00:40:17 [main] INFO  c.i.provider.YahooFinanceProvider - Downloaded and saved 6259 data points for ACLS
2025-05-30 00:40:17 [main] INFO  c.investment.MarketDataDownloaderApp - ✓ Successfully processed ACLS
2025-05-30 00:40:17 [main] INFO  c.investment.MarketDataDownloaderApp - Processing instrument 101/10009: ACLX (Arcellx, Inc.)
2025-05-30 00:40:17 [main] INFO  c.investment.MarketDataDownloaderApp - Downloading data for ACLX from 1963-05-30 to 2025-05-30 (attempt 1/4)
2025-05-30 00:40:17 [main] INFO  c.i.provider.YahooFinanceProvider - Requesting data from Yahoo Finance for symbol: ACLX, from 1963-05-30 to 2025-05-30
2025-05-30 00:40:17 [main] INFO  c.i.provider.YahooFinanceScraper - Scraping data from Yahoo Finance: https://finance.yahoo.com/quote/ACLX/history/?period1=-*********&period2=**********&interval=1d
2025-05-30 00:40:19 [main] INFO  c.i.provider.YahooFinanceScraper - Saved HTML content to file: C:\Users\<USER>\Downloads\yahoo-finance-html\yahoo_finance_ACLX_1748536819.html
2025-05-30 00:40:19 [main] INFO  c.i.provider.YahooFinanceScraper - Read HTML content from file: C:\Users\<USER>\Downloads\yahoo-finance-html\yahoo_finance_ACLX_1748536819.html
2025-05-30 00:40:19 [main] INFO  c.i.provider.YahooFinanceScraper - Extracted 831 OHLCV data points for ACLX
2025-05-30 00:40:19 [main] INFO  c.i.database.DatabaseManager - Saved 831 OHLCV data points for ACLX
2025-05-30 00:40:19 [main] INFO  c.i.provider.YahooFinanceProvider - Downloaded and saved 831 data points for ACLX
2025-05-30 00:40:19 [main] INFO  c.investment.MarketDataDownloaderApp - ✓ Successfully processed ACLX
2025-05-30 00:40:20 [main] INFO  c.investment.MarketDataDownloaderApp - Processing instrument 102/10009: ACM (AECOM)
2025-05-30 00:40:20 [main] INFO  c.investment.MarketDataDownloaderApp - Downloading data for ACM from 1963-05-30 to 2025-05-30 (attempt 1/4)
2025-05-30 00:40:20 [main] INFO  c.i.provider.YahooFinanceProvider - Requesting data from Yahoo Finance for symbol: ACM, from 1963-05-30 to 2025-05-30
2025-05-30 00:40:20 [main] INFO  c.i.provider.YahooFinanceScraper - Scraping data from Yahoo Finance: https://finance.yahoo.com/quote/ACM/history/?period1=-*********&period2=**********&interval=1d
2025-05-30 00:40:25 [main] INFO  c.i.provider.YahooFinanceScraper - Saved HTML content to file: C:\Users\<USER>\Downloads\yahoo-finance-html\yahoo_finance_ACM_1748536825.html
2025-05-30 00:40:25 [main] INFO  c.i.provider.YahooFinanceScraper - Read HTML content from file: C:\Users\<USER>\Downloads\yahoo-finance-html\yahoo_finance_ACM_1748536825.html
2025-05-30 00:40:25 [main] INFO  c.i.provider.YahooFinanceScraper - Extracted 4543 OHLCV data points for ACM
2025-05-30 00:40:26 [main] INFO  c.i.database.DatabaseManager - Saved 4543 OHLCV data points for ACM
2025-05-30 00:40:26 [main] INFO  c.i.provider.YahooFinanceProvider - Downloaded and saved 4543 data points for ACM
2025-05-30 00:40:26 [main] INFO  c.investment.MarketDataDownloaderApp - ✓ Successfully processed ACM
2025-05-30 00:40:27 [main] INFO  c.investment.MarketDataDownloaderApp - Processing instrument 103/10009: ACMR (ACM Research, Inc.)
2025-05-30 00:40:27 [main] INFO  c.investment.MarketDataDownloaderApp - Downloading data for ACMR from 1963-05-30 to 2025-05-30 (attempt 1/4)
2025-05-30 00:40:27 [main] INFO  c.i.provider.YahooFinanceProvider - Requesting data from Yahoo Finance for symbol: ACMR, from 1963-05-30 to 2025-05-30
2025-05-30 00:40:27 [main] INFO  c.i.provider.YahooFinanceScraper - Scraping data from Yahoo Finance: https://finance.yahoo.com/quote/ACMR/history/?period1=-*********&period2=**********&interval=1d
2025-05-30 00:40:29 [main] INFO  c.i.provider.YahooFinanceScraper - Saved HTML content to file: C:\Users\<USER>\Downloads\yahoo-finance-html\yahoo_finance_ACMR_1748536829.html
2025-05-30 00:40:29 [main] INFO  c.i.provider.YahooFinanceScraper - Read HTML content from file: C:\Users\<USER>\Downloads\yahoo-finance-html\yahoo_finance_ACMR_1748536829.html
2025-05-30 00:40:29 [main] INFO  c.i.provider.YahooFinanceScraper - Extracted 1902 OHLCV data points for ACMR
2025-05-30 00:40:29 [main] INFO  c.i.database.DatabaseManager - Saved 1902 OHLCV data points for ACMR
2025-05-30 00:40:29 [main] INFO  c.i.provider.YahooFinanceProvider - Downloaded and saved 1902 data points for ACMR
2025-05-30 00:40:29 [main] INFO  c.investment.MarketDataDownloaderApp - ✓ Successfully processed ACMR
2025-05-30 00:40:30 [main] INFO  c.investment.MarketDataDownloaderApp - Processing instrument 104/10009: ACN (Accenture plc)
2025-05-30 00:40:30 [main] INFO  c.investment.MarketDataDownloaderApp - Downloading data for ACN from 1963-05-30 to 2025-05-30 (attempt 1/4)
2025-05-30 00:40:30 [main] INFO  c.i.provider.YahooFinanceProvider - Requesting data from Yahoo Finance for symbol: ACN, from 1963-05-30 to 2025-05-30
2025-05-30 00:40:30 [main] INFO  c.i.provider.YahooFinanceScraper - Scraping data from Yahoo Finance: https://finance.yahoo.com/quote/ACN/history/?period1=-*********&period2=**********&interval=1d
2025-05-30 00:40:33 [main] INFO  c.i.provider.YahooFinanceScraper - Saved HTML content to file: C:\Users\<USER>\Downloads\yahoo-finance-html\yahoo_finance_ACN_1748536833.html
2025-05-30 00:40:33 [main] INFO  c.i.provider.YahooFinanceScraper - Read HTML content from file: C:\Users\<USER>\Downloads\yahoo-finance-html\yahoo_finance_ACN_1748536833.html
2025-05-30 00:40:33 [main] INFO  c.i.provider.YahooFinanceScraper - Extracted 6001 OHLCV data points for ACN
2025-05-30 00:40:35 [main] INFO  c.i.database.DatabaseManager - Saved 6001 OHLCV data points for ACN
2025-05-30 00:40:35 [main] INFO  c.i.provider.YahooFinanceProvider - Downloaded and saved 6001 data points for ACN
2025-05-30 00:40:35 [main] INFO  c.investment.MarketDataDownloaderApp - ✓ Successfully processed ACN
2025-05-30 00:40:36 [main] INFO  c.investment.MarketDataDownloaderApp - Processing instrument 105/10009: ACNB (ACNB CORP)
2025-05-30 00:40:36 [main] INFO  c.investment.MarketDataDownloaderApp - Downloading data for ACNB from 1963-05-30 to 2025-05-30 (attempt 1/4)
2025-05-30 00:40:36 [main] INFO  c.i.provider.YahooFinanceProvider - Requesting data from Yahoo Finance for symbol: ACNB, from 1963-05-30 to 2025-05-30
2025-05-30 00:40:36 [main] INFO  c.i.provider.YahooFinanceScraper - Scraping data from Yahoo Finance: https://finance.yahoo.com/quote/ACNB/history/?period1=-*********&period2=**********&interval=1d
2025-05-30 00:40:40 [main] INFO  c.i.provider.YahooFinanceScraper - Saved HTML content to file: C:\Users\<USER>\Downloads\yahoo-finance-html\yahoo_finance_ACNB_1748536840.html
2025-05-30 00:40:40 [main] INFO  c.i.provider.YahooFinanceScraper - Read HTML content from file: C:\Users\<USER>\Downloads\yahoo-finance-html\yahoo_finance_ACNB_1748536840.html
2025-05-30 00:40:40 [main] INFO  c.i.provider.YahooFinanceScraper - Extracted 6752 OHLCV data points for ACNB
2025-05-30 00:40:42 [main] INFO  c.i.database.DatabaseManager - Saved 6752 OHLCV data points for ACNB
2025-05-30 00:40:42 [main] INFO  c.i.provider.YahooFinanceProvider - Downloaded and saved 6752 data points for ACNB
2025-05-30 00:40:42 [main] INFO  c.investment.MarketDataDownloaderApp - ✓ Successfully processed ACNB
2025-05-30 00:40:42 [main] INFO  c.investment.MarketDataDownloaderApp - Processing instrument 106/10009: ACNT (ASCENT INDUSTRIES CO.)
2025-05-30 00:40:42 [main] INFO  c.investment.MarketDataDownloaderApp - Downloading data for ACNT from 1963-05-30 to 2025-05-30 (attempt 1/4)
2025-05-30 00:40:42 [main] INFO  c.i.provider.YahooFinanceProvider - Requesting data from Yahoo Finance for symbol: ACNT, from 1963-05-30 to 2025-05-30
2025-05-30 00:40:42 [main] INFO  c.i.provider.YahooFinanceScraper - Scraping data from Yahoo Finance: https://finance.yahoo.com/quote/ACNT/history/?period1=-*********&period2=**********&interval=1d
2025-05-30 00:40:47 [main] INFO  c.i.provider.YahooFinanceScraper - Saved HTML content to file: C:\Users\<USER>\Downloads\yahoo-finance-html\yahoo_finance_ACNT_1748536847.html
2025-05-30 00:40:48 [main] INFO  c.i.provider.YahooFinanceScraper - Read HTML content from file: C:\Users\<USER>\Downloads\yahoo-finance-html\yahoo_finance_ACNT_1748536847.html
2025-05-30 00:40:48 [main] INFO  c.i.provider.YahooFinanceScraper - Extracted 11022 OHLCV data points for ACNT
2025-05-30 00:40:51 [main] INFO  c.i.database.DatabaseManager - Saved 11022 OHLCV data points for ACNT
2025-05-30 00:40:51 [main] INFO  c.i.provider.YahooFinanceProvider - Downloaded and saved 11022 data points for ACNT
2025-05-30 00:40:51 [main] INFO  c.investment.MarketDataDownloaderApp - ✓ Successfully processed ACNT
2025-05-30 00:40:51 [main] INFO  c.investment.MarketDataDownloaderApp - Processing instrument 107/10009: ACOG (Alpha Cognition Inc.)
2025-05-30 00:40:51 [main] INFO  c.investment.MarketDataDownloaderApp - Downloading data for ACOG from 1963-05-30 to 2025-05-30 (attempt 1/4)
2025-05-30 00:40:51 [main] INFO  c.i.provider.YahooFinanceProvider - Requesting data from Yahoo Finance for symbol: ACOG, from 1963-05-30 to 2025-05-30
2025-05-30 00:40:51 [main] INFO  c.i.provider.YahooFinanceScraper - Scraping data from Yahoo Finance: https://finance.yahoo.com/quote/ACOG/history/?period1=-*********&period2=**********&interval=1d
2025-05-30 00:40:53 [main] INFO  c.i.provider.YahooFinanceScraper - Saved HTML content to file: C:\Users\<USER>\Downloads\yahoo-finance-html\yahoo_finance_ACOG_1748536853.html
2025-05-30 00:40:53 [main] INFO  c.i.provider.YahooFinanceScraper - Read HTML content from file: C:\Users\<USER>\Downloads\yahoo-finance-html\yahoo_finance_ACOG_1748536853.html
2025-05-30 00:40:53 [main] INFO  c.i.provider.YahooFinanceScraper - Extracted 135 OHLCV data points for ACOG
2025-05-30 00:40:53 [main] INFO  c.i.database.DatabaseManager - Saved 135 OHLCV data points for ACOG
2025-05-30 00:40:53 [main] INFO  c.i.provider.YahooFinanceProvider - Downloaded and saved 135 data points for ACOG
2025-05-30 00:40:53 [main] INFO  c.investment.MarketDataDownloaderApp - ✓ Successfully processed ACOG
2025-05-30 00:40:53 [main] INFO  c.investment.MarketDataDownloaderApp - Processing instrument 108/10009: ACON (Aclarion, Inc.)
2025-05-30 00:40:53 [main] INFO  c.investment.MarketDataDownloaderApp - Downloading data for ACON from 1963-05-30 to 2025-05-30 (attempt 1/4)
2025-05-30 00:40:53 [main] INFO  c.i.provider.YahooFinanceProvider - Requesting data from Yahoo Finance for symbol: ACON, from 1963-05-30 to 2025-05-30
2025-05-30 00:40:53 [main] INFO  c.i.provider.YahooFinanceScraper - Scraping data from Yahoo Finance: https://finance.yahoo.com/quote/ACON/history/?period1=-*********&period2=**********&interval=1d
2025-05-30 00:40:55 [main] INFO  c.i.provider.YahooFinanceScraper - Saved HTML content to file: C:\Users\<USER>\Downloads\yahoo-finance-html\yahoo_finance_ACON_1748536855.html
2025-05-30 00:40:55 [main] INFO  c.i.provider.YahooFinanceScraper - Read HTML content from file: C:\Users\<USER>\Downloads\yahoo-finance-html\yahoo_finance_ACON_1748536855.html
2025-05-30 00:40:55 [main] INFO  c.i.provider.YahooFinanceScraper - Extracted 510 OHLCV data points for ACON
2025-05-30 00:40:55 [main] INFO  c.i.database.DatabaseManager - Saved 510 OHLCV data points for ACON
2025-05-30 00:40:55 [main] INFO  c.i.provider.YahooFinanceProvider - Downloaded and saved 510 data points for ACON
2025-05-30 00:40:55 [main] INFO  c.investment.MarketDataDownloaderApp - ✓ Successfully processed ACON
2025-05-30 00:40:56 [main] INFO  c.investment.MarketDataDownloaderApp - Processing instrument 109/10009: ACONW (Aclarion, Inc.)
2025-05-30 00:40:56 [main] INFO  c.investment.MarketDataDownloaderApp - Downloading data for ACONW from 1963-05-30 to 2025-05-30 (attempt 1/4)
2025-05-30 00:40:56 [main] INFO  c.i.provider.YahooFinanceProvider - Requesting data from Yahoo Finance for symbol: ACONW, from 1963-05-30 to 2025-05-30
2025-05-30 00:40:56 [main] INFO  c.i.provider.YahooFinanceScraper - Scraping data from Yahoo Finance: https://finance.yahoo.com/quote/ACONW/history/?period1=-*********&period2=**********&interval=1d
2025-05-30 00:40:57 [main] INFO  c.i.provider.YahooFinanceScraper - Saved HTML content to file: C:\Users\<USER>\Downloads\yahoo-finance-html\yahoo_finance_ACONW_1748536857.html
2025-05-30 00:40:57 [main] INFO  c.i.provider.YahooFinanceScraper - Read HTML content from file: C:\Users\<USER>\Downloads\yahoo-finance-html\yahoo_finance_ACONW_1748536857.html
2025-05-30 00:40:57 [main] INFO  c.i.provider.YahooFinanceScraper - Extracted 1 OHLCV data points for ACONW
2025-05-30 00:40:57 [main] INFO  c.i.database.DatabaseManager - Saved 1 OHLCV data points for ACONW
2025-05-30 00:40:57 [main] INFO  c.i.provider.YahooFinanceProvider - Downloaded and saved 1 data points for ACONW
2025-05-30 00:40:57 [main] INFO  c.investment.MarketDataDownloaderApp - ✓ Successfully processed ACONW
2025-05-30 00:40:58 [main] INFO  c.investment.MarketDataDownloaderApp - Processing instrument 110/10009: ACP (abrdn Income Credit Strategies Fund)
2025-05-30 00:40:58 [main] INFO  c.investment.MarketDataDownloaderApp - Downloading data for ACP from 1963-05-30 to 2025-05-30 (attempt 1/4)
2025-05-30 00:40:58 [main] INFO  c.i.provider.YahooFinanceProvider - Requesting data from Yahoo Finance for symbol: ACP, from 1963-05-30 to 2025-05-30
2025-05-30 00:40:58 [main] INFO  c.i.provider.YahooFinanceScraper - Scraping data from Yahoo Finance: https://finance.yahoo.com/quote/ACP/history/?period1=-*********&period2=**********&interval=1d
2025-05-30 00:41:01 [main] INFO  c.i.provider.YahooFinanceScraper - Saved HTML content to file: C:\Users\<USER>\Downloads\yahoo-finance-html\yahoo_finance_ACP_1748536861.html
2025-05-30 00:41:01 [main] INFO  c.i.provider.YahooFinanceScraper - Read HTML content from file: C:\Users\<USER>\Downloads\yahoo-finance-html\yahoo_finance_ACP_1748536861.html
2025-05-30 00:41:01 [main] INFO  c.i.provider.YahooFinanceScraper - Extracted 3606 OHLCV data points for ACP
2025-05-30 00:41:02 [main] INFO  c.i.database.DatabaseManager - Saved 3606 OHLCV data points for ACP
2025-05-30 00:41:02 [main] INFO  c.i.provider.YahooFinanceProvider - Downloaded and saved 3606 data points for ACP
2025-05-30 00:41:02 [main] INFO  c.investment.MarketDataDownloaderApp - ✓ Successfully processed ACP
2025-05-30 00:41:02 [main] INFO  c.investment.MarketDataDownloaderApp - Processing instrument 111/10009: ACP-PA (abrdn Income Credit Strategies Fund)
2025-05-30 00:41:02 [main] INFO  c.investment.MarketDataDownloaderApp - Downloading data for ACP-PA from 1963-05-30 to 2025-05-30 (attempt 1/4)
2025-05-30 00:41:02 [main] INFO  c.i.provider.YahooFinanceProvider - Requesting data from Yahoo Finance for symbol: ACP-PA, from 1963-05-30 to 2025-05-30
2025-05-30 00:41:02 [main] INFO  c.i.provider.YahooFinanceScraper - Scraping data from Yahoo Finance: https://finance.yahoo.com/quote/ACP-PA/history/?period1=-*********&period2=**********&interval=1d
2025-05-30 00:41:04 [main] INFO  c.i.provider.YahooFinanceScraper - Saved HTML content to file: C:\Users\<USER>\Downloads\yahoo-finance-html\yahoo_finance_ACP-PA_1748536864.html
2025-05-30 00:41:04 [main] INFO  c.i.provider.YahooFinanceScraper - Read HTML content from file: C:\Users\<USER>\Downloads\yahoo-finance-html\yahoo_finance_ACP-PA_1748536864.html
2025-05-30 00:41:04 [main] INFO  c.i.provider.YahooFinanceScraper - Extracted 950 OHLCV data points for ACP-PA
2025-05-30 00:41:05 [main] INFO  c.i.database.DatabaseManager - Saved 950 OHLCV data points for ACP-PA
2025-05-30 00:41:05 [main] INFO  c.i.provider.YahooFinanceProvider - Downloaded and saved 950 data points for ACP-PA
2025-05-30 00:41:05 [main] INFO  c.investment.MarketDataDownloaderApp - ✓ Successfully processed ACP-PA
2025-05-30 00:41:05 [main] INFO  c.investment.MarketDataDownloaderApp - Processing instrument 112/10009: ACPS (AC Partners, Inc.)
2025-05-30 00:41:05 [main] INFO  c.investment.MarketDataDownloaderApp - Downloading data for ACPS from 1963-05-30 to 2025-05-30 (attempt 1/4)
2025-05-30 00:41:05 [main] INFO  c.i.provider.YahooFinanceProvider - Requesting data from Yahoo Finance for symbol: ACPS, from 1963-05-30 to 2025-05-30
2025-05-30 00:41:05 [main] INFO  c.i.provider.YahooFinanceScraper - Scraping data from Yahoo Finance: https://finance.yahoo.com/quote/ACPS/history/?period1=-*********&period2=**********&interval=1d
2025-05-30 00:41:08 [main] INFO  c.i.provider.YahooFinanceScraper - Saved HTML content to file: C:\Users\<USER>\Downloads\yahoo-finance-html\yahoo_finance_ACPS_1748536868.html
2025-05-30 00:41:08 [main] INFO  c.i.provider.YahooFinanceScraper - Read HTML content from file: C:\Users\<USER>\Downloads\yahoo-finance-html\yahoo_finance_ACPS_1748536868.html
2025-05-30 00:41:08 [main] INFO  c.i.provider.YahooFinanceScraper - Extracted 78 OHLCV data points for ACPS
2025-05-30 00:41:08 [main] INFO  c.i.database.DatabaseManager - Saved 78 OHLCV data points for ACPS
2025-05-30 00:41:08 [main] INFO  c.i.provider.YahooFinanceProvider - Downloaded and saved 78 data points for ACPS
2025-05-30 00:41:08 [main] INFO  c.investment.MarketDataDownloaderApp - ✓ Successfully processed ACPS
2025-05-30 00:41:09 [main] INFO  c.investment.MarketDataDownloaderApp - Processing instrument 113/10009: ACR (ACRES Commercial Realty Corp.)
2025-05-30 00:41:09 [main] INFO  c.investment.MarketDataDownloaderApp - Downloading data for ACR from 1963-05-30 to 2025-05-30 (attempt 1/4)
2025-05-30 00:41:09 [main] INFO  c.i.provider.YahooFinanceProvider - Requesting data from Yahoo Finance for symbol: ACR, from 1963-05-30 to 2025-05-30
2025-05-30 00:41:09 [main] INFO  c.i.provider.YahooFinanceScraper - Scraping data from Yahoo Finance: https://finance.yahoo.com/quote/ACR/history/?period1=-*********&period2=**********&interval=1d
2025-05-30 00:41:11 [main] INFO  c.i.provider.YahooFinanceScraper - Saved HTML content to file: C:\Users\<USER>\Downloads\yahoo-finance-html\yahoo_finance_ACR_1748536871.html
2025-05-30 00:41:12 [main] INFO  c.i.provider.YahooFinanceScraper - Read HTML content from file: C:\Users\<USER>\Downloads\yahoo-finance-html\yahoo_finance_ACR_1748536871.html
2025-05-30 00:41:12 [main] INFO  c.i.provider.YahooFinanceScraper - Extracted 4855 OHLCV data points for ACR
2025-05-30 00:41:13 [main] INFO  c.i.database.DatabaseManager - Saved 4855 OHLCV data points for ACR
2025-05-30 00:41:13 [main] INFO  c.i.provider.YahooFinanceProvider - Downloaded and saved 4855 data points for ACR
2025-05-30 00:41:13 [main] INFO  c.investment.MarketDataDownloaderApp - ✓ Successfully processed ACR
2025-05-30 00:41:13 [main] INFO  c.investment.MarketDataDownloaderApp - Processing instrument 114/10009: ACR-PC (ACRES Commercial Realty Corp.)
2025-05-30 00:41:13 [main] INFO  c.investment.MarketDataDownloaderApp - Downloading data for ACR-PC from 1963-05-30 to 2025-05-30 (attempt 1/4)
2025-05-30 00:41:13 [main] INFO  c.i.provider.YahooFinanceProvider - Requesting data from Yahoo Finance for symbol: ACR-PC, from 1963-05-30 to 2025-05-30
2025-05-30 00:41:13 [main] INFO  c.i.provider.YahooFinanceScraper - Scraping data from Yahoo Finance: https://finance.yahoo.com/quote/ACR-PC/history/?period1=-*********&period2=**********&interval=1d
2025-05-30 00:41:16 [main] INFO  c.i.provider.YahooFinanceScraper - Saved HTML content to file: C:\Users\<USER>\Downloads\yahoo-finance-html\yahoo_finance_ACR-PC_1748536876.html
2025-05-30 00:41:16 [main] INFO  c.i.provider.YahooFinanceScraper - Read HTML content from file: C:\Users\<USER>\Downloads\yahoo-finance-html\yahoo_finance_ACR-PC_1748536876.html
2025-05-30 00:41:16 [main] INFO  c.i.provider.YahooFinanceScraper - Extracted 2762 OHLCV data points for ACR-PC
2025-05-30 00:41:17 [main] INFO  c.i.database.DatabaseManager - Saved 2762 OHLCV data points for ACR-PC
2025-05-30 00:41:17 [main] INFO  c.i.provider.YahooFinanceProvider - Downloaded and saved 2762 data points for ACR-PC
2025-05-30 00:41:17 [main] INFO  c.investment.MarketDataDownloaderApp - ✓ Successfully processed ACR-PC
2025-05-30 00:41:17 [main] INFO  c.investment.MarketDataDownloaderApp - Processing instrument 115/10009: ACR-PD (ACRES Commercial Realty Corp.)
2025-05-30 00:41:17 [main] INFO  c.investment.MarketDataDownloaderApp - Downloading data for ACR-PD from 1963-05-30 to 2025-05-30 (attempt 1/4)
2025-05-30 00:41:17 [main] INFO  c.i.provider.YahooFinanceProvider - Requesting data from Yahoo Finance for symbol: ACR-PD, from 1963-05-30 to 2025-05-30
2025-05-30 00:41:17 [main] INFO  c.i.provider.YahooFinanceScraper - Scraping data from Yahoo Finance: https://finance.yahoo.com/quote/ACR-PD/history/?period1=-*********&period2=**********&interval=1d
2025-05-30 00:41:20 [main] INFO  c.i.provider.YahooFinanceScraper - Saved HTML content to file: C:\Users\<USER>\Downloads\yahoo-finance-html\yahoo_finance_ACR-PD_1748536880.html
2025-05-30 00:41:20 [main] INFO  c.i.provider.YahooFinanceScraper - Read HTML content from file: C:\Users\<USER>\Downloads\yahoo-finance-html\yahoo_finance_ACR-PD_1748536880.html
2025-05-30 00:41:20 [main] INFO  c.i.provider.YahooFinanceScraper - Extracted 958 OHLCV data points for ACR-PD
2025-05-30 00:41:20 [main] INFO  c.i.database.DatabaseManager - Saved 958 OHLCV data points for ACR-PD
2025-05-30 00:41:20 [main] INFO  c.i.provider.YahooFinanceProvider - Downloaded and saved 958 data points for ACR-PD
2025-05-30 00:41:20 [main] INFO  c.investment.MarketDataDownloaderApp - ✓ Successfully processed ACR-PD
2025-05-30 00:41:20 [main] INFO  c.investment.MarketDataDownloaderApp - Processing instrument 116/10009: ACRE (Ares Commercial Real Estate Corp)
2025-05-30 00:41:20 [main] INFO  c.investment.MarketDataDownloaderApp - Downloading data for ACRE from 1963-05-30 to 2025-05-30 (attempt 1/4)
2025-05-30 00:41:20 [main] INFO  c.i.provider.YahooFinanceProvider - Requesting data from Yahoo Finance for symbol: ACRE, from 1963-05-30 to 2025-05-30
2025-05-30 00:41:20 [main] INFO  c.i.provider.YahooFinanceScraper - Scraping data from Yahoo Finance: https://finance.yahoo.com/quote/ACRE/history/?period1=-*********&period2=**********&interval=1d
2025-05-30 00:41:25 [main] INFO  c.i.provider.YahooFinanceScraper - Saved HTML content to file: C:\Users\<USER>\Downloads\yahoo-finance-html\yahoo_finance_ACRE_1748536884.html
2025-05-30 00:41:25 [main] INFO  c.i.provider.YahooFinanceScraper - Read HTML content from file: C:\Users\<USER>\Downloads\yahoo-finance-html\yahoo_finance_ACRE_1748536884.html
2025-05-30 00:41:25 [main] INFO  c.i.provider.YahooFinanceScraper - Extracted 3291 OHLCV data points for ACRE
2025-05-30 00:41:25 [main] INFO  c.i.database.DatabaseManager - Saved 3291 OHLCV data points for ACRE
2025-05-30 00:41:25 [main] INFO  c.i.provider.YahooFinanceProvider - Downloaded and saved 3291 data points for ACRE
2025-05-30 00:41:25 [main] INFO  c.investment.MarketDataDownloaderApp - ✓ Successfully processed ACRE
2025-05-30 00:41:26 [main] INFO  c.investment.MarketDataDownloaderApp - Processing instrument 117/10009: ACRL (Atacama Resources International, Inc.)
2025-05-30 00:41:26 [main] INFO  c.investment.MarketDataDownloaderApp - Downloading data for ACRL from 1963-05-30 to 2025-05-30 (attempt 1/4)
2025-05-30 00:41:26 [main] INFO  c.i.provider.YahooFinanceProvider - Requesting data from Yahoo Finance for symbol: ACRL, from 1963-05-30 to 2025-05-30
2025-05-30 00:41:26 [main] INFO  c.i.provider.YahooFinanceScraper - Scraping data from Yahoo Finance: https://finance.yahoo.com/quote/ACRL/history/?period1=-*********&period2=**********&interval=1d
2025-05-30 00:41:29 [main] INFO  c.i.provider.YahooFinanceScraper - Saved HTML content to file: C:\Users\<USER>\Downloads\yahoo-finance-html\yahoo_finance_ACRL_1748536889.html
2025-05-30 00:41:29 [main] INFO  c.i.provider.YahooFinanceScraper - Read HTML content from file: C:\Users\<USER>\Downloads\yahoo-finance-html\yahoo_finance_ACRL_1748536889.html
2025-05-30 00:41:29 [main] INFO  c.i.provider.YahooFinanceScraper - Extracted 2005 OHLCV data points for ACRL
2025-05-30 00:41:30 [main] INFO  c.i.database.DatabaseManager - Saved 2005 OHLCV data points for ACRL
2025-05-30 00:41:30 [main] INFO  c.i.provider.YahooFinanceProvider - Downloaded and saved 2005 data points for ACRL
2025-05-30 00:41:30 [main] INFO  c.investment.MarketDataDownloaderApp - ✓ Successfully processed ACRL
2025-05-30 00:41:30 [main] INFO  c.investment.MarketDataDownloaderApp - Processing instrument 118/10009: ACRS (Aclaris Therapeutics, Inc.)
2025-05-30 00:41:30 [main] INFO  c.investment.MarketDataDownloaderApp - Downloading data for ACRS from 1963-05-30 to 2025-05-30 (attempt 1/4)
2025-05-30 00:41:30 [main] INFO  c.i.provider.YahooFinanceProvider - Requesting data from Yahoo Finance for symbol: ACRS, from 1963-05-30 to 2025-05-30
2025-05-30 00:41:30 [main] INFO  c.i.provider.YahooFinanceScraper - Scraping data from Yahoo Finance: https://finance.yahoo.com/quote/ACRS/history/?period1=-*********&period2=**********&interval=1d
2025-05-30 00:41:32 [main] INFO  c.i.provider.YahooFinanceScraper - Saved HTML content to file: C:\Users\<USER>\Downloads\yahoo-finance-html\yahoo_finance_ACRS_1748536892.html
2025-05-30 00:41:32 [main] INFO  c.i.provider.YahooFinanceScraper - Read HTML content from file: C:\Users\<USER>\Downloads\yahoo-finance-html\yahoo_finance_ACRS_1748536892.html
2025-05-30 00:41:32 [main] INFO  c.i.provider.YahooFinanceScraper - Extracted 2425 OHLCV data points for ACRS
2025-05-30 00:41:33 [main] INFO  c.i.database.DatabaseManager - Saved 2425 OHLCV data points for ACRS
2025-05-30 00:41:33 [main] INFO  c.i.provider.YahooFinanceProvider - Downloaded and saved 2425 data points for ACRS
2025-05-30 00:41:33 [main] INFO  c.investment.MarketDataDownloaderApp - ✓ Successfully processed ACRS
2025-05-30 00:41:34 [main] INFO  c.investment.MarketDataDownloaderApp - Processing instrument 119/10009: ACRV (Acrivon Therapeutics, Inc.)
2025-05-30 00:41:34 [main] INFO  c.investment.MarketDataDownloaderApp - Downloading data for ACRV from 1963-05-30 to 2025-05-30 (attempt 1/4)
2025-05-30 00:41:34 [main] INFO  c.i.provider.YahooFinanceProvider - Requesting data from Yahoo Finance for symbol: ACRV, from 1963-05-30 to 2025-05-30
2025-05-30 00:41:34 [main] INFO  c.i.provider.YahooFinanceScraper - Scraping data from Yahoo Finance: https://finance.yahoo.com/quote/ACRV/history/?period1=-*********&period2=**********&interval=1d
2025-05-30 00:41:35 [main] INFO  c.i.provider.YahooFinanceScraper - Saved HTML content to file: C:\Users\<USER>\Downloads\yahoo-finance-html\yahoo_finance_ACRV_1748536895.html
2025-05-30 00:41:35 [main] INFO  c.i.provider.YahooFinanceScraper - Read HTML content from file: C:\Users\<USER>\Downloads\yahoo-finance-html\yahoo_finance_ACRV_1748536895.html
2025-05-30 00:41:35 [main] INFO  c.i.provider.YahooFinanceScraper - Extracted 635 OHLCV data points for ACRV
2025-05-30 00:41:35 [main] INFO  c.i.database.DatabaseManager - Saved 635 OHLCV data points for ACRV
2025-05-30 00:41:35 [main] INFO  c.i.provider.YahooFinanceProvider - Downloaded and saved 635 data points for ACRV
2025-05-30 00:41:35 [main] INFO  c.investment.MarketDataDownloaderApp - ✓ Successfully processed ACRV
2025-05-30 00:41:36 [main] INFO  c.investment.MarketDataDownloaderApp - Processing instrument 120/10009: ACT (Enact Holdings, Inc.)
2025-05-30 00:41:36 [main] INFO  c.investment.MarketDataDownloaderApp - Downloading data for ACT from 1963-05-30 to 2025-05-30 (attempt 1/4)
2025-05-30 00:41:36 [main] INFO  c.i.provider.YahooFinanceProvider - Requesting data from Yahoo Finance for symbol: ACT, from 1963-05-30 to 2025-05-30
2025-05-30 00:41:36 [main] INFO  c.i.provider.YahooFinanceScraper - Scraping data from Yahoo Finance: https://finance.yahoo.com/quote/ACT/history/?period1=-*********&period2=**********&interval=1d
2025-05-30 00:41:38 [main] INFO  c.i.provider.YahooFinanceScraper - Saved HTML content to file: C:\Users\<USER>\Downloads\yahoo-finance-html\yahoo_finance_ACT_1748536898.html
2025-05-30 00:41:38 [main] INFO  c.i.provider.YahooFinanceScraper - Read HTML content from file: C:\Users\<USER>\Downloads\yahoo-finance-html\yahoo_finance_ACT_1748536898.html
2025-05-30 00:41:38 [main] INFO  c.i.provider.YahooFinanceScraper - Extracted 929 OHLCV data points for ACT
2025-05-30 00:41:38 [main] INFO  c.i.database.DatabaseManager - Saved 929 OHLCV data points for ACT
2025-05-30 00:41:38 [main] INFO  c.i.provider.YahooFinanceProvider - Downloaded and saved 929 data points for ACT
2025-05-30 00:41:38 [main] INFO  c.investment.MarketDataDownloaderApp - ✓ Successfully processed ACT
2025-05-30 00:41:38 [main] INFO  c.investment.MarketDataDownloaderApp - Processing instrument 121/10009: ACTG (ACACIA RESEARCH CORP)
2025-05-30 00:41:38 [main] INFO  c.investment.MarketDataDownloaderApp - Downloading data for ACTG from 1963-05-30 to 2025-05-30 (attempt 1/4)
2025-05-30 00:41:38 [main] INFO  c.i.provider.YahooFinanceProvider - Requesting data from Yahoo Finance for symbol: ACTG, from 1963-05-30 to 2025-05-30
2025-05-30 00:41:38 [main] INFO  c.i.provider.YahooFinanceScraper - Scraping data from Yahoo Finance: https://finance.yahoo.com/quote/ACTG/history/?period1=-*********&period2=**********&interval=1d
2025-05-30 00:41:44 [main] INFO  c.i.provider.YahooFinanceScraper - Saved HTML content to file: C:\Users\<USER>\Downloads\yahoo-finance-html\yahoo_finance_ACTG_1748536904.html
2025-05-30 00:41:44 [main] INFO  c.i.provider.YahooFinanceScraper - Read HTML content from file: C:\Users\<USER>\Downloads\yahoo-finance-html\yahoo_finance_ACTG_1748536904.html
2025-05-30 00:41:44 [main] INFO  c.i.provider.YahooFinanceScraper - Extracted 5649 OHLCV data points for ACTG
2025-05-30 00:41:45 [main] INFO  c.i.database.DatabaseManager - Saved 5649 OHLCV data points for ACTG
2025-05-30 00:41:45 [main] INFO  c.i.provider.YahooFinanceProvider - Downloaded and saved 5649 data points for ACTG
2025-05-30 00:41:45 [main] INFO  c.investment.MarketDataDownloaderApp - ✓ Successfully processed ACTG
2025-05-30 00:41:46 [main] INFO  c.investment.MarketDataDownloaderApp - Processing instrument 122/10009: ACTU (ACTUATE THERAPEUTICS, INC.)
2025-05-30 00:41:46 [main] INFO  c.investment.MarketDataDownloaderApp - Downloading data for ACTU from 1963-05-30 to 2025-05-30 (attempt 1/4)
2025-05-30 00:41:46 [main] INFO  c.i.provider.YahooFinanceProvider - Requesting data from Yahoo Finance for symbol: ACTU, from 1963-05-30 to 2025-05-30
2025-05-30 00:41:46 [main] INFO  c.i.provider.YahooFinanceScraper - Scraping data from Yahoo Finance: https://finance.yahoo.com/quote/ACTU/history/?period1=-*********&period2=**********&interval=1d
2025-05-30 00:41:47 [main] INFO  c.i.provider.YahooFinanceScraper - Saved HTML content to file: C:\Users\<USER>\Downloads\yahoo-finance-html\yahoo_finance_ACTU_1748536907.html
2025-05-30 00:41:47 [main] INFO  c.i.provider.YahooFinanceScraper - Read HTML content from file: C:\Users\<USER>\Downloads\yahoo-finance-html\yahoo_finance_ACTU_1748536907.html
2025-05-30 00:41:47 [main] INFO  c.i.provider.YahooFinanceScraper - Extracted 199 OHLCV data points for ACTU
2025-05-30 00:41:48 [main] INFO  c.i.database.DatabaseManager - Saved 199 OHLCV data points for ACTU
2025-05-30 00:41:48 [main] INFO  c.i.provider.YahooFinanceProvider - Downloaded and saved 199 data points for ACTU
2025-05-30 00:41:48 [main] INFO  c.investment.MarketDataDownloaderApp - ✓ Successfully processed ACTU
2025-05-30 00:41:48 [main] INFO  c.investment.MarketDataDownloaderApp - Processing instrument 123/10009: ACU (ACME UNITED CORP)
2025-05-30 00:41:48 [main] INFO  c.investment.MarketDataDownloaderApp - Downloading data for ACU from 1963-05-30 to 2025-05-30 (attempt 1/4)
2025-05-30 00:41:48 [main] INFO  c.i.provider.YahooFinanceProvider - Requesting data from Yahoo Finance for symbol: ACU, from 1963-05-30 to 2025-05-30
2025-05-30 00:41:48 [main] INFO  c.i.provider.YahooFinanceScraper - Scraping data from Yahoo Finance: https://finance.yahoo.com/quote/ACU/history/?period1=-*********&period2=**********&interval=1d
2025-05-30 00:41:57 [main] INFO  c.i.provider.YahooFinanceScraper - Saved HTML content to file: C:\Users\<USER>\Downloads\yahoo-finance-html\yahoo_finance_ACU_1748536917.html
2025-05-30 00:41:57 [main] INFO  c.i.provider.YahooFinanceScraper - Read HTML content from file: C:\Users\<USER>\Downloads\yahoo-finance-html\yahoo_finance_ACU_1748536917.html
2025-05-30 00:41:57 [main] INFO  c.i.provider.YahooFinanceScraper - Extracted 10170 OHLCV data points for ACU
2025-05-30 00:42:00 [main] INFO  c.i.database.DatabaseManager - Saved 10170 OHLCV data points for ACU
2025-05-30 00:42:00 [main] INFO  c.i.provider.YahooFinanceProvider - Downloaded and saved 10170 data points for ACU
2025-05-30 00:42:00 [main] INFO  c.investment.MarketDataDownloaderApp - ✓ Successfully processed ACU
2025-05-30 00:42:00 [main] INFO  c.investment.MarketDataDownloaderApp - Processing instrument 124/10009: ACUT (Accustem Sciences Inc.)
2025-05-30 00:42:00 [main] INFO  c.investment.MarketDataDownloaderApp - Downloading data for ACUT from 1963-05-30 to 2025-05-30 (attempt 1/4)
2025-05-30 00:42:00 [main] INFO  c.i.provider.YahooFinanceProvider - Requesting data from Yahoo Finance for symbol: ACUT, from 1963-05-30 to 2025-05-30
2025-05-30 00:42:00 [main] INFO  c.i.provider.YahooFinanceScraper - Scraping data from Yahoo Finance: https://finance.yahoo.com/quote/ACUT/history/?period1=-*********&period2=**********&interval=1d
2025-05-30 00:42:02 [main] INFO  c.i.provider.YahooFinanceScraper - Saved HTML content to file: C:\Users\<USER>\Downloads\yahoo-finance-html\yahoo_finance_ACUT_1748536922.html
2025-05-30 00:42:02 [main] INFO  c.i.provider.YahooFinanceScraper - Read HTML content from file: C:\Users\<USER>\Downloads\yahoo-finance-html\yahoo_finance_ACUT_1748536922.html
2025-05-30 00:42:02 [main] INFO  c.i.provider.YahooFinanceScraper - Extracted 599 OHLCV data points for ACUT
2025-05-30 00:42:02 [main] INFO  c.i.database.DatabaseManager - Saved 599 OHLCV data points for ACUT
2025-05-30 00:42:02 [main] INFO  c.i.provider.YahooFinanceProvider - Downloaded and saved 599 data points for ACUT
2025-05-30 00:42:02 [main] INFO  c.investment.MarketDataDownloaderApp - ✓ Successfully processed ACUT
2025-05-30 00:42:02 [main] INFO  c.investment.MarketDataDownloaderApp - Processing instrument 125/10009: ACV (Virtus Diversified Income & Convertible Fund)
2025-05-30 00:42:02 [main] INFO  c.investment.MarketDataDownloaderApp - Downloading data for ACV from 1963-05-30 to 2025-05-30 (attempt 1/4)
2025-05-30 00:42:02 [main] INFO  c.i.provider.YahooFinanceProvider - Requesting data from Yahoo Finance for symbol: ACV, from 1963-05-30 to 2025-05-30
2025-05-30 00:42:02 [main] INFO  c.i.provider.YahooFinanceScraper - Scraping data from Yahoo Finance: https://finance.yahoo.com/quote/ACV/history/?period1=-*********&period2=**********&interval=1d
2025-05-30 00:42:06 [main] INFO  c.i.provider.YahooFinanceScraper - Saved HTML content to file: C:\Users\<USER>\Downloads\yahoo-finance-html\yahoo_finance_ACV_1748536926.html
2025-05-30 00:42:06 [main] INFO  c.i.provider.YahooFinanceScraper - Read HTML content from file: C:\Users\<USER>\Downloads\yahoo-finance-html\yahoo_finance_ACV_1748536926.html
2025-05-30 00:42:06 [main] INFO  c.i.provider.YahooFinanceScraper - Extracted 2520 OHLCV data points for ACV
2025-05-30 00:42:06 [main] INFO  c.i.database.DatabaseManager - Saved 2520 OHLCV data points for ACV
2025-05-30 00:42:06 [main] INFO  c.i.provider.YahooFinanceProvider - Downloaded and saved 2520 data points for ACV
2025-05-30 00:42:06 [main] INFO  c.investment.MarketDataDownloaderApp - ✓ Successfully processed ACV
2025-05-30 00:42:07 [main] INFO  c.investment.MarketDataDownloaderApp - Processing instrument 126/10009: ACVA (ACV Auctions Inc.)
2025-05-30 00:42:07 [main] INFO  c.investment.MarketDataDownloaderApp - Downloading data for ACVA from 1963-05-30 to 2025-05-30 (attempt 1/4)
2025-05-30 00:42:07 [main] INFO  c.i.provider.YahooFinanceProvider - Requesting data from Yahoo Finance for symbol: ACVA, from 1963-05-30 to 2025-05-30
2025-05-30 00:42:07 [main] INFO  c.i.provider.YahooFinanceScraper - Scraping data from Yahoo Finance: https://finance.yahoo.com/quote/ACVA/history/?period1=-*********&period2=**********&interval=1d
2025-05-30 00:42:09 [main] INFO  c.i.provider.YahooFinanceScraper - Saved HTML content to file: C:\Users\<USER>\Downloads\yahoo-finance-html\yahoo_finance_ACVA_1748536929.html
2025-05-30 00:42:09 [main] INFO  c.i.provider.YahooFinanceScraper - Read HTML content from file: C:\Users\<USER>\Downloads\yahoo-finance-html\yahoo_finance_ACVA_1748536929.html
2025-05-30 00:42:09 [main] INFO  c.i.provider.YahooFinanceScraper - Extracted 1050 OHLCV data points for ACVA
2025-05-30 00:42:09 [main] INFO  c.i.database.DatabaseManager - Saved 1050 OHLCV data points for ACVA
2025-05-30 00:42:09 [main] INFO  c.i.provider.YahooFinanceProvider - Downloaded and saved 1050 data points for ACVA
2025-05-30 00:42:09 [main] INFO  c.investment.MarketDataDownloaderApp - ✓ Successfully processed ACVA
2025-05-30 00:42:10 [main] INFO  c.investment.MarketDataDownloaderApp - Processing instrument 127/10009: ACXP (Acurx Pharmaceuticals, Inc.)
2025-05-30 00:42:10 [main] INFO  c.investment.MarketDataDownloaderApp - Downloading data for ACXP from 1963-05-30 to 2025-05-30 (attempt 1/4)
2025-05-30 00:42:10 [main] INFO  c.i.provider.YahooFinanceProvider - Requesting data from Yahoo Finance for symbol: ACXP, from 1963-05-30 to 2025-05-30
2025-05-30 00:42:10 [main] INFO  c.i.provider.YahooFinanceScraper - Scraping data from Yahoo Finance: https://finance.yahoo.com/quote/ACXP/history/?period1=-*********&period2=**********&interval=1d
2025-05-30 00:42:12 [main] INFO  c.i.provider.YahooFinanceScraper - Saved HTML content to file: C:\Users\<USER>\Downloads\yahoo-finance-html\yahoo_finance_ACXP_1748536932.html
2025-05-30 00:42:12 [main] INFO  c.i.provider.YahooFinanceScraper - Read HTML content from file: C:\Users\<USER>\Downloads\yahoo-finance-html\yahoo_finance_ACXP_1748536932.html
2025-05-30 00:42:12 [main] INFO  c.i.provider.YahooFinanceScraper - Extracted 986 OHLCV data points for ACXP
2025-05-30 00:42:12 [main] INFO  c.i.database.DatabaseManager - Saved 986 OHLCV data points for ACXP
2025-05-30 00:42:12 [main] INFO  c.i.provider.YahooFinanceProvider - Downloaded and saved 986 data points for ACXP
2025-05-30 00:42:12 [main] INFO  c.investment.MarketDataDownloaderApp - ✓ Successfully processed ACXP
2025-05-30 00:42:12 [main] INFO  c.investment.MarketDataDownloaderApp - Processing instrument 128/10009: ADAG (Adagene Inc.)
2025-05-30 00:42:12 [main] INFO  c.investment.MarketDataDownloaderApp - Downloading data for ADAG from 1963-05-30 to 2025-05-30 (attempt 1/4)
2025-05-30 00:42:12 [main] INFO  c.i.provider.YahooFinanceProvider - Requesting data from Yahoo Finance for symbol: ADAG, from 1963-05-30 to 2025-05-30
2025-05-30 00:42:12 [main] INFO  c.i.provider.YahooFinanceScraper - Scraping data from Yahoo Finance: https://finance.yahoo.com/quote/ADAG/history/?period1=-*********&period2=**********&interval=1d
2025-05-30 00:42:14 [main] INFO  c.i.provider.YahooFinanceScraper - Saved HTML content to file: C:\Users\<USER>\Downloads\yahoo-finance-html\yahoo_finance_ADAG_1748536934.html
2025-05-30 00:42:15 [main] INFO  c.i.provider.YahooFinanceScraper - Read HTML content from file: C:\Users\<USER>\Downloads\yahoo-finance-html\yahoo_finance_ADAG_1748536934.html
2025-05-30 00:42:15 [main] INFO  c.i.provider.YahooFinanceScraper - Extracted 1079 OHLCV data points for ADAG
2025-05-30 00:42:15 [main] INFO  c.i.database.DatabaseManager - Saved 1079 OHLCV data points for ADAG
2025-05-30 00:42:15 [main] INFO  c.i.provider.YahooFinanceProvider - Downloaded and saved 1079 data points for ADAG
2025-05-30 00:42:15 [main] INFO  c.investment.MarketDataDownloaderApp - ✓ Successfully processed ADAG
2025-05-30 00:42:15 [main] INFO  c.investment.MarketDataDownloaderApp - Processing instrument 129/10009: ADAP (Adaptimmune Therapeutics PLC)
2025-05-30 00:42:15 [main] INFO  c.investment.MarketDataDownloaderApp - Downloading data for ADAP from 1963-05-30 to 2025-05-30 (attempt 1/4)
2025-05-30 00:42:15 [main] INFO  c.i.provider.YahooFinanceProvider - Requesting data from Yahoo Finance for symbol: ADAP, from 1963-05-30 to 2025-05-30
2025-05-30 00:42:15 [main] INFO  c.i.provider.YahooFinanceScraper - Scraping data from Yahoo Finance: https://finance.yahoo.com/quote/ADAP/history/?period1=-*********&period2=**********&interval=1d
2025-05-30 00:42:18 [main] INFO  c.i.provider.YahooFinanceScraper - Saved HTML content to file: C:\Users\<USER>\Downloads\yahoo-finance-html\yahoo_finance_ADAP_1748536938.html
2025-05-30 00:42:18 [main] INFO  c.i.provider.YahooFinanceScraper - Read HTML content from file: C:\Users\<USER>\Downloads\yahoo-finance-html\yahoo_finance_ADAP_1748536938.html
2025-05-30 00:42:18 [main] INFO  c.i.provider.YahooFinanceScraper - Extracted 2532 OHLCV data points for ADAP
2025-05-30 00:42:19 [main] INFO  c.i.database.DatabaseManager - Saved 2532 OHLCV data points for ADAP
2025-05-30 00:42:19 [main] INFO  c.i.provider.YahooFinanceProvider - Downloaded and saved 2532 data points for ADAP
2025-05-30 00:42:19 [main] INFO  c.investment.MarketDataDownloaderApp - ✓ Successfully processed ADAP
2025-05-30 00:42:19 [main] INFO  c.investment.MarketDataDownloaderApp - Processing instrument 130/10009: ADBE (ADOBE INC.)
2025-05-30 00:42:19 [main] INFO  c.investment.MarketDataDownloaderApp - Downloading data for ADBE from 1963-05-30 to 2025-05-30 (attempt 1/4)
2025-05-30 00:42:19 [main] INFO  c.i.provider.YahooFinanceProvider - Requesting data from Yahoo Finance for symbol: ADBE, from 1963-05-30 to 2025-05-30
2025-05-30 00:42:19 [main] INFO  c.i.provider.YahooFinanceScraper - Scraping data from Yahoo Finance: https://finance.yahoo.com/quote/ADBE/history/?period1=-*********&period2=**********&interval=1d
2025-05-30 00:42:24 [main] INFO  c.i.provider.YahooFinanceScraper - Saved HTML content to file: C:\Users\<USER>\Downloads\yahoo-finance-html\yahoo_finance_ADBE_1748536944.html
2025-05-30 00:42:25 [main] INFO  c.i.provider.YahooFinanceScraper - Read HTML content from file: C:\Users\<USER>\Downloads\yahoo-finance-html\yahoo_finance_ADBE_1748536944.html
2025-05-30 00:42:25 [main] INFO  c.i.provider.YahooFinanceScraper - Extracted 9774 OHLCV data points for ADBE
2025-05-30 00:42:27 [main] INFO  c.i.database.DatabaseManager - Saved 9774 OHLCV data points for ADBE
2025-05-30 00:42:27 [main] INFO  c.i.provider.YahooFinanceProvider - Downloaded and saved 9774 data points for ADBE
2025-05-30 00:42:27 [main] INFO  c.investment.MarketDataDownloaderApp - ✓ Successfully processed ADBE
2025-05-30 00:42:28 [main] INFO  c.investment.MarketDataDownloaderApp - Processing instrument 131/10009: ADC (AGREE REALTY CORP)
2025-05-30 00:42:28 [main] INFO  c.investment.MarketDataDownloaderApp - Downloading data for ADC from 1963-05-30 to 2025-05-30 (attempt 1/4)
2025-05-30 00:42:28 [main] INFO  c.i.provider.YahooFinanceProvider - Requesting data from Yahoo Finance for symbol: ADC, from 1963-05-30 to 2025-05-30
2025-05-30 00:42:28 [main] INFO  c.i.provider.YahooFinanceScraper - Scraping data from Yahoo Finance: https://finance.yahoo.com/quote/ADC/history/?period1=-*********&period2=**********&interval=1d
2025-05-30 00:42:32 [main] INFO  c.i.provider.YahooFinanceScraper - Saved HTML content to file: C:\Users\<USER>\Downloads\yahoo-finance-html\yahoo_finance_ADC_1748536952.html
2025-05-30 00:42:32 [main] INFO  c.i.provider.YahooFinanceScraper - Read HTML content from file: C:\Users\<USER>\Downloads\yahoo-finance-html\yahoo_finance_ADC_1748536952.html
2025-05-30 00:42:32 [main] INFO  c.i.provider.YahooFinanceScraper - Extracted 7830 OHLCV data points for ADC
2025-05-30 00:42:34 [main] INFO  c.i.database.DatabaseManager - Saved 7830 OHLCV data points for ADC
2025-05-30 00:42:34 [main] INFO  c.i.provider.YahooFinanceProvider - Downloaded and saved 7830 data points for ADC
2025-05-30 00:42:34 [main] INFO  c.investment.MarketDataDownloaderApp - ✓ Successfully processed ADC
2025-05-30 00:42:35 [main] INFO  c.investment.MarketDataDownloaderApp - Processing instrument 132/10009: ADC-PA (AGREE REALTY CORP)
2025-05-30 00:42:35 [main] INFO  c.investment.MarketDataDownloaderApp - Downloading data for ADC-PA from 1963-05-30 to 2025-05-30 (attempt 1/4)
2025-05-30 00:42:35 [main] INFO  c.i.provider.YahooFinanceProvider - Requesting data from Yahoo Finance for symbol: ADC-PA, from 1963-05-30 to 2025-05-30
2025-05-30 00:42:35 [main] INFO  c.i.provider.YahooFinanceScraper - Scraping data from Yahoo Finance: https://finance.yahoo.com/quote/ADC-PA/history/?period1=-*********&period2=**********&interval=1d
2025-05-30 00:42:37 [main] INFO  c.i.provider.YahooFinanceScraper - Saved HTML content to file: C:\Users\<USER>\Downloads\yahoo-finance-html\yahoo_finance_ADC-PA_1748536957.html
2025-05-30 00:42:37 [main] INFO  c.i.provider.YahooFinanceScraper - Read HTML content from file: C:\Users\<USER>\Downloads\yahoo-finance-html\yahoo_finance_ADC-PA_1748536957.html
2025-05-30 00:42:37 [main] INFO  c.i.provider.YahooFinanceScraper - Extracted 915 OHLCV data points for ADC-PA
2025-05-30 00:42:37 [main] INFO  c.i.database.DatabaseManager - Saved 915 OHLCV data points for ADC-PA
2025-05-30 00:42:37 [main] INFO  c.i.provider.YahooFinanceProvider - Downloaded and saved 915 data points for ADC-PA
2025-05-30 00:42:37 [main] INFO  c.investment.MarketDataDownloaderApp - ✓ Successfully processed ADC-PA
2025-05-30 00:42:38 [main] INFO  c.investment.MarketDataDownloaderApp - Processing instrument 133/10009: ADCT (ADC Therapeutics SA)
2025-05-30 00:42:38 [main] INFO  c.investment.MarketDataDownloaderApp - Downloading data for ADCT from 1963-05-30 to 2025-05-30 (attempt 1/4)
2025-05-30 00:42:38 [main] INFO  c.i.provider.YahooFinanceProvider - Requesting data from Yahoo Finance for symbol: ADCT, from 1963-05-30 to 2025-05-30
2025-05-30 00:42:38 [main] INFO  c.i.provider.YahooFinanceScraper - Scraping data from Yahoo Finance: https://finance.yahoo.com/quote/ADCT/history/?period1=-*********&period2=**********&interval=1d
2025-05-30 00:42:40 [main] INFO  c.i.provider.YahooFinanceScraper - Saved HTML content to file: C:\Users\<USER>\Downloads\yahoo-finance-html\yahoo_finance_ADCT_1748536960.html
2025-05-30 00:42:40 [main] INFO  c.i.provider.YahooFinanceScraper - Read HTML content from file: C:\Users\<USER>\Downloads\yahoo-finance-html\yahoo_finance_ADCT_1748536960.html
2025-05-30 00:42:40 [main] INFO  c.i.provider.YahooFinanceScraper - Extracted 1265 OHLCV data points for ADCT
2025-05-30 00:42:40 [main] INFO  c.i.database.DatabaseManager - Saved 1265 OHLCV data points for ADCT
2025-05-30 00:42:40 [main] INFO  c.i.provider.YahooFinanceProvider - Downloaded and saved 1265 data points for ADCT
2025-05-30 00:42:40 [main] INFO  c.investment.MarketDataDownloaderApp - ✓ Successfully processed ADCT
2025-05-30 00:42:41 [main] INFO  c.investment.MarketDataDownloaderApp - Processing instrument 134/10009: ADD (Color Star Technology Co., Ltd.)
2025-05-30 00:42:41 [main] INFO  c.investment.MarketDataDownloaderApp - Downloading data for ADD from 1963-05-30 to 2025-05-30 (attempt 1/4)
2025-05-30 00:42:41 [main] INFO  c.i.provider.YahooFinanceProvider - Requesting data from Yahoo Finance for symbol: ADD, from 1963-05-30 to 2025-05-30
2025-05-30 00:42:41 [main] INFO  c.i.provider.YahooFinanceScraper - Scraping data from Yahoo Finance: https://finance.yahoo.com/quote/ADD/history/?period1=-*********&period2=**********&interval=1d
2025-05-30 00:42:43 [main] INFO  c.i.provider.YahooFinanceScraper - Saved HTML content to file: C:\Users\<USER>\Downloads\yahoo-finance-html\yahoo_finance_ADD_1748536963.html
2025-05-30 00:42:44 [main] INFO  c.i.provider.YahooFinanceScraper - Read HTML content from file: C:\Users\<USER>\Downloads\yahoo-finance-html\yahoo_finance_ADD_1748536963.html
2025-05-30 00:42:44 [main] INFO  c.i.provider.YahooFinanceScraper - Extracted 3217 OHLCV data points for ADD
2025-05-30 00:42:44 [main] INFO  c.i.database.DatabaseManager - Saved 3217 OHLCV data points for ADD
2025-05-30 00:42:44 [main] INFO  c.i.provider.YahooFinanceProvider - Downloaded and saved 3217 data points for ADD
2025-05-30 00:42:44 [main] INFO  c.investment.MarketDataDownloaderApp - ✓ Successfully processed ADD
2025-05-30 00:42:45 [main] INFO  c.investment.MarketDataDownloaderApp - Processing instrument 135/10009: ADEA (Adeia Inc.)
2025-05-30 00:42:45 [main] INFO  c.investment.MarketDataDownloaderApp - Downloading data for ADEA from 1963-05-30 to 2025-05-30 (attempt 1/4)
2025-05-30 00:42:45 [main] INFO  c.i.provider.YahooFinanceProvider - Requesting data from Yahoo Finance for symbol: ADEA, from 1963-05-30 to 2025-05-30
2025-05-30 00:42:45 [main] INFO  c.i.provider.YahooFinanceScraper - Scraping data from Yahoo Finance: https://finance.yahoo.com/quote/ADEA/history/?period1=-*********&period2=**********&interval=1d
2025-05-30 00:42:50 [main] INFO  c.i.provider.YahooFinanceScraper - Saved HTML content to file: C:\Users\<USER>\Downloads\yahoo-finance-html\yahoo_finance_ADEA_1748536970.html
2025-05-30 00:42:50 [main] INFO  c.i.provider.YahooFinanceScraper - Read HTML content from file: C:\Users\<USER>\Downloads\yahoo-finance-html\yahoo_finance_ADEA_1748536970.html
2025-05-30 00:42:50 [main] INFO  c.i.provider.YahooFinanceScraper - Extracted 5417 OHLCV data points for ADEA
2025-05-30 00:42:51 [main] INFO  c.i.database.DatabaseManager - Saved 5417 OHLCV data points for ADEA
2025-05-30 00:42:51 [main] INFO  c.i.provider.YahooFinanceProvider - Downloaded and saved 5417 data points for ADEA
2025-05-30 00:42:51 [main] INFO  c.investment.MarketDataDownloaderApp - ✓ Successfully processed ADEA
2025-05-30 00:42:52 [main] INFO  c.investment.MarketDataDownloaderApp - Processing instrument 136/10009: ADGM (Adagio Medical Holdings, Inc.)
2025-05-30 00:42:52 [main] INFO  c.investment.MarketDataDownloaderApp - Downloading data for ADGM from 1963-05-30 to 2025-05-30 (attempt 1/4)
2025-05-30 00:42:52 [main] INFO  c.i.provider.YahooFinanceProvider - Requesting data from Yahoo Finance for symbol: ADGM, from 1963-05-30 to 2025-05-30
2025-05-30 00:42:52 [main] INFO  c.i.provider.YahooFinanceScraper - Scraping data from Yahoo Finance: https://finance.yahoo.com/quote/ADGM/history/?period1=-*********&period2=**********&interval=1d
2025-05-30 00:42:53 [main] INFO  c.i.provider.YahooFinanceScraper - Saved HTML content to file: C:\Users\<USER>\Downloads\yahoo-finance-html\yahoo_finance_ADGM_1748536973.html
2025-05-30 00:42:53 [main] INFO  c.i.provider.YahooFinanceScraper - Read HTML content from file: C:\Users\<USER>\Downloads\yahoo-finance-html\yahoo_finance_ADGM_1748536973.html
2025-05-30 00:42:53 [main] INFO  c.i.provider.YahooFinanceScraper - Extracted 206 OHLCV data points for ADGM
2025-05-30 00:42:53 [main] INFO  c.i.database.DatabaseManager - Saved 206 OHLCV data points for ADGM
2025-05-30 00:42:53 [main] INFO  c.i.provider.YahooFinanceProvider - Downloaded and saved 206 data points for ADGM
2025-05-30 00:42:53 [main] INFO  c.investment.MarketDataDownloaderApp - ✓ Successfully processed ADGM
2025-05-30 00:42:53 [main] INFO  c.investment.MarketDataDownloaderApp - Processing instrument 137/10009: ADI (ANALOG DEVICES INC)
2025-05-30 00:42:53 [main] INFO  c.investment.MarketDataDownloaderApp - Downloading data for ADI from 1963-05-30 to 2025-05-30 (attempt 1/4)
2025-05-30 00:42:53 [main] INFO  c.i.provider.YahooFinanceProvider - Requesting data from Yahoo Finance for symbol: ADI, from 1963-05-30 to 2025-05-30
2025-05-30 00:42:53 [main] INFO  c.i.provider.YahooFinanceScraper - Scraping data from Yahoo Finance: https://finance.yahoo.com/quote/ADI/history/?period1=-*********&period2=**********&interval=1d
2025-05-30 00:43:02 [main] INFO  c.i.provider.YahooFinanceScraper - Saved HTML content to file: C:\Users\<USER>\Downloads\yahoo-finance-html\yahoo_finance_ADI_1748536982.html
2025-05-30 00:43:02 [main] INFO  c.i.provider.YahooFinanceScraper - Read HTML content from file: C:\Users\<USER>\Downloads\yahoo-finance-html\yahoo_finance_ADI_1748536982.html
2025-05-30 00:43:02 [main] INFO  c.i.provider.YahooFinanceScraper - Extracted 11394 OHLCV data points for ADI
2025-05-30 00:43:05 [main] INFO  c.i.database.DatabaseManager - Saved 11394 OHLCV data points for ADI
2025-05-30 00:43:05 [main] INFO  c.i.provider.YahooFinanceProvider - Downloaded and saved 11394 data points for ADI
2025-05-30 00:43:05 [main] INFO  c.investment.MarketDataDownloaderApp - ✓ Successfully processed ADI
2025-05-30 00:43:05 [main] INFO  c.investment.MarketDataDownloaderApp - Processing instrument 138/10009: ADIA (Adia Nutrition, Inc.)
2025-05-30 00:43:05 [main] INFO  c.investment.MarketDataDownloaderApp - Downloading data for ADIA from 1963-05-30 to 2025-05-30 (attempt 1/4)
2025-05-30 00:43:05 [main] INFO  c.i.provider.YahooFinanceProvider - Requesting data from Yahoo Finance for symbol: ADIA, from 1963-05-30 to 2025-05-30
2025-05-30 00:43:05 [main] INFO  c.i.provider.YahooFinanceScraper - Scraping data from Yahoo Finance: https://finance.yahoo.com/quote/ADIA/history/?period1=-*********&period2=**********&interval=1d
2025-05-30 00:43:08 [main] INFO  c.i.provider.YahooFinanceScraper - Saved HTML content to file: C:\Users\<USER>\Downloads\yahoo-finance-html\yahoo_finance_ADIA_1748536988.html
2025-05-30 00:43:08 [main] INFO  c.i.provider.YahooFinanceScraper - Read HTML content from file: C:\Users\<USER>\Downloads\yahoo-finance-html\yahoo_finance_ADIA_1748536988.html
2025-05-30 00:43:08 [main] INFO  c.i.provider.YahooFinanceScraper - Extracted 981 OHLCV data points for ADIA
2025-05-30 00:43:09 [main] INFO  c.i.database.DatabaseManager - Saved 981 OHLCV data points for ADIA
2025-05-30 00:43:09 [main] INFO  c.i.provider.YahooFinanceProvider - Downloaded and saved 981 data points for ADIA
2025-05-30 00:43:09 [main] INFO  c.investment.MarketDataDownloaderApp - ✓ Successfully processed ADIA
2025-05-30 00:43:09 [main] INFO  c.investment.MarketDataDownloaderApp - Processing instrument 139/10009: ADIL (ADIAL PHARMACEUTICALS, INC.)
2025-05-30 00:43:09 [main] INFO  c.investment.MarketDataDownloaderApp - Downloading data for ADIL from 1963-05-30 to 2025-05-30 (attempt 1/4)
2025-05-30 00:43:09 [main] INFO  c.i.provider.YahooFinanceProvider - Requesting data from Yahoo Finance for symbol: ADIL, from 1963-05-30 to 2025-05-30
2025-05-30 00:43:09 [main] INFO  c.i.provider.YahooFinanceScraper - Scraping data from Yahoo Finance: https://finance.yahoo.com/quote/ADIL/history/?period1=-*********&period2=**********&interval=1d
2025-05-30 00:43:11 [main] INFO  c.i.provider.YahooFinanceScraper - Saved HTML content to file: C:\Users\<USER>\Downloads\yahoo-finance-html\yahoo_finance_ADIL_1748536991.html
2025-05-30 00:43:11 [main] INFO  c.i.provider.YahooFinanceScraper - Read HTML content from file: C:\Users\<USER>\Downloads\yahoo-finance-html\yahoo_finance_ADIL_1748536991.html
2025-05-30 00:43:11 [main] INFO  c.i.provider.YahooFinanceScraper - Extracted 1719 OHLCV data points for ADIL
2025-05-30 00:43:12 [main] INFO  c.i.database.DatabaseManager - Saved 1719 OHLCV data points for ADIL
2025-05-30 00:43:12 [main] INFO  c.i.provider.YahooFinanceProvider - Downloaded and saved 1719 data points for ADIL
2025-05-30 00:43:12 [main] INFO  c.investment.MarketDataDownloaderApp - ✓ Successfully processed ADIL
2025-05-30 00:43:12 [main] INFO  c.investment.MarketDataDownloaderApp - Processing instrument 140/10009: ADM (Archer-Daniels-Midland Co)
2025-05-30 00:43:12 [main] INFO  c.investment.MarketDataDownloaderApp - Downloading data for ADM from 1963-05-30 to 2025-05-30 (attempt 1/4)
2025-05-30 00:43:12 [main] INFO  c.i.provider.YahooFinanceProvider - Requesting data from Yahoo Finance for symbol: ADM, from 1963-05-30 to 2025-05-30
2025-05-30 00:43:12 [main] INFO  c.i.provider.YahooFinanceScraper - Scraping data from Yahoo Finance: https://finance.yahoo.com/quote/ADM/history/?period1=-*********&period2=**********&interval=1d
2025-05-30 00:43:22 [main] INFO  c.i.provider.YahooFinanceScraper - Saved HTML content to file: C:\Users\<USER>\Downloads\yahoo-finance-html\yahoo_finance_ADM_1748537002.html
2025-05-30 00:43:22 [main] INFO  c.i.provider.YahooFinanceScraper - Read HTML content from file: C:\Users\<USER>\Downloads\yahoo-finance-html\yahoo_finance_ADM_1748537002.html
2025-05-30 00:43:22 [main] INFO  c.i.provider.YahooFinanceScraper - Extracted 11394 OHLCV data points for ADM
2025-05-30 00:43:25 [main] INFO  c.i.database.DatabaseManager - Saved 11394 OHLCV data points for ADM
2025-05-30 00:43:25 [main] INFO  c.i.provider.YahooFinanceProvider - Downloaded and saved 11394 data points for ADM
2025-05-30 00:43:25 [main] INFO  c.investment.MarketDataDownloaderApp - ✓ Successfully processed ADM
2025-05-30 00:43:26 [main] INFO  c.investment.MarketDataDownloaderApp - Processing instrument 141/10009: ADMA (ADMA BIOLOGICS, INC.)
2025-05-30 00:43:26 [main] INFO  c.investment.MarketDataDownloaderApp - Downloading data for ADMA from 1963-05-30 to 2025-05-30 (attempt 1/4)
2025-05-30 00:43:26 [main] INFO  c.i.provider.YahooFinanceProvider - Requesting data from Yahoo Finance for symbol: ADMA, from 1963-05-30 to 2025-05-30
2025-05-30 00:43:26 [main] INFO  c.i.provider.YahooFinanceScraper - Scraping data from Yahoo Finance: https://finance.yahoo.com/quote/ADMA/history/?period1=-*********&period2=**********&interval=1d
2025-05-30 00:43:29 [main] INFO  c.i.provider.YahooFinanceScraper - Saved HTML content to file: C:\Users\<USER>\Downloads\yahoo-finance-html\yahoo_finance_ADMA_1748537009.html
2025-05-30 00:43:29 [main] INFO  c.i.provider.YahooFinanceScraper - Read HTML content from file: C:\Users\<USER>\Downloads\yahoo-finance-html\yahoo_finance_ADMA_1748537009.html
2025-05-30 00:43:29 [main] INFO  c.i.provider.YahooFinanceScraper - Extracted 2888 OHLCV data points for ADMA
2025-05-30 00:43:30 [main] INFO  c.i.database.DatabaseManager - Saved 2888 OHLCV data points for ADMA
2025-05-30 00:43:30 [main] INFO  c.i.provider.YahooFinanceProvider - Downloaded and saved 2888 data points for ADMA
2025-05-30 00:43:30 [main] INFO  c.investment.MarketDataDownloaderApp - ✓ Successfully processed ADMA
2025-05-30 00:43:31 [main] INFO  c.investment.MarketDataDownloaderApp - Processing instrument 142/10009: ADMQ (ADM ENDEAVORS, INC.)
2025-05-30 00:43:31 [main] INFO  c.investment.MarketDataDownloaderApp - Downloading data for ADMQ from 1963-05-30 to 2025-05-30 (attempt 1/4)
2025-05-30 00:43:31 [main] INFO  c.i.provider.YahooFinanceProvider - Requesting data from Yahoo Finance for symbol: ADMQ, from 1963-05-30 to 2025-05-30
2025-05-30 00:43:31 [main] INFO  c.i.provider.YahooFinanceScraper - Scraping data from Yahoo Finance: https://finance.yahoo.com/quote/ADMQ/history/?period1=-*********&period2=**********&interval=1d
2025-05-30 00:43:33 [main] INFO  c.i.provider.YahooFinanceScraper - Saved HTML content to file: C:\Users\<USER>\Downloads\yahoo-finance-html\yahoo_finance_ADMQ_1748537013.html
2025-05-30 00:43:33 [main] INFO  c.i.provider.YahooFinanceScraper - Read HTML content from file: C:\Users\<USER>\Downloads\yahoo-finance-html\yahoo_finance_ADMQ_1748537013.html
2025-05-30 00:43:33 [main] INFO  c.i.provider.YahooFinanceScraper - Extracted 1089 OHLCV data points for ADMQ
2025-05-30 00:43:33 [main] INFO  c.i.database.DatabaseManager - Saved 1089 OHLCV data points for ADMQ
2025-05-30 00:43:33 [main] INFO  c.i.provider.YahooFinanceProvider - Downloaded and saved 1089 data points for ADMQ
2025-05-30 00:43:33 [main] INFO  c.investment.MarketDataDownloaderApp - ✓ Successfully processed ADMQ
2025-05-30 00:43:34 [main] INFO  c.investment.MarketDataDownloaderApp - Processing instrument 143/10009: ADMT (ADM TRONICS UNLIMITED, INC.)
2025-05-30 00:43:34 [main] INFO  c.investment.MarketDataDownloaderApp - Downloading data for ADMT from 1963-05-30 to 2025-05-30 (attempt 1/4)
2025-05-30 00:43:34 [main] INFO  c.i.provider.YahooFinanceProvider - Requesting data from Yahoo Finance for symbol: ADMT, from 1963-05-30 to 2025-05-30
2025-05-30 00:43:34 [main] INFO  c.i.provider.YahooFinanceScraper - Scraping data from Yahoo Finance: https://finance.yahoo.com/quote/ADMT/history/?period1=-*********&period2=**********&interval=1d
2025-05-30 00:43:40 [main] INFO  c.i.provider.YahooFinanceScraper - Saved HTML content to file: C:\Users\<USER>\Downloads\yahoo-finance-html\yahoo_finance_ADMT_1748537019.html
2025-05-30 00:43:40 [main] INFO  c.i.provider.YahooFinanceScraper - Read HTML content from file: C:\Users\<USER>\Downloads\yahoo-finance-html\yahoo_finance_ADMT_1748537019.html
2025-05-30 00:43:40 [main] INFO  c.i.provider.YahooFinanceScraper - Extracted 7239 OHLCV data points for ADMT
2025-05-30 00:43:42 [main] INFO  c.i.database.DatabaseManager - Saved 7239 OHLCV data points for ADMT
2025-05-30 00:43:42 [main] INFO  c.i.provider.YahooFinanceProvider - Downloaded and saved 7239 data points for ADMT
2025-05-30 00:43:42 [main] INFO  c.investment.MarketDataDownloaderApp - ✓ Successfully processed ADMT
2025-05-30 00:43:42 [main] INFO  c.investment.MarketDataDownloaderApp - Processing instrument 144/10009: ADN (ADVENT TECHNOLOGIES HOLDINGS, INC.)
2025-05-30 00:43:42 [main] INFO  c.investment.MarketDataDownloaderApp - Downloading data for ADN from 1963-05-30 to 2025-05-30 (attempt 1/4)
2025-05-30 00:43:42 [main] INFO  c.i.provider.YahooFinanceProvider - Requesting data from Yahoo Finance for symbol: ADN, from 1963-05-30 to 2025-05-30
2025-05-30 00:43:42 [main] INFO  c.i.provider.YahooFinanceScraper - Scraping data from Yahoo Finance: https://finance.yahoo.com/quote/ADN/history/?period1=-*********&period2=**********&interval=1d
2025-05-30 00:43:45 [main] INFO  c.i.provider.YahooFinanceScraper - Saved HTML content to file: C:\Users\<USER>\Downloads\yahoo-finance-html\yahoo_finance_ADN_1748537025.html
2025-05-30 00:43:45 [main] INFO  c.i.provider.YahooFinanceScraper - Read HTML content from file: C:\Users\<USER>\Downloads\yahoo-finance-html\yahoo_finance_ADN_1748537025.html
2025-05-30 00:43:45 [main] INFO  c.i.provider.YahooFinanceScraper - Extracted 1476 OHLCV data points for ADN
2025-05-30 00:43:45 [main] INFO  c.i.database.DatabaseManager - Saved 1476 OHLCV data points for ADN
2025-05-30 00:43:45 [main] INFO  c.i.provider.YahooFinanceProvider - Downloaded and saved 1476 data points for ADN
2025-05-30 00:43:45 [main] INFO  c.investment.MarketDataDownloaderApp - ✓ Successfully processed ADN
2025-05-30 00:43:46 [main] INFO  c.investment.MarketDataDownloaderApp - Processing instrument 145/10009: ADNT (Adient plc)
2025-05-30 00:43:46 [main] INFO  c.investment.MarketDataDownloaderApp - Downloading data for ADNT from 1963-05-30 to 2025-05-30 (attempt 1/4)
2025-05-30 00:43:46 [main] INFO  c.i.provider.YahooFinanceProvider - Requesting data from Yahoo Finance for symbol: ADNT, from 1963-05-30 to 2025-05-30
2025-05-30 00:43:46 [main] INFO  c.i.provider.YahooFinanceScraper - Scraping data from Yahoo Finance: https://finance.yahoo.com/quote/ADNT/history/?period1=-*********&period2=**********&interval=1d
2025-05-30 00:43:48 [main] INFO  c.i.provider.YahooFinanceScraper - Saved HTML content to file: C:\Users\<USER>\Downloads\yahoo-finance-html\yahoo_finance_ADNT_1748537028.html
2025-05-30 00:43:48 [main] INFO  c.i.provider.YahooFinanceScraper - Read HTML content from file: C:\Users\<USER>\Downloads\yahoo-finance-html\yahoo_finance_ADNT_1748537028.html
2025-05-30 00:43:48 [main] INFO  c.i.provider.YahooFinanceScraper - Extracted 2165 OHLCV data points for ADNT
2025-05-30 00:43:49 [main] INFO  c.i.database.DatabaseManager - Saved 2165 OHLCV data points for ADNT
2025-05-30 00:43:49 [main] INFO  c.i.provider.YahooFinanceProvider - Downloaded and saved 2165 data points for ADNT
2025-05-30 00:43:49 [main] INFO  c.investment.MarketDataDownloaderApp - ✓ Successfully processed ADNT
2025-05-30 00:43:49 [main] INFO  c.investment.MarketDataDownloaderApp - Processing instrument 146/10009: ADNWW (ADVENT TECHNOLOGIES HOLDINGS, INC.)
2025-05-30 00:43:49 [main] INFO  c.investment.MarketDataDownloaderApp - Downloading data for ADNWW from 1963-05-30 to 2025-05-30 (attempt 1/4)
2025-05-30 00:43:49 [main] INFO  c.i.provider.YahooFinanceProvider - Requesting data from Yahoo Finance for symbol: ADNWW, from 1963-05-30 to 2025-05-30
2025-05-30 00:43:49 [main] INFO  c.i.provider.YahooFinanceScraper - Scraping data from Yahoo Finance: https://finance.yahoo.com/quote/ADNWW/history/?period1=-*********&period2=**********&interval=1d
2025-05-30 00:43:51 [main] INFO  c.i.provider.YahooFinanceScraper - Saved HTML content to file: C:\Users\<USER>\Downloads\yahoo-finance-html\yahoo_finance_ADNWW_1748537031.html
2025-05-30 00:43:51 [main] INFO  c.i.provider.YahooFinanceScraper - Read HTML content from file: C:\Users\<USER>\Downloads\yahoo-finance-html\yahoo_finance_ADNWW_1748537031.html
2025-05-30 00:43:51 [main] INFO  c.i.provider.YahooFinanceScraper - Extracted 1 OHLCV data points for ADNWW
2025-05-30 00:43:51 [main] INFO  c.i.database.DatabaseManager - Saved 1 OHLCV data points for ADNWW
2025-05-30 00:43:51 [main] INFO  c.i.provider.YahooFinanceProvider - Downloaded and saved 1 data points for ADNWW
2025-05-30 00:43:51 [main] INFO  c.investment.MarketDataDownloaderApp - ✓ Successfully processed ADNWW
2025-05-30 00:43:51 [main] INFO  c.investment.MarketDataDownloaderApp - Processing instrument 147/10009: ADOOY (Adaro Energy PT/ADR/)
2025-05-30 00:43:51 [main] INFO  c.investment.MarketDataDownloaderApp - Downloading data for ADOOY from 1963-05-30 to 2025-05-30 (attempt 1/4)
2025-05-30 00:43:51 [main] INFO  c.i.provider.YahooFinanceProvider - Requesting data from Yahoo Finance for symbol: ADOOY, from 1963-05-30 to 2025-05-30
2025-05-30 00:43:51 [main] INFO  c.i.provider.YahooFinanceScraper - Scraping data from Yahoo Finance: https://finance.yahoo.com/quote/ADOOY/history/?period1=-*********&period2=**********&interval=1d
2025-05-30 00:43:54 [main] INFO  c.i.provider.YahooFinanceScraper - Saved HTML content to file: C:\Users\<USER>\Downloads\yahoo-finance-html\yahoo_finance_ADOOY_1748537034.html
2025-05-30 00:43:54 [main] INFO  c.i.provider.YahooFinanceScraper - Read HTML content from file: C:\Users\<USER>\Downloads\yahoo-finance-html\yahoo_finance_ADOOY_1748537034.html
2025-05-30 00:43:54 [main] INFO  c.i.provider.YahooFinanceScraper - Extracted 1253 OHLCV data points for ADOOY
2025-05-30 00:43:55 [main] INFO  c.i.database.DatabaseManager - Saved 1253 OHLCV data points for ADOOY
2025-05-30 00:43:55 [main] INFO  c.i.provider.YahooFinanceProvider - Downloaded and saved 1253 data points for ADOOY
2025-05-30 00:43:55 [main] INFO  c.investment.MarketDataDownloaderApp - ✓ Successfully processed ADOOY
2025-05-30 00:43:55 [main] INFO  c.investment.MarketDataDownloaderApp - Processing instrument 148/10009: ADP (AUTOMATIC DATA PROCESSING INC)
2025-05-30 00:43:55 [main] INFO  c.investment.MarketDataDownloaderApp - Downloading data for ADP from 1963-05-30 to 2025-05-30 (attempt 1/4)
2025-05-30 00:43:55 [main] INFO  c.i.provider.YahooFinanceProvider - Requesting data from Yahoo Finance for symbol: ADP, from 1963-05-30 to 2025-05-30
2025-05-30 00:43:55 [main] INFO  c.i.provider.YahooFinanceScraper - Scraping data from Yahoo Finance: https://finance.yahoo.com/quote/ADP/history/?period1=-*********&period2=**********&interval=1d
2025-05-30 00:44:02 [main] INFO  c.i.provider.YahooFinanceScraper - Saved HTML content to file: C:\Users\<USER>\Downloads\yahoo-finance-html\yahoo_finance_ADP_1748537042.html
2025-05-30 00:44:02 [main] INFO  c.i.provider.YahooFinanceScraper - Read HTML content from file: C:\Users\<USER>\Downloads\yahoo-finance-html\yahoo_finance_ADP_1748537042.html
2025-05-30 00:44:02 [main] INFO  c.i.provider.YahooFinanceScraper - Extracted 11394 OHLCV data points for ADP
2025-05-30 00:44:06 [main] INFO  c.i.database.DatabaseManager - Saved 11394 OHLCV data points for ADP
2025-05-30 00:44:06 [main] INFO  c.i.provider.YahooFinanceProvider - Downloaded and saved 11394 data points for ADP
2025-05-30 00:44:06 [main] INFO  c.investment.MarketDataDownloaderApp - ✓ Successfully processed ADP
2025-05-30 00:44:06 [main] INFO  c.investment.MarketDataDownloaderApp - Processing instrument 149/10009: ADPT (Adaptive Biotechnologies Corp)
2025-05-30 00:44:06 [main] INFO  c.investment.MarketDataDownloaderApp - Downloading data for ADPT from 1963-05-30 to 2025-05-30 (attempt 1/4)
2025-05-30 00:44:06 [main] INFO  c.i.provider.YahooFinanceProvider - Requesting data from Yahoo Finance for symbol: ADPT, from 1963-05-30 to 2025-05-30
2025-05-30 00:44:06 [main] INFO  c.i.provider.YahooFinanceScraper - Scraping data from Yahoo Finance: https://finance.yahoo.com/quote/ADPT/history/?period1=-*********&period2=**********&interval=1d
2025-05-30 00:44:08 [main] INFO  c.i.provider.YahooFinanceScraper - Saved HTML content to file: C:\Users\<USER>\Downloads\yahoo-finance-html\yahoo_finance_ADPT_1748537048.html
2025-05-30 00:44:08 [main] INFO  c.i.provider.YahooFinanceScraper - Read HTML content from file: C:\Users\<USER>\Downloads\yahoo-finance-html\yahoo_finance_ADPT_1748537048.html
2025-05-30 00:44:08 [main] INFO  c.i.provider.YahooFinanceScraper - Extracted 1489 OHLCV data points for ADPT
2025-05-30 00:44:08 [main] INFO  c.i.database.DatabaseManager - Saved 1489 OHLCV data points for ADPT
2025-05-30 00:44:08 [main] INFO  c.i.provider.YahooFinanceProvider - Downloaded and saved 1489 data points for ADPT
2025-05-30 00:44:08 [main] INFO  c.investment.MarketDataDownloaderApp - ✓ Successfully processed ADPT
2025-05-30 00:44:09 [main] INFO  c.investment.MarketDataDownloaderApp - Processing instrument 150/10009: ADSE (Ads-Tec Energy Public Ltd Co)
2025-05-30 00:44:09 [main] INFO  c.investment.MarketDataDownloaderApp - Downloading data for ADSE from 1963-05-30 to 2025-05-30 (attempt 1/4)
2025-05-30 00:44:09 [main] INFO  c.i.provider.YahooFinanceProvider - Requesting data from Yahoo Finance for symbol: ADSE, from 1963-05-30 to 2025-05-30
2025-05-30 00:44:09 [main] INFO  c.i.provider.YahooFinanceScraper - Scraping data from Yahoo Finance: https://finance.yahoo.com/quote/ADSE/history/?period1=-*********&period2=**********&interval=1d
2025-05-30 00:44:11 [main] INFO  c.i.provider.YahooFinanceScraper - Saved HTML content to file: C:\Users\<USER>\Downloads\yahoo-finance-html\yahoo_finance_ADSE_1748537051.html
2025-05-30 00:44:11 [main] INFO  c.i.provider.YahooFinanceScraper - Read HTML content from file: C:\Users\<USER>\Downloads\yahoo-finance-html\yahoo_finance_ADSE_1748537051.html
2025-05-30 00:44:11 [main] INFO  c.i.provider.YahooFinanceScraper - Extracted 1052 OHLCV data points for ADSE
2025-05-30 00:44:11 [main] INFO  c.i.database.DatabaseManager - Saved 1052 OHLCV data points for ADSE
2025-05-30 00:44:11 [main] INFO  c.i.provider.YahooFinanceProvider - Downloaded and saved 1052 data points for ADSE
2025-05-30 00:44:11 [main] INFO  c.investment.MarketDataDownloaderApp - ✓ Successfully processed ADSE
2025-05-30 00:44:11 [main] INFO  c.investment.MarketDataDownloaderApp - Processing instrument 151/10009: ADSEW (Ads-Tec Energy Public Ltd Co)
2025-05-30 00:44:11 [main] INFO  c.investment.MarketDataDownloaderApp - Downloading data for ADSEW from 1963-05-30 to 2025-05-30 (attempt 1/4)
2025-05-30 00:44:11 [main] INFO  c.i.provider.YahooFinanceProvider - Requesting data from Yahoo Finance for symbol: ADSEW, from 1963-05-30 to 2025-05-30
2025-05-30 00:44:11 [main] INFO  c.i.provider.YahooFinanceScraper - Scraping data from Yahoo Finance: https://finance.yahoo.com/quote/ADSEW/history/?period1=-*********&period2=**********&interval=1d
2025-05-30 00:44:12 [main] INFO  c.i.provider.YahooFinanceScraper - Saved HTML content to file: C:\Users\<USER>\Downloads\yahoo-finance-html\yahoo_finance_ADSEW_1748537052.html
2025-05-30 00:44:12 [main] INFO  c.i.provider.YahooFinanceScraper - Read HTML content from file: C:\Users\<USER>\Downloads\yahoo-finance-html\yahoo_finance_ADSEW_1748537052.html
2025-05-30 00:44:12 [main] INFO  c.i.provider.YahooFinanceScraper - Extracted 1 OHLCV data points for ADSEW
2025-05-30 00:44:12 [main] INFO  c.i.database.DatabaseManager - Saved 1 OHLCV data points for ADSEW
2025-05-30 00:44:12 [main] INFO  c.i.provider.YahooFinanceProvider - Downloaded and saved 1 data points for ADSEW
2025-05-30 00:44:12 [main] INFO  c.investment.MarketDataDownloaderApp - ✓ Successfully processed ADSEW
2025-05-30 00:44:13 [main] INFO  c.investment.MarketDataDownloaderApp - Processing instrument 152/10009: ADSK (Autodesk, Inc.)
2025-05-30 00:44:13 [main] INFO  c.investment.MarketDataDownloaderApp - Downloading data for ADSK from 1963-05-30 to 2025-05-30 (attempt 1/4)
2025-05-30 00:44:13 [main] INFO  c.i.provider.YahooFinanceProvider - Requesting data from Yahoo Finance for symbol: ADSK, from 1963-05-30 to 2025-05-30
2025-05-30 00:44:13 [main] INFO  c.i.provider.YahooFinanceScraper - Scraping data from Yahoo Finance: https://finance.yahoo.com/quote/ADSK/history/?period1=-*********&period2=**********&interval=1d
2025-05-30 00:44:20 [main] INFO  c.i.provider.YahooFinanceScraper - Saved HTML content to file: C:\Users\<USER>\Downloads\yahoo-finance-html\yahoo_finance_ADSK_1748537060.html
2025-05-30 00:44:20 [main] INFO  c.i.provider.YahooFinanceScraper - Read HTML content from file: C:\Users\<USER>\Downloads\yahoo-finance-html\yahoo_finance_ADSK_1748537060.html
2025-05-30 00:44:20 [main] INFO  c.i.provider.YahooFinanceScraper - Extracted 10057 OHLCV data points for ADSK
2025-05-30 00:44:23 [main] INFO  c.i.database.DatabaseManager - Saved 10057 OHLCV data points for ADSK
2025-05-30 00:44:23 [main] INFO  c.i.provider.YahooFinanceProvider - Downloaded and saved 10057 data points for ADSK
2025-05-30 00:44:23 [main] INFO  c.investment.MarketDataDownloaderApp - ✓ Successfully processed ADSK
2025-05-30 00:44:24 [main] INFO  c.investment.MarketDataDownloaderApp - Processing instrument 153/10009: ADT (ADT Inc.)
2025-05-30 00:44:24 [main] INFO  c.investment.MarketDataDownloaderApp - Downloading data for ADT from 1963-05-30 to 2025-05-30 (attempt 1/4)
2025-05-30 00:44:24 [main] INFO  c.i.provider.YahooFinanceProvider - Requesting data from Yahoo Finance for symbol: ADT, from 1963-05-30 to 2025-05-30
2025-05-30 00:44:24 [main] INFO  c.i.provider.YahooFinanceScraper - Scraping data from Yahoo Finance: https://finance.yahoo.com/quote/ADT/history/?period1=-*********&period2=**********&interval=1d
2025-05-30 00:44:27 [main] INFO  c.i.provider.YahooFinanceScraper - Saved HTML content to file: C:\Users\<USER>\Downloads\yahoo-finance-html\yahoo_finance_ADT_1748537067.html
2025-05-30 00:44:27 [main] INFO  c.i.provider.YahooFinanceScraper - Read HTML content from file: C:\Users\<USER>\Downloads\yahoo-finance-html\yahoo_finance_ADT_1748537067.html
2025-05-30 00:44:27 [main] INFO  c.i.provider.YahooFinanceScraper - Extracted 1850 OHLCV data points for ADT
2025-05-30 00:44:27 [main] INFO  c.i.database.DatabaseManager - Saved 1850 OHLCV data points for ADT
2025-05-30 00:44:27 [main] INFO  c.i.provider.YahooFinanceProvider - Downloaded and saved 1850 data points for ADT
2025-05-30 00:44:27 [main] INFO  c.investment.MarketDataDownloaderApp - ✓ Successfully processed ADT
2025-05-30 00:44:28 [main] INFO  c.investment.MarketDataDownloaderApp - Processing instrument 154/10009: ADTN (ADTRAN Holdings, Inc.)
2025-05-30 00:44:28 [main] INFO  c.investment.MarketDataDownloaderApp - Downloading data for ADTN from 1963-05-30 to 2025-05-30 (attempt 1/4)
2025-05-30 00:44:28 [main] INFO  c.i.provider.YahooFinanceProvider - Requesting data from Yahoo Finance for symbol: ADTN, from 1963-05-30 to 2025-05-30
2025-05-30 00:44:28 [main] INFO  c.i.provider.YahooFinanceScraper - Scraping data from Yahoo Finance: https://finance.yahoo.com/quote/ADTN/history/?period1=-*********&period2=**********&interval=1d
2025-05-30 00:44:36 [main] INFO  c.i.provider.YahooFinanceScraper - Saved HTML content to file: C:\Users\<USER>\Downloads\yahoo-finance-html\yahoo_finance_ADTN_1748537076.html
2025-05-30 00:44:36 [main] INFO  c.i.provider.YahooFinanceScraper - Read HTML content from file: C:\Users\<USER>\Downloads\yahoo-finance-html\yahoo_finance_ADTN_1748537076.html
2025-05-30 00:44:36 [main] INFO  c.i.provider.YahooFinanceScraper - Extracted 7753 OHLCV data points for ADTN
2025-05-30 00:44:38 [main] INFO  c.i.database.DatabaseManager - Saved 7753 OHLCV data points for ADTN
2025-05-30 00:44:38 [main] INFO  c.i.provider.YahooFinanceProvider - Downloaded and saved 7753 data points for ADTN
2025-05-30 00:44:38 [main] INFO  c.investment.MarketDataDownloaderApp - ✓ Successfully processed ADTN
2025-05-30 00:44:38 [main] INFO  c.investment.MarketDataDownloaderApp - Processing instrument 155/10009: ADTTF (ADVANTEST CORP)
2025-05-30 00:44:38 [main] INFO  c.investment.MarketDataDownloaderApp - Downloading data for ADTTF from 1963-05-30 to 2025-05-30 (attempt 1/4)
2025-05-30 00:44:38 [main] INFO  c.i.provider.YahooFinanceProvider - Requesting data from Yahoo Finance for symbol: ADTTF, from 1963-05-30 to 2025-05-30
2025-05-30 00:44:38 [main] INFO  c.i.provider.YahooFinanceScraper - Scraping data from Yahoo Finance: https://finance.yahoo.com/quote/ADTTF/history/?period1=-*********&period2=**********&interval=1d
2025-05-30 00:44:42 [main] INFO  c.i.provider.YahooFinanceScraper - Saved HTML content to file: C:\Users\<USER>\Downloads\yahoo-finance-html\yahoo_finance_ADTTF_1748537082.html
2025-05-30 00:44:42 [main] INFO  c.i.provider.YahooFinanceScraper - Read HTML content from file: C:\Users\<USER>\Downloads\yahoo-finance-html\yahoo_finance_ADTTF_1748537082.html
2025-05-30 00:44:42 [main] INFO  c.i.provider.YahooFinanceScraper - Extracted 496 OHLCV data points for ADTTF
2025-05-30 00:44:42 [main] INFO  c.i.database.DatabaseManager - Saved 496 OHLCV data points for ADTTF
2025-05-30 00:44:42 [main] INFO  c.i.provider.YahooFinanceProvider - Downloaded and saved 496 data points for ADTTF
2025-05-30 00:44:42 [main] INFO  c.investment.MarketDataDownloaderApp - ✓ Successfully processed ADTTF
2025-05-30 00:44:42 [main] INFO  c.investment.MarketDataDownloaderApp - Processing instrument 156/10009: ADTX (Aditxt, Inc.)
2025-05-30 00:44:42 [main] INFO  c.investment.MarketDataDownloaderApp - Downloading data for ADTX from 1963-05-30 to 2025-05-30 (attempt 1/4)
2025-05-30 00:44:42 [main] INFO  c.i.provider.YahooFinanceProvider - Requesting data from Yahoo Finance for symbol: ADTX, from 1963-05-30 to 2025-05-30
2025-05-30 00:44:42 [main] INFO  c.i.provider.YahooFinanceScraper - Scraping data from Yahoo Finance: https://finance.yahoo.com/quote/ADTX/history/?period1=-*********&period2=**********&interval=1d
2025-05-30 00:44:44 [main] INFO  c.i.provider.YahooFinanceScraper - Saved HTML content to file: C:\Users\<USER>\Downloads\yahoo-finance-html\yahoo_finance_ADTX_1748537084.html
2025-05-30 00:44:44 [main] INFO  c.i.provider.YahooFinanceScraper - Read HTML content from file: C:\Users\<USER>\Downloads\yahoo-finance-html\yahoo_finance_ADTX_1748537084.html
2025-05-30 00:44:44 [main] INFO  c.i.provider.YahooFinanceScraper - Extracted 537 OHLCV data points for ADTX
2025-05-30 00:44:44 [main] INFO  c.i.database.DatabaseManager - Saved 537 OHLCV data points for ADTX
2025-05-30 00:44:44 [main] INFO  c.i.provider.YahooFinanceProvider - Downloaded and saved 537 data points for ADTX
2025-05-30 00:44:44 [main] INFO  c.investment.MarketDataDownloaderApp - ✓ Successfully processed ADTX
2025-05-30 00:44:44 [main] INFO  c.investment.MarketDataDownloaderApp - Processing instrument 157/10009: ADUR (ADURO CLEAN TECHNOLOGIES INC.)
2025-05-30 00:44:44 [main] INFO  c.investment.MarketDataDownloaderApp - Downloading data for ADUR from 1963-05-30 to 2025-05-30 (attempt 1/4)
2025-05-30 00:44:44 [main] INFO  c.i.provider.YahooFinanceProvider - Requesting data from Yahoo Finance for symbol: ADUR, from 1963-05-30 to 2025-05-30
2025-05-30 00:44:44 [main] INFO  c.i.provider.YahooFinanceScraper - Scraping data from Yahoo Finance: https://finance.yahoo.com/quote/ADUR/history/?period1=-*********&period2=**********&interval=1d
2025-05-30 00:44:46 [main] INFO  c.i.provider.YahooFinanceScraper - Saved HTML content to file: C:\Users\<USER>\Downloads\yahoo-finance-html\yahoo_finance_ADUR_1748537086.html
2025-05-30 00:44:46 [main] INFO  c.i.provider.YahooFinanceScraper - Read HTML content from file: C:\Users\<USER>\Downloads\yahoo-finance-html\yahoo_finance_ADUR_1748537086.html
2025-05-30 00:44:46 [main] INFO  c.i.provider.YahooFinanceScraper - Extracted 197 OHLCV data points for ADUR
2025-05-30 00:44:46 [main] INFO  c.i.database.DatabaseManager - Saved 197 OHLCV data points for ADUR
2025-05-30 00:44:46 [main] INFO  c.i.provider.YahooFinanceProvider - Downloaded and saved 197 data points for ADUR
2025-05-30 00:44:46 [main] INFO  c.investment.MarketDataDownloaderApp - ✓ Successfully processed ADUR
2025-05-30 00:44:47 [main] INFO  c.investment.MarketDataDownloaderApp - Processing instrument 158/10009: ADUS (Addus HomeCare Corp)
2025-05-30 00:44:47 [main] INFO  c.investment.MarketDataDownloaderApp - Downloading data for ADUS from 1963-05-30 to 2025-05-30 (attempt 1/4)
2025-05-30 00:44:47 [main] INFO  c.i.provider.YahooFinanceProvider - Requesting data from Yahoo Finance for symbol: ADUS, from 1963-05-30 to 2025-05-30
2025-05-30 00:44:47 [main] INFO  c.i.provider.YahooFinanceScraper - Scraping data from Yahoo Finance: https://finance.yahoo.com/quote/ADUS/history/?period1=-*********&period2=**********&interval=1d
2025-05-30 00:44:50 [main] INFO  c.i.provider.YahooFinanceScraper - Saved HTML content to file: C:\Users\<USER>\Downloads\yahoo-finance-html\yahoo_finance_ADUS_1748537090.html
2025-05-30 00:44:50 [main] INFO  c.i.provider.YahooFinanceScraper - Read HTML content from file: C:\Users\<USER>\Downloads\yahoo-finance-html\yahoo_finance_ADUS_1748537090.html
2025-05-30 00:44:50 [main] INFO  c.i.provider.YahooFinanceScraper - Extracted 3918 OHLCV data points for ADUS
2025-05-30 00:44:51 [main] INFO  c.i.database.DatabaseManager - Saved 3918 OHLCV data points for ADUS
2025-05-30 00:44:51 [main] INFO  c.i.provider.YahooFinanceProvider - Downloaded and saved 3918 data points for ADUS
2025-05-30 00:44:51 [main] INFO  c.investment.MarketDataDownloaderApp - ✓ Successfully processed ADUS
2025-05-30 00:44:51 [main] INFO  c.investment.MarketDataDownloaderApp - Processing instrument 159/10009: ADV (Advantage Solutions Inc.)
2025-05-30 00:44:51 [main] INFO  c.investment.MarketDataDownloaderApp - Downloading data for ADV from 1963-05-30 to 2025-05-30 (attempt 1/4)
2025-05-30 00:44:51 [main] INFO  c.i.provider.YahooFinanceProvider - Requesting data from Yahoo Finance for symbol: ADV, from 1963-05-30 to 2025-05-30
2025-05-30 00:44:51 [main] INFO  c.i.provider.YahooFinanceScraper - Scraping data from Yahoo Finance: https://finance.yahoo.com/quote/ADV/history/?period1=-*********&period2=**********&interval=1d
2025-05-30 00:44:54 [main] INFO  c.i.provider.YahooFinanceScraper - Saved HTML content to file: C:\Users\<USER>\Downloads\yahoo-finance-html\yahoo_finance_ADV_1748537094.html
2025-05-30 00:44:54 [main] INFO  c.i.provider.YahooFinanceScraper - Read HTML content from file: C:\Users\<USER>\Downloads\yahoo-finance-html\yahoo_finance_ADV_1748537094.html
2025-05-30 00:44:54 [main] INFO  c.i.provider.YahooFinanceScraper - Extracted 1308 OHLCV data points for ADV
2025-05-30 00:44:54 [main] INFO  c.i.database.DatabaseManager - Saved 1308 OHLCV data points for ADV
2025-05-30 00:44:54 [main] INFO  c.i.provider.YahooFinanceProvider - Downloaded and saved 1308 data points for ADV
2025-05-30 00:44:54 [main] INFO  c.investment.MarketDataDownloaderApp - ✓ Successfully processed ADV
2025-05-30 00:44:54 [main] INFO  c.investment.MarketDataDownloaderApp - Processing instrument 160/10009: ADVB (Advanced Biomed Inc.)
2025-05-30 00:44:54 [main] INFO  c.investment.MarketDataDownloaderApp - Downloading data for ADVB from 1963-05-30 to 2025-05-30 (attempt 1/4)
2025-05-30 00:44:54 [main] INFO  c.i.provider.YahooFinanceProvider - Requesting data from Yahoo Finance for symbol: ADVB, from 1963-05-30 to 2025-05-30
2025-05-30 00:44:54 [main] INFO  c.i.provider.YahooFinanceScraper - Scraping data from Yahoo Finance: https://finance.yahoo.com/quote/ADVB/history/?period1=-*********&period2=**********&interval=1d
2025-05-30 00:44:56 [main] INFO  c.i.provider.YahooFinanceScraper - Saved HTML content to file: C:\Users\<USER>\Downloads\yahoo-finance-html\yahoo_finance_ADVB_1748537096.html
2025-05-30 00:44:56 [main] INFO  c.i.provider.YahooFinanceScraper - Read HTML content from file: C:\Users\<USER>\Downloads\yahoo-finance-html\yahoo_finance_ADVB_1748537096.html
2025-05-30 00:44:56 [main] INFO  c.i.provider.YahooFinanceScraper - Extracted 59 OHLCV data points for ADVB
2025-05-30 00:44:56 [main] INFO  c.i.database.DatabaseManager - Saved 59 OHLCV data points for ADVB
2025-05-30 00:44:56 [main] INFO  c.i.provider.YahooFinanceProvider - Downloaded and saved 59 data points for ADVB
2025-05-30 00:44:56 [main] INFO  c.investment.MarketDataDownloaderApp - ✓ Successfully processed ADVB
2025-05-30 00:44:57 [main] INFO  c.investment.MarketDataDownloaderApp - Processing instrument 161/10009: ADVM (Adverum Biotechnologies, Inc.)
2025-05-30 00:44:57 [main] INFO  c.investment.MarketDataDownloaderApp - Downloading data for ADVM from 1963-05-30 to 2025-05-30 (attempt 1/4)
2025-05-30 00:44:57 [main] INFO  c.i.provider.YahooFinanceProvider - Requesting data from Yahoo Finance for symbol: ADVM, from 1963-05-30 to 2025-05-30
2025-05-30 00:44:57 [main] INFO  c.i.provider.YahooFinanceScraper - Scraping data from Yahoo Finance: https://finance.yahoo.com/quote/ADVM/history/?period1=-*********&period2=**********&interval=1d
2025-05-30 00:45:00 [main] INFO  c.i.provider.YahooFinanceScraper - Saved HTML content to file: C:\Users\<USER>\Downloads\yahoo-finance-html\yahoo_finance_ADVM_1748537100.html
2025-05-30 00:45:00 [main] INFO  c.i.provider.YahooFinanceScraper - Read HTML content from file: C:\Users\<USER>\Downloads\yahoo-finance-html\yahoo_finance_ADVM_1748537100.html
2025-05-30 00:45:00 [main] INFO  c.i.provider.YahooFinanceScraper - Extracted 2724 OHLCV data points for ADVM
2025-05-30 00:45:01 [main] INFO  c.i.database.DatabaseManager - Saved 2724 OHLCV data points for ADVM
2025-05-30 00:45:01 [main] INFO  c.i.provider.YahooFinanceProvider - Downloaded and saved 2724 data points for ADVM
2025-05-30 00:45:01 [main] INFO  c.investment.MarketDataDownloaderApp - ✓ Successfully processed ADVM
2025-05-30 00:45:02 [main] INFO  c.investment.MarketDataDownloaderApp - Processing instrument 162/10009: ADVWW (Advantage Solutions Inc.)
2025-05-30 00:45:02 [main] INFO  c.investment.MarketDataDownloaderApp - Downloading data for ADVWW from 1963-05-30 to 2025-05-30 (attempt 1/4)
2025-05-30 00:45:02 [main] INFO  c.i.provider.YahooFinanceProvider - Requesting data from Yahoo Finance for symbol: ADVWW, from 1963-05-30 to 2025-05-30
2025-05-30 00:45:02 [main] INFO  c.i.provider.YahooFinanceScraper - Scraping data from Yahoo Finance: https://finance.yahoo.com/quote/ADVWW/history/?period1=-*********&period2=**********&interval=1d
2025-05-30 00:45:03 [main] INFO  c.i.provider.YahooFinanceScraper - Saved HTML content to file: C:\Users\<USER>\Downloads\yahoo-finance-html\yahoo_finance_ADVWW_1748537103.html
2025-05-30 00:45:03 [main] INFO  c.i.provider.YahooFinanceScraper - Read HTML content from file: C:\Users\<USER>\Downloads\yahoo-finance-html\yahoo_finance_ADVWW_1748537103.html
2025-05-30 00:45:03 [main] INFO  c.i.provider.YahooFinanceScraper - Extracted 1 OHLCV data points for ADVWW
2025-05-30 00:45:03 [main] INFO  c.i.database.DatabaseManager - Saved 1 OHLCV data points for ADVWW
2025-05-30 00:45:03 [main] INFO  c.i.provider.YahooFinanceProvider - Downloaded and saved 1 data points for ADVWW
2025-05-30 00:45:03 [main] INFO  c.investment.MarketDataDownloaderApp - ✓ Successfully processed ADVWW
2025-05-30 00:45:04 [main] INFO  c.investment.MarketDataDownloaderApp - Processing instrument 163/10009: ADX (ADAMS DIVERSIFIED EQUITY FUND, INC.)
2025-05-30 00:45:04 [main] INFO  c.investment.MarketDataDownloaderApp - Downloading data for ADX from 1963-05-30 to 2025-05-30 (attempt 1/4)
2025-05-30 00:45:04 [main] INFO  c.i.provider.YahooFinanceProvider - Requesting data from Yahoo Finance for symbol: ADX, from 1963-05-30 to 2025-05-30
2025-05-30 00:45:04 [main] INFO  c.i.provider.YahooFinanceScraper - Scraping data from Yahoo Finance: https://finance.yahoo.com/quote/ADX/history/?period1=-*********&period2=**********&interval=1d
2025-05-30 00:45:14 [main] ERROR c.i.provider.YahooFinanceProvider - Error downloading data for instrument: ADX
java.io.IOException: Failed to download data: Response{protocol=h2, code=404, message=, url=https://finance.yahoo.com/quote/ADX/history/?period1=-*********&period2=**********&interval=1d}
	at com.investment.provider.YahooFinanceScraper.scrapeHistoricalData(YahooFinanceScraper.java:150)
	at com.investment.provider.YahooFinanceProvider.downloadHistoricalData(YahooFinanceProvider.java:32)
	at com.investment.MarketDataDownloaderApp.downloadDataForInstrument(MarketDataDownloaderApp.java:145)
	at com.investment.MarketDataDownloaderApp.downloadMarketDataForInstruments(MarketDataDownloaderApp.java:73)
	at com.investment.MarketDataDownloaderApp.main(MarketDataDownloaderApp.java:44)
2025-05-30 00:45:14 [main] WARN  c.investment.MarketDataDownloaderApp - Attempt 1/4 failed for ADX: Failed to download historical data. Retrying in 1000ms...
2025-05-30 00:45:15 [main] INFO  c.investment.MarketDataDownloaderApp - Downloading data for ADX from 1963-05-30 to 2025-05-30 (attempt 2/4)
2025-05-30 00:45:15 [main] INFO  c.i.provider.YahooFinanceProvider - Requesting data from Yahoo Finance for symbol: ADX, from 1963-05-30 to 2025-05-30
2025-05-30 00:45:15 [main] INFO  c.i.provider.YahooFinanceScraper - Scraping data from Yahoo Finance: https://finance.yahoo.com/quote/ADX/history/?period1=-*********&period2=**********&interval=1d
2025-05-30 00:45:20 [main] INFO  c.i.provider.YahooFinanceScraper - Saved HTML content to file: C:\Users\<USER>\Downloads\yahoo-finance-html\yahoo_finance_ADX_1748537120.html
2025-05-30 00:45:20 [main] INFO  c.i.provider.YahooFinanceScraper - Read HTML content from file: C:\Users\<USER>\Downloads\yahoo-finance-html\yahoo_finance_ADX_1748537120.html
2025-05-30 00:45:20 [main] INFO  c.i.provider.YahooFinanceScraper - Extracted 11394 OHLCV data points for ADX
2025-05-30 00:45:23 [main] INFO  c.i.database.DatabaseManager - Saved 11394 OHLCV data points for ADX
2025-05-30 00:45:23 [main] INFO  c.i.provider.YahooFinanceProvider - Downloaded and saved 11394 data points for ADX
2025-05-30 00:45:23 [main] INFO  c.investment.MarketDataDownloaderApp - ✓ Successfully processed ADX
2025-05-30 00:45:24 [main] INFO  c.investment.MarketDataDownloaderApp - Processing instrument 164/10009: ADXN (Addex Therapeutics Ltd.)
2025-05-30 00:45:24 [main] INFO  c.investment.MarketDataDownloaderApp - Downloading data for ADXN from 1963-05-30 to 2025-05-30 (attempt 1/4)
2025-05-30 00:45:24 [main] INFO  c.i.provider.YahooFinanceProvider - Requesting data from Yahoo Finance for symbol: ADXN, from 1963-05-30 to 2025-05-30
2025-05-30 00:45:24 [main] INFO  c.i.provider.YahooFinanceScraper - Scraping data from Yahoo Finance: https://finance.yahoo.com/quote/ADXN/history/?period1=-*********&period2=**********&interval=1d
2025-05-30 00:45:26 [main] INFO  c.i.provider.YahooFinanceScraper - Saved HTML content to file: C:\Users\<USER>\Downloads\yahoo-finance-html\yahoo_finance_ADXN_1748537126.html
2025-05-30 00:45:26 [main] INFO  c.i.provider.YahooFinanceScraper - Read HTML content from file: C:\Users\<USER>\Downloads\yahoo-finance-html\yahoo_finance_ADXN_1748537126.html
2025-05-30 00:45:26 [main] INFO  c.i.provider.YahooFinanceScraper - Extracted 1192 OHLCV data points for ADXN
2025-05-30 00:45:26 [main] INFO  c.i.database.DatabaseManager - Saved 1192 OHLCV data points for ADXN
2025-05-30 00:45:26 [main] INFO  c.i.provider.YahooFinanceProvider - Downloaded and saved 1192 data points for ADXN
2025-05-30 00:45:26 [main] INFO  c.investment.MarketDataDownloaderApp - ✓ Successfully processed ADXN
2025-05-30 00:45:27 [main] INFO  c.investment.MarketDataDownloaderApp - Processing instrument 165/10009: ADYYF (Adyen N.V./ADR)
2025-05-30 00:45:27 [main] INFO  c.investment.MarketDataDownloaderApp - Downloading data for ADYYF from 1963-05-30 to 2025-05-30 (attempt 1/4)
2025-05-30 00:45:27 [main] INFO  c.i.provider.YahooFinanceProvider - Requesting data from Yahoo Finance for symbol: ADYYF, from 1963-05-30 to 2025-05-30
2025-05-30 00:45:27 [main] INFO  c.i.provider.YahooFinanceScraper - Scraping data from Yahoo Finance: https://finance.yahoo.com/quote/ADYYF/history/?period1=-*********&period2=**********&interval=1d
2025-05-30 00:45:29 [main] INFO  c.i.provider.YahooFinanceScraper - Saved HTML content to file: C:\Users\<USER>\Downloads\yahoo-finance-html\yahoo_finance_ADYYF_1748537129.html
2025-05-30 00:45:29 [main] INFO  c.i.provider.YahooFinanceScraper - Read HTML content from file: C:\Users\<USER>\Downloads\yahoo-finance-html\yahoo_finance_ADYYF_1748537129.html
2025-05-30 00:45:29 [main] INFO  c.i.provider.YahooFinanceScraper - Extracted 1669 OHLCV data points for ADYYF
2025-05-30 00:45:30 [main] INFO  c.i.database.DatabaseManager - Saved 1669 OHLCV data points for ADYYF
2025-05-30 00:45:30 [main] INFO  c.i.provider.YahooFinanceProvider - Downloaded and saved 1669 data points for ADYYF
2025-05-30 00:45:30 [main] INFO  c.investment.MarketDataDownloaderApp - ✓ Successfully processed ADYYF
2025-05-30 00:45:30 [main] INFO  c.investment.MarketDataDownloaderApp - Processing instrument 166/10009: ADZCF (DEUTSCHE BANK AKTIENGESELLSCHAFT)
2025-05-30 00:45:30 [main] INFO  c.investment.MarketDataDownloaderApp - Downloading data for ADZCF from 1963-05-30 to 2025-05-30 (attempt 1/4)
2025-05-30 00:45:30 [main] INFO  c.i.provider.YahooFinanceProvider - Requesting data from Yahoo Finance for symbol: ADZCF, from 1963-05-30 to 2025-05-30
2025-05-30 00:45:30 [main] INFO  c.i.provider.YahooFinanceScraper - Scraping data from Yahoo Finance: https://finance.yahoo.com/quote/ADZCF/history/?period1=-*********&period2=**********&interval=1d
2025-05-30 00:45:32 [main] INFO  c.i.provider.YahooFinanceScraper - Saved HTML content to file: C:\Users\<USER>\Downloads\yahoo-finance-html\yahoo_finance_ADZCF_1748537132.html
2025-05-30 00:45:32 [main] INFO  c.i.provider.YahooFinanceScraper - Read HTML content from file: C:\Users\<USER>\Downloads\yahoo-finance-html\yahoo_finance_ADZCF_1748537132.html
2025-05-30 00:45:32 [main] INFO  c.i.provider.YahooFinanceScraper - Extracted 1 OHLCV data points for ADZCF
2025-05-30 00:45:32 [main] INFO  c.i.database.DatabaseManager - Saved 1 OHLCV data points for ADZCF
2025-05-30 00:45:32 [main] INFO  c.i.provider.YahooFinanceProvider - Downloaded and saved 1 data points for ADZCF
2025-05-30 00:45:32 [main] INFO  c.investment.MarketDataDownloaderApp - ✓ Successfully processed ADZCF
2025-05-30 00:45:32 [main] INFO  c.investment.MarketDataDownloaderApp - Processing instrument 167/10009: AEAE (AltEnergy Acquisition Corp)
2025-05-30 00:45:32 [main] INFO  c.investment.MarketDataDownloaderApp - Downloading data for AEAE from 1963-05-30 to 2025-05-30 (attempt 1/4)
2025-05-30 00:45:32 [main] INFO  c.i.provider.YahooFinanceProvider - Requesting data from Yahoo Finance for symbol: AEAE, from 1963-05-30 to 2025-05-30
2025-05-30 00:45:32 [main] INFO  c.i.provider.YahooFinanceScraper - Scraping data from Yahoo Finance: https://finance.yahoo.com/quote/AEAE/history/?period1=-*********&period2=**********&interval=1d
2025-05-30 00:45:34 [main] INFO  c.i.provider.YahooFinanceScraper - Saved HTML content to file: C:\Users\<USER>\Downloads\yahoo-finance-html\yahoo_finance_AEAE_1748537134.html
2025-05-30 00:45:34 [main] INFO  c.i.provider.YahooFinanceScraper - Read HTML content from file: C:\Users\<USER>\Downloads\yahoo-finance-html\yahoo_finance_AEAE_1748537134.html
2025-05-30 00:45:34 [main] INFO  c.i.provider.YahooFinanceScraper - Extracted 465 OHLCV data points for AEAE
2025-05-30 00:45:34 [main] INFO  c.i.database.DatabaseManager - Saved 465 OHLCV data points for AEAE
2025-05-30 00:45:34 [main] INFO  c.i.provider.YahooFinanceProvider - Downloaded and saved 465 data points for AEAE
2025-05-30 00:45:34 [main] INFO  c.investment.MarketDataDownloaderApp - ✓ Successfully processed AEAE
2025-05-30 00:45:35 [main] INFO  c.investment.MarketDataDownloaderApp - Processing instrument 168/10009: AEAEU (AltEnergy Acquisition Corp)
2025-05-30 00:45:35 [main] INFO  c.investment.MarketDataDownloaderApp - Downloading data for AEAEU from 1963-05-30 to 2025-05-30 (attempt 1/4)
2025-05-30 00:45:35 [main] INFO  c.i.provider.YahooFinanceProvider - Requesting data from Yahoo Finance for symbol: AEAEU, from 1963-05-30 to 2025-05-30
2025-05-30 00:45:35 [main] INFO  c.i.provider.YahooFinanceScraper - Scraping data from Yahoo Finance: https://finance.yahoo.com/quote/AEAEU/history/?period1=-*********&period2=**********&interval=1d
2025-05-30 00:45:36 [main] INFO  c.i.provider.YahooFinanceScraper - Saved HTML content to file: C:\Users\<USER>\Downloads\yahoo-finance-html\yahoo_finance_AEAEU_1748537136.html
2025-05-30 00:45:36 [main] INFO  c.i.provider.YahooFinanceScraper - Read HTML content from file: C:\Users\<USER>\Downloads\yahoo-finance-html\yahoo_finance_AEAEU_1748537136.html
2025-05-30 00:45:36 [main] INFO  c.i.provider.YahooFinanceScraper - Extracted 208 OHLCV data points for AEAEU
2025-05-30 00:45:36 [main] INFO  c.i.database.DatabaseManager - Saved 208 OHLCV data points for AEAEU
2025-05-30 00:45:36 [main] INFO  c.i.provider.YahooFinanceProvider - Downloaded and saved 208 data points for AEAEU
2025-05-30 00:45:36 [main] INFO  c.investment.MarketDataDownloaderApp - ✓ Successfully processed AEAEU
2025-05-30 00:45:37 [main] INFO  c.investment.MarketDataDownloaderApp - Processing instrument 169/10009: AEAEW (AltEnergy Acquisition Corp)
2025-05-30 00:45:37 [main] INFO  c.investment.MarketDataDownloaderApp - Downloading data for AEAEW from 1963-05-30 to 2025-05-30 (attempt 1/4)
2025-05-30 00:45:37 [main] INFO  c.i.provider.YahooFinanceProvider - Requesting data from Yahoo Finance for symbol: AEAEW, from 1963-05-30 to 2025-05-30
2025-05-30 00:45:37 [main] INFO  c.i.provider.YahooFinanceScraper - Scraping data from Yahoo Finance: https://finance.yahoo.com/quote/AEAEW/history/?period1=-*********&period2=**********&interval=1d
2025-05-30 00:45:38 [main] INFO  c.i.provider.YahooFinanceScraper - Saved HTML content to file: C:\Users\<USER>\Downloads\yahoo-finance-html\yahoo_finance_AEAEW_1748537138.html
2025-05-30 00:45:38 [main] INFO  c.i.provider.YahooFinanceScraper - Read HTML content from file: C:\Users\<USER>\Downloads\yahoo-finance-html\yahoo_finance_AEAEW_1748537138.html
2025-05-30 00:45:38 [main] WARN  c.i.provider.YahooFinanceScraper - Could not find historical prices rows for AEAEW
2025-05-30 00:45:38 [main] INFO  c.i.provider.YahooFinanceProvider - Downloaded and saved 0 data points for AEAEW
2025-05-30 00:45:38 [main] INFO  c.investment.MarketDataDownloaderApp - ✓ Successfully processed AEAEW
2025-05-30 00:45:38 [main] INFO  c.investment.MarketDataDownloaderApp - Processing instrument 170/10009: AEBI (Aebi Schmidt Holding AG)
2025-05-30 00:45:38 [main] INFO  c.investment.MarketDataDownloaderApp - Downloading data for AEBI from 1963-05-30 to 2025-05-30 (attempt 1/4)
2025-05-30 00:45:38 [main] INFO  c.i.provider.YahooFinanceProvider - Requesting data from Yahoo Finance for symbol: AEBI, from 1963-05-30 to 2025-05-30
2025-05-30 00:45:38 [main] INFO  c.i.provider.YahooFinanceScraper - Scraping data from Yahoo Finance: https://finance.yahoo.com/quote/AEBI/history/?period1=-*********&period2=**********&interval=1d
2025-05-30 00:45:39 [main] INFO  c.i.provider.YahooFinanceScraper - Saved HTML content to file: C:\Users\<USER>\Downloads\yahoo-finance-html\yahoo_finance_AEBI_1748537139.html
2025-05-30 00:45:39 [main] INFO  c.i.provider.YahooFinanceScraper - Read HTML content from file: C:\Users\<USER>\Downloads\yahoo-finance-html\yahoo_finance_AEBI_1748537139.html
2025-05-30 00:45:39 [main] WARN  c.i.provider.YahooFinanceScraper - Could not find historical prices rows for AEBI
2025-05-30 00:45:39 [main] INFO  c.i.provider.YahooFinanceProvider - Downloaded and saved 0 data points for AEBI
2025-05-30 00:45:39 [main] INFO  c.investment.MarketDataDownloaderApp - ✓ Successfully processed AEBI
2025-05-30 00:45:39 [main] INFO  c.investment.MarketDataDownloaderApp - Processing instrument 171/10009: AEE (AMEREN CORP)
2025-05-30 00:45:39 [main] INFO  c.investment.MarketDataDownloaderApp - Downloading data for AEE from 1963-05-30 to 2025-05-30 (attempt 1/4)
2025-05-30 00:45:39 [main] INFO  c.i.provider.YahooFinanceProvider - Requesting data from Yahoo Finance for symbol: AEE, from 1963-05-30 to 2025-05-30
2025-05-30 00:45:39 [main] INFO  c.i.provider.YahooFinanceScraper - Scraping data from Yahoo Finance: https://finance.yahoo.com/quote/AEE/history/?period1=-*********&period2=**********&interval=1d
2025-05-30 00:45:43 [main] INFO  c.i.provider.YahooFinanceScraper - Saved HTML content to file: C:\Users\<USER>\Downloads\yahoo-finance-html\yahoo_finance_AEE_1748537143.html
2025-05-30 00:45:44 [main] INFO  c.i.provider.YahooFinanceScraper - Read HTML content from file: C:\Users\<USER>\Downloads\yahoo-finance-html\yahoo_finance_AEE_1748537143.html
2025-05-30 00:45:44 [main] INFO  c.i.provider.YahooFinanceScraper - Extracted 6894 OHLCV data points for AEE
2025-05-30 00:45:45 [main] INFO  c.i.database.DatabaseManager - Saved 6894 OHLCV data points for AEE
2025-05-30 00:45:45 [main] INFO  c.i.provider.YahooFinanceProvider - Downloaded and saved 6894 data points for AEE
2025-05-30 00:45:45 [main] INFO  c.investment.MarketDataDownloaderApp - ✓ Successfully processed AEE
2025-05-30 00:45:46 [main] INFO  c.investment.MarketDataDownloaderApp - Processing instrument 172/10009: AEF (abrdn Emerging Markets ex-China Fund, Inc.)
2025-05-30 00:45:46 [main] INFO  c.investment.MarketDataDownloaderApp - Downloading data for AEF from 1963-05-30 to 2025-05-30 (attempt 1/4)
2025-05-30 00:45:46 [main] INFO  c.i.provider.YahooFinanceProvider - Requesting data from Yahoo Finance for symbol: AEF, from 1963-05-30 to 2025-05-30
2025-05-30 00:45:46 [main] INFO  c.i.provider.YahooFinanceScraper - Scraping data from Yahoo Finance: https://finance.yahoo.com/quote/AEF/history/?period1=-*********&period2=**********&interval=1d
2025-05-30 00:45:50 [main] INFO  c.i.provider.YahooFinanceScraper - Saved HTML content to file: C:\Users\<USER>\Downloads\yahoo-finance-html\yahoo_finance_AEF_1748537150.html
2025-05-30 00:45:50 [main] INFO  c.i.provider.YahooFinanceScraper - Read HTML content from file: C:\Users\<USER>\Downloads\yahoo-finance-html\yahoo_finance_AEF_1748537150.html
2025-05-30 00:45:50 [main] INFO  c.i.provider.YahooFinanceScraper - Extracted 8978 OHLCV data points for AEF
2025-05-30 00:45:52 [main] INFO  c.i.database.DatabaseManager - Saved 8978 OHLCV data points for AEF
2025-05-30 00:45:52 [main] INFO  c.i.provider.YahooFinanceProvider - Downloaded and saved 8978 data points for AEF
2025-05-30 00:45:52 [main] INFO  c.investment.MarketDataDownloaderApp - ✓ Successfully processed AEF
2025-05-30 00:45:53 [main] INFO  c.investment.MarketDataDownloaderApp - Processing instrument 173/10009: AEFC (AEGON LTD.)
2025-05-30 00:45:53 [main] INFO  c.investment.MarketDataDownloaderApp - Downloading data for AEFC from 1963-05-30 to 2025-05-30 (attempt 1/4)
2025-05-30 00:45:53 [main] INFO  c.i.provider.YahooFinanceProvider - Requesting data from Yahoo Finance for symbol: AEFC, from 1963-05-30 to 2025-05-30
2025-05-30 00:45:53 [main] INFO  c.i.provider.YahooFinanceScraper - Scraping data from Yahoo Finance: https://finance.yahoo.com/quote/AEFC/history/?period1=-*********&period2=**********&interval=1d
2025-05-30 00:45:55 [main] INFO  c.i.provider.YahooFinanceScraper - Saved HTML content to file: C:\Users\<USER>\Downloads\yahoo-finance-html\yahoo_finance_AEFC_1748537155.html
2025-05-30 00:45:55 [main] INFO  c.i.provider.YahooFinanceScraper - Read HTML content from file: C:\Users\<USER>\Downloads\yahoo-finance-html\yahoo_finance_AEFC_1748537155.html
2025-05-30 00:45:55 [main] INFO  c.i.provider.YahooFinanceScraper - Extracted 1406 OHLCV data points for AEFC
2025-05-30 00:45:55 [main] INFO  c.i.database.DatabaseManager - Saved 1406 OHLCV data points for AEFC
2025-05-30 00:45:55 [main] INFO  c.i.provider.YahooFinanceProvider - Downloaded and saved 1406 data points for AEFC
2025-05-30 00:45:55 [main] INFO  c.investment.MarketDataDownloaderApp - ✓ Successfully processed AEFC
2025-05-30 00:45:56 [main] INFO  c.investment.MarketDataDownloaderApp - Processing instrument 174/10009: AEG (AEGON LTD.)
2025-05-30 00:45:56 [main] INFO  c.investment.MarketDataDownloaderApp - Downloading data for AEG from 1963-05-30 to 2025-05-30 (attempt 1/4)
2025-05-30 00:45:56 [main] INFO  c.i.provider.YahooFinanceProvider - Requesting data from Yahoo Finance for symbol: AEG, from 1963-05-30 to 2025-05-30
2025-05-30 00:45:56 [main] INFO  c.i.provider.YahooFinanceScraper - Scraping data from Yahoo Finance: https://finance.yahoo.com/quote/AEG/history/?period1=-*********&period2=**********&interval=1d
2025-05-30 00:46:04 [main] INFO  c.i.provider.YahooFinanceScraper - Saved HTML content to file: C:\Users\<USER>\Downloads\yahoo-finance-html\yahoo_finance_AEG_1748537164.html
2025-05-30 00:46:04 [main] INFO  c.i.provider.YahooFinanceScraper - Read HTML content from file: C:\Users\<USER>\Downloads\yahoo-finance-html\yahoo_finance_AEG_1748537164.html
2025-05-30 00:46:04 [main] INFO  c.i.provider.YahooFinanceScraper - Extracted 9754 OHLCV data points for AEG
2025-05-30 00:46:06 [main] INFO  c.i.database.DatabaseManager - Saved 9754 OHLCV data points for AEG
2025-05-30 00:46:06 [main] INFO  c.i.provider.YahooFinanceProvider - Downloaded and saved 9754 data points for AEG
2025-05-30 00:46:06 [main] INFO  c.investment.MarketDataDownloaderApp - ✓ Successfully processed AEG
2025-05-30 00:46:07 [main] INFO  c.investment.MarketDataDownloaderApp - Processing instrument 175/10009: AEGOF (AEGON LTD.)
2025-05-30 00:46:07 [main] INFO  c.investment.MarketDataDownloaderApp - Downloading data for AEGOF from 1963-05-30 to 2025-05-30 (attempt 1/4)
2025-05-30 00:46:07 [main] INFO  c.i.provider.YahooFinanceProvider - Requesting data from Yahoo Finance for symbol: AEGOF, from 1963-05-30 to 2025-05-30
2025-05-30 00:46:07 [main] INFO  c.i.provider.YahooFinanceScraper - Scraping data from Yahoo Finance: https://finance.yahoo.com/quote/AEGOF/history/?period1=-*********&period2=**********&interval=1d
2025-05-30 00:46:11 [main] INFO  c.i.provider.YahooFinanceScraper - Saved HTML content to file: C:\Users\<USER>\Downloads\yahoo-finance-html\yahoo_finance_AEGOF_1748537171.html
2025-05-30 00:46:11 [main] INFO  c.i.provider.YahooFinanceScraper - Read HTML content from file: C:\Users\<USER>\Downloads\yahoo-finance-html\yahoo_finance_AEGOF_1748537171.html
2025-05-30 00:46:11 [main] INFO  c.i.provider.YahooFinanceScraper - Extracted 1017 OHLCV data points for AEGOF
2025-05-30 00:46:11 [main] INFO  c.i.database.DatabaseManager - Saved 1017 OHLCV data points for AEGOF
2025-05-30 00:46:11 [main] INFO  c.i.provider.YahooFinanceProvider - Downloaded and saved 1017 data points for AEGOF
2025-05-30 00:46:11 [main] INFO  c.investment.MarketDataDownloaderApp - ✓ Successfully processed AEGOF
2025-05-30 00:46:11 [main] INFO  c.investment.MarketDataDownloaderApp - Processing instrument 176/10009: AEHL (Antelope Enterprise Holdings Ltd)
2025-05-30 00:46:11 [main] INFO  c.investment.MarketDataDownloaderApp - Downloading data for AEHL from 1963-05-30 to 2025-05-30 (attempt 1/4)
2025-05-30 00:46:11 [main] INFO  c.i.provider.YahooFinanceProvider - Requesting data from Yahoo Finance for symbol: AEHL, from 1963-05-30 to 2025-05-30
2025-05-30 00:46:11 [main] INFO  c.i.provider.YahooFinanceScraper - Scraping data from Yahoo Finance: https://finance.yahoo.com/quote/AEHL/history/?period1=-*********&period2=**********&interval=1d
2025-05-30 00:46:16 [main] INFO  c.i.provider.YahooFinanceScraper - Saved HTML content to file: C:\Users\<USER>\Downloads\yahoo-finance-html\yahoo_finance_AEHL_1748537176.html
2025-05-30 00:46:16 [main] INFO  c.i.provider.YahooFinanceScraper - Read HTML content from file: C:\Users\<USER>\Downloads\yahoo-finance-html\yahoo_finance_AEHL_1748537176.html
2025-05-30 00:46:16 [main] INFO  c.i.provider.YahooFinanceScraper - Extracted 3702 OHLCV data points for AEHL
2025-05-30 00:46:17 [main] INFO  c.i.database.DatabaseManager - Saved 3702 OHLCV data points for AEHL
2025-05-30 00:46:17 [main] INFO  c.i.provider.YahooFinanceProvider - Downloaded and saved 3702 data points for AEHL
2025-05-30 00:46:17 [main] INFO  c.investment.MarketDataDownloaderApp - ✓ Successfully processed AEHL
2025-05-30 00:46:17 [main] INFO  c.investment.MarketDataDownloaderApp - Processing instrument 177/10009: AEHR (AEHR TEST SYSTEMS)
2025-05-30 00:46:17 [main] INFO  c.investment.MarketDataDownloaderApp - Downloading data for AEHR from 1963-05-30 to 2025-05-30 (attempt 1/4)
2025-05-30 00:46:17 [main] INFO  c.i.provider.YahooFinanceProvider - Requesting data from Yahoo Finance for symbol: AEHR, from 1963-05-30 to 2025-05-30
2025-05-30 00:46:17 [main] INFO  c.i.provider.YahooFinanceScraper - Scraping data from Yahoo Finance: https://finance.yahoo.com/quote/AEHR/history/?period1=-*********&period2=**********&interval=1d
2025-05-30 00:46:21 [main] INFO  c.i.provider.YahooFinanceScraper - Saved HTML content to file: C:\Users\<USER>\Downloads\yahoo-finance-html\yahoo_finance_AEHR_1748537181.html
2025-05-30 00:46:21 [main] INFO  c.i.provider.YahooFinanceScraper - Read HTML content from file: C:\Users\<USER>\Downloads\yahoo-finance-html\yahoo_finance_AEHR_1748537181.html
2025-05-30 00:46:21 [main] INFO  c.i.provider.YahooFinanceScraper - Extracted 6701 OHLCV data points for AEHR
2025-05-30 00:46:23 [main] INFO  c.i.database.DatabaseManager - Saved 6701 OHLCV data points for AEHR
2025-05-30 00:46:23 [main] INFO  c.i.provider.YahooFinanceProvider - Downloaded and saved 6701 data points for AEHR
2025-05-30 00:46:23 [main] INFO  c.investment.MarketDataDownloaderApp - ✓ Successfully processed AEHR
2025-05-30 00:46:23 [main] INFO  c.investment.MarketDataDownloaderApp - Processing instrument 178/10009: AEI (Alset Inc.)
2025-05-30 00:46:23 [main] INFO  c.investment.MarketDataDownloaderApp - Downloading data for AEI from 1963-05-30 to 2025-05-30 (attempt 1/4)
2025-05-30 00:46:23 [main] INFO  c.i.provider.YahooFinanceProvider - Requesting data from Yahoo Finance for symbol: AEI, from 1963-05-30 to 2025-05-30
2025-05-30 00:46:23 [main] INFO  c.i.provider.YahooFinanceScraper - Scraping data from Yahoo Finance: https://finance.yahoo.com/quote/AEI/history/?period1=-*********&period2=**********&interval=1d
2025-05-30 00:46:26 [main] INFO  c.i.provider.YahooFinanceScraper - Saved HTML content to file: C:\Users\<USER>\Downloads\yahoo-finance-html\yahoo_finance_AEI_1748537186.html
2025-05-30 00:46:26 [main] INFO  c.i.provider.YahooFinanceScraper - Read HTML content from file: C:\Users\<USER>\Downloads\yahoo-finance-html\yahoo_finance_AEI_1748537186.html
2025-05-30 00:46:26 [main] INFO  c.i.provider.YahooFinanceScraper - Extracted 1131 OHLCV data points for AEI
2025-05-30 00:46:26 [main] INFO  c.i.database.DatabaseManager - Saved 1131 OHLCV data points for AEI
2025-05-30 00:46:26 [main] INFO  c.i.provider.YahooFinanceProvider - Downloaded and saved 1131 data points for AEI
2025-05-30 00:46:26 [main] INFO  c.investment.MarketDataDownloaderApp - ✓ Successfully processed AEI
2025-05-30 00:46:26 [main] INFO  c.investment.MarketDataDownloaderApp - Processing instrument 179/10009: AEIS (ADVANCED ENERGY INDUSTRIES INC)
2025-05-30 00:46:26 [main] INFO  c.investment.MarketDataDownloaderApp - Downloading data for AEIS from 1963-05-30 to 2025-05-30 (attempt 1/4)
2025-05-30 00:46:26 [main] INFO  c.i.provider.YahooFinanceProvider - Requesting data from Yahoo Finance for symbol: AEIS, from 1963-05-30 to 2025-05-30
2025-05-30 00:46:26 [main] INFO  c.i.provider.YahooFinanceScraper - Scraping data from Yahoo Finance: https://finance.yahoo.com/quote/AEIS/history/?period1=-*********&period2=**********&interval=1d
2025-05-30 00:46:30 [main] INFO  c.i.provider.YahooFinanceScraper - Saved HTML content to file: C:\Users\<USER>\Downloads\yahoo-finance-html\yahoo_finance_AEIS_1748537190.html
2025-05-30 00:46:30 [main] INFO  c.i.provider.YahooFinanceScraper - Read HTML content from file: C:\Users\<USER>\Downloads\yahoo-finance-html\yahoo_finance_AEIS_1748537190.html
2025-05-30 00:46:30 [main] INFO  c.i.provider.YahooFinanceScraper - Extracted 7430 OHLCV data points for AEIS
2025-05-30 00:46:32 [main] INFO  c.i.database.DatabaseManager - Saved 7430 OHLCV data points for AEIS
2025-05-30 00:46:32 [main] INFO  c.i.provider.YahooFinanceProvider - Downloaded and saved 7430 data points for AEIS
2025-05-30 00:46:32 [main] INFO  c.investment.MarketDataDownloaderApp - ✓ Successfully processed AEIS
2025-05-30 00:46:33 [main] INFO  c.investment.MarketDataDownloaderApp - Processing instrument 180/10009: AEM (AGNICO EAGLE MINES LTD)
2025-05-30 00:46:33 [main] INFO  c.investment.MarketDataDownloaderApp - Downloading data for AEM from 1963-05-30 to 2025-05-30 (attempt 1/4)
2025-05-30 00:46:33 [main] INFO  c.i.provider.YahooFinanceProvider - Requesting data from Yahoo Finance for symbol: AEM, from 1963-05-30 to 2025-05-30
2025-05-30 00:46:33 [main] INFO  c.i.provider.YahooFinanceScraper - Scraping data from Yahoo Finance: https://finance.yahoo.com/quote/AEM/history/?period1=-*********&period2=**********&interval=1d
2025-05-30 00:46:40 [main] INFO  c.i.provider.YahooFinanceScraper - Saved HTML content to file: C:\Users\<USER>\Downloads\yahoo-finance-html\yahoo_finance_AEM_1748537200.html
2025-05-30 00:46:40 [main] INFO  c.i.provider.YahooFinanceScraper - Read HTML content from file: C:\Users\<USER>\Downloads\yahoo-finance-html\yahoo_finance_AEM_1748537200.html
2025-05-30 00:46:40 [main] INFO  c.i.provider.YahooFinanceScraper - Extracted 13171 OHLCV data points for AEM
2025-05-30 00:46:43 [main] INFO  c.i.database.DatabaseManager - Saved 13171 OHLCV data points for AEM
2025-05-30 00:46:43 [main] INFO  c.i.provider.YahooFinanceProvider - Downloaded and saved 13171 data points for AEM
2025-05-30 00:46:43 [main] INFO  c.investment.MarketDataDownloaderApp - ✓ Successfully processed AEM
2025-05-30 00:46:44 [main] INFO  c.investment.MarketDataDownloaderApp - Processing instrument 181/10009: AEMD (AETHLON MEDICAL INC)
2025-05-30 00:46:44 [main] INFO  c.investment.MarketDataDownloaderApp - Downloading data for AEMD from 1963-05-30 to 2025-05-30 (attempt 1/4)
2025-05-30 00:46:44 [main] INFO  c.i.provider.YahooFinanceProvider - Requesting data from Yahoo Finance for symbol: AEMD, from 1963-05-30 to 2025-05-30
2025-05-30 00:46:44 [main] INFO  c.i.provider.YahooFinanceScraper - Scraping data from Yahoo Finance: https://finance.yahoo.com/quote/AEMD/history/?period1=-*********&period2=**********&interval=1d
2025-05-30 00:46:48 [main] INFO  c.i.provider.YahooFinanceScraper - Saved HTML content to file: C:\Users\<USER>\Downloads\yahoo-finance-html\yahoo_finance_AEMD_1748537208.html
2025-05-30 00:46:48 [main] INFO  c.i.provider.YahooFinanceScraper - Read HTML content from file: C:\Users\<USER>\Downloads\yahoo-finance-html\yahoo_finance_AEMD_1748537208.html
2025-05-30 00:46:48 [main] INFO  c.i.provider.YahooFinanceScraper - Extracted 5429 OHLCV data points for AEMD
2025-05-30 00:46:49 [main] INFO  c.i.database.DatabaseManager - Saved 5429 OHLCV data points for AEMD
2025-05-30 00:46:49 [main] INFO  c.i.provider.YahooFinanceProvider - Downloaded and saved 5429 data points for AEMD
2025-05-30 00:46:49 [main] INFO  c.investment.MarketDataDownloaderApp - ✓ Successfully processed AEMD
2025-05-30 00:46:49 [main] INFO  c.investment.MarketDataDownloaderApp - Processing instrument 182/10009: AENT (ALLIANCE ENTERTAINMENT HOLDING CORP)
2025-05-30 00:46:49 [main] INFO  c.investment.MarketDataDownloaderApp - Downloading data for AENT from 1963-05-30 to 2025-05-30 (attempt 1/4)
2025-05-30 00:46:49 [main] INFO  c.i.provider.YahooFinanceProvider - Requesting data from Yahoo Finance for symbol: AENT, from 1963-05-30 to 2025-05-30
2025-05-30 00:46:49 [main] INFO  c.i.provider.YahooFinanceScraper - Scraping data from Yahoo Finance: https://finance.yahoo.com/quote/AENT/history/?period1=-*********&period2=**********&interval=1d
2025-05-30 00:46:52 [main] INFO  c.i.provider.YahooFinanceScraper - Saved HTML content to file: C:\Users\<USER>\Downloads\yahoo-finance-html\yahoo_finance_AENT_1748537212.html
2025-05-30 00:46:52 [main] INFO  c.i.provider.YahooFinanceScraper - Read HTML content from file: C:\Users\<USER>\Downloads\yahoo-finance-html\yahoo_finance_AENT_1748537212.html
2025-05-30 00:46:52 [main] INFO  c.i.provider.YahooFinanceScraper - Extracted 959 OHLCV data points for AENT
2025-05-30 00:46:52 [main] INFO  c.i.database.DatabaseManager - Saved 959 OHLCV data points for AENT
2025-05-30 00:46:52 [main] INFO  c.i.provider.YahooFinanceProvider - Downloaded and saved 959 data points for AENT
2025-05-30 00:46:52 [main] INFO  c.investment.MarketDataDownloaderApp - ✓ Successfully processed AENT
2025-05-30 00:46:52 [main] INFO  c.investment.MarketDataDownloaderApp - Processing instrument 183/10009: AENTW (ALLIANCE ENTERTAINMENT HOLDING CORP)
2025-05-30 00:46:52 [main] INFO  c.investment.MarketDataDownloaderApp - Downloading data for AENTW from 1963-05-30 to 2025-05-30 (attempt 1/4)
2025-05-30 00:46:52 [main] INFO  c.i.provider.YahooFinanceProvider - Requesting data from Yahoo Finance for symbol: AENTW, from 1963-05-30 to 2025-05-30
2025-05-30 00:46:52 [main] INFO  c.i.provider.YahooFinanceScraper - Scraping data from Yahoo Finance: https://finance.yahoo.com/quote/AENTW/history/?period1=-*********&period2=**********&interval=1d
2025-05-30 00:46:54 [main] INFO  c.i.provider.YahooFinanceScraper - Saved HTML content to file: C:\Users\<USER>\Downloads\yahoo-finance-html\yahoo_finance_AENTW_1748537214.html
2025-05-30 00:46:54 [main] INFO  c.i.provider.YahooFinanceScraper - Read HTML content from file: C:\Users\<USER>\Downloads\yahoo-finance-html\yahoo_finance_AENTW_1748537214.html
2025-05-30 00:46:54 [main] INFO  c.i.provider.YahooFinanceScraper - Extracted 1 OHLCV data points for AENTW
2025-05-30 00:46:54 [main] INFO  c.i.database.DatabaseManager - Saved 1 OHLCV data points for AENTW
2025-05-30 00:46:54 [main] INFO  c.i.provider.YahooFinanceProvider - Downloaded and saved 1 data points for AENTW
2025-05-30 00:46:54 [main] INFO  c.investment.MarketDataDownloaderApp - ✓ Successfully processed AENTW
2025-05-30 00:46:55 [main] INFO  c.investment.MarketDataDownloaderApp - Processing instrument 184/10009: AEO (AMERICAN EAGLE OUTFITTERS INC)
2025-05-30 00:46:55 [main] INFO  c.investment.MarketDataDownloaderApp - Downloading data for AEO from 1963-05-30 to 2025-05-30 (attempt 1/4)
2025-05-30 00:46:55 [main] INFO  c.i.provider.YahooFinanceProvider - Requesting data from Yahoo Finance for symbol: AEO, from 1963-05-30 to 2025-05-30
2025-05-30 00:46:55 [main] INFO  c.i.provider.YahooFinanceScraper - Scraping data from Yahoo Finance: https://finance.yahoo.com/quote/AEO/history/?period1=-*********&period2=**********&interval=1d
2025-05-30 00:46:58 [main] INFO  c.i.provider.YahooFinanceScraper - Saved HTML content to file: C:\Users\<USER>\Downloads\yahoo-finance-html\yahoo_finance_AEO_1748537218.html
2025-05-30 00:46:58 [main] INFO  c.i.provider.YahooFinanceScraper - Read HTML content from file: C:\Users\<USER>\Downloads\yahoo-finance-html\yahoo_finance_AEO_1748537218.html
2025-05-30 00:46:58 [main] INFO  c.i.provider.YahooFinanceScraper - Extracted 7834 OHLCV data points for AEO
2025-05-30 00:47:00 [main] INFO  c.i.database.DatabaseManager - Saved 7834 OHLCV data points for AEO
2025-05-30 00:47:00 [main] INFO  c.i.provider.YahooFinanceProvider - Downloaded and saved 7834 data points for AEO
2025-05-30 00:47:00 [main] INFO  c.investment.MarketDataDownloaderApp - ✓ Successfully processed AEO
2025-05-30 00:47:01 [main] INFO  c.investment.MarketDataDownloaderApp - Processing instrument 185/10009: AEON (AEON Biopharma, Inc.)
2025-05-30 00:47:01 [main] INFO  c.investment.MarketDataDownloaderApp - Downloading data for AEON from 1963-05-30 to 2025-05-30 (attempt 1/4)
2025-05-30 00:47:01 [main] INFO  c.i.provider.YahooFinanceProvider - Requesting data from Yahoo Finance for symbol: AEON, from 1963-05-30 to 2025-05-30
2025-05-30 00:47:01 [main] INFO  c.i.provider.YahooFinanceScraper - Scraping data from Yahoo Finance: https://finance.yahoo.com/quote/AEON/history/?period1=-*********&period2=**********&interval=1d
2025-05-30 00:47:03 [main] INFO  c.i.provider.YahooFinanceScraper - Saved HTML content to file: C:\Users\<USER>\Downloads\yahoo-finance-html\yahoo_finance_AEON_1748537223.html
2025-05-30 00:47:03 [main] INFO  c.i.provider.YahooFinanceScraper - Read HTML content from file: C:\Users\<USER>\Downloads\yahoo-finance-html\yahoo_finance_AEON_1748537223.html
2025-05-30 00:47:03 [main] INFO  c.i.provider.YahooFinanceScraper - Extracted 573 OHLCV data points for AEON
2025-05-30 00:47:03 [main] INFO  c.i.database.DatabaseManager - Saved 573 OHLCV data points for AEON
2025-05-30 00:47:03 [main] INFO  c.i.provider.YahooFinanceProvider - Downloaded and saved 573 data points for AEON
2025-05-30 00:47:03 [main] INFO  c.investment.MarketDataDownloaderApp - ✓ Successfully processed AEON
2025-05-30 00:47:03 [main] INFO  c.investment.MarketDataDownloaderApp - Processing instrument 186/10009: AEP (AMERICAN ELECTRIC POWER CO INC)
2025-05-30 00:47:03 [main] INFO  c.investment.MarketDataDownloaderApp - Downloading data for AEP from 1963-05-30 to 2025-05-30 (attempt 1/4)
2025-05-30 00:47:03 [main] INFO  c.i.provider.YahooFinanceProvider - Requesting data from Yahoo Finance for symbol: AEP, from 1963-05-30 to 2025-05-30
2025-05-30 00:47:03 [main] INFO  c.i.provider.YahooFinanceScraper - Scraping data from Yahoo Finance: https://finance.yahoo.com/quote/AEP/history/?period1=-*********&period2=**********&interval=1d
2025-05-30 00:47:13 [main] INFO  c.i.provider.YahooFinanceScraper - Saved HTML content to file: C:\Users\<USER>\Downloads\yahoo-finance-html\yahoo_finance_AEP_1748537233.html
2025-05-30 00:47:13 [main] INFO  c.i.provider.YahooFinanceScraper - Read HTML content from file: C:\Users\<USER>\Downloads\yahoo-finance-html\yahoo_finance_AEP_1748537233.html
2025-05-30 00:47:13 [main] INFO  c.i.provider.YahooFinanceScraper - Extracted 15603 OHLCV data points for AEP
2025-05-30 00:47:18 [main] INFO  c.i.database.DatabaseManager - Saved 15603 OHLCV data points for AEP
2025-05-30 00:47:18 [main] INFO  c.i.provider.YahooFinanceProvider - Downloaded and saved 15603 data points for AEP
2025-05-30 00:47:18 [main] INFO  c.investment.MarketDataDownloaderApp - ✓ Successfully processed AEP
2025-05-30 00:47:18 [main] INFO  c.investment.MarketDataDownloaderApp - Processing instrument 187/10009: AER (AerCap Holdings N.V.)
2025-05-30 00:47:18 [main] INFO  c.investment.MarketDataDownloaderApp - Downloading data for AER from 1963-05-30 to 2025-05-30 (attempt 1/4)
2025-05-30 00:47:18 [main] INFO  c.i.provider.YahooFinanceProvider - Requesting data from Yahoo Finance for symbol: AER, from 1963-05-30 to 2025-05-30
2025-05-30 00:47:18 [main] INFO  c.i.provider.YahooFinanceScraper - Scraping data from Yahoo Finance: https://finance.yahoo.com/quote/AER/history/?period1=-*********&period2=**********&interval=1d
2025-05-30 00:47:21 [main] INFO  c.i.provider.YahooFinanceScraper - Saved HTML content to file: C:\Users\<USER>\Downloads\yahoo-finance-html\yahoo_finance_AER_1748537241.html
2025-05-30 00:47:21 [main] INFO  c.i.provider.YahooFinanceScraper - Read HTML content from file: C:\Users\<USER>\Downloads\yahoo-finance-html\yahoo_finance_AER_1748537241.html
2025-05-30 00:47:21 [main] INFO  c.i.provider.YahooFinanceScraper - Extracted 4658 OHLCV data points for AER
2025-05-30 00:47:22 [main] INFO  c.i.database.DatabaseManager - Saved 4658 OHLCV data points for AER
2025-05-30 00:47:22 [main] INFO  c.i.provider.YahooFinanceProvider - Downloaded and saved 4658 data points for AER
2025-05-30 00:47:22 [main] INFO  c.investment.MarketDataDownloaderApp - ✓ Successfully processed AER
2025-05-30 00:47:23 [main] INFO  c.investment.MarketDataDownloaderApp - Processing instrument 188/10009: AERG (APPLIED ENERGETICS, INC.)
2025-05-30 00:47:23 [main] INFO  c.investment.MarketDataDownloaderApp - Downloading data for AERG from 1963-05-30 to 2025-05-30 (attempt 1/4)
2025-05-30 00:47:23 [main] INFO  c.i.provider.YahooFinanceProvider - Requesting data from Yahoo Finance for symbol: AERG, from 1963-05-30 to 2025-05-30
2025-05-30 00:47:23 [main] INFO  c.i.provider.YahooFinanceScraper - Scraping data from Yahoo Finance: https://finance.yahoo.com/quote/AERG/history/?period1=-*********&period2=**********&interval=1d
2025-05-30 00:47:25 [main] INFO  c.i.provider.YahooFinanceScraper - Saved HTML content to file: C:\Users\<USER>\Downloads\yahoo-finance-html\yahoo_finance_AERG_1748537245.html
2025-05-30 00:47:25 [main] INFO  c.i.provider.YahooFinanceScraper - Read HTML content from file: C:\Users\<USER>\Downloads\yahoo-finance-html\yahoo_finance_AERG_1748537245.html
2025-05-30 00:47:25 [main] INFO  c.i.provider.YahooFinanceScraper - Extracted 1102 OHLCV data points for AERG
2025-05-30 00:47:25 [main] INFO  c.i.database.DatabaseManager - Saved 1102 OHLCV data points for AERG
2025-05-30 00:47:25 [main] INFO  c.i.provider.YahooFinanceProvider - Downloaded and saved 1102 data points for AERG
2025-05-30 00:47:25 [main] INFO  c.investment.MarketDataDownloaderApp - ✓ Successfully processed AERG
2025-05-30 00:47:26 [main] INFO  c.investment.MarketDataDownloaderApp - Processing instrument 189/10009: AERGP (APPLIED ENERGETICS, INC.)
2025-05-30 00:47:26 [main] INFO  c.investment.MarketDataDownloaderApp - Downloading data for AERGP from 1963-05-30 to 2025-05-30 (attempt 1/4)
2025-05-30 00:47:26 [main] INFO  c.i.provider.YahooFinanceProvider - Requesting data from Yahoo Finance for symbol: AERGP, from 1963-05-30 to 2025-05-30
2025-05-30 00:47:26 [main] INFO  c.i.provider.YahooFinanceScraper - Scraping data from Yahoo Finance: https://finance.yahoo.com/quote/AERGP/history/?period1=-*********&period2=**********&interval=1d
2025-05-30 00:47:29 [main] INFO  c.i.provider.YahooFinanceScraper - Saved HTML content to file: C:\Users\<USER>\Downloads\yahoo-finance-html\yahoo_finance_AERGP_1748537249.html
2025-05-30 00:47:29 [main] INFO  c.i.provider.YahooFinanceScraper - Read HTML content from file: C:\Users\<USER>\Downloads\yahoo-finance-html\yahoo_finance_AERGP_1748537249.html
2025-05-30 00:47:29 [main] INFO  c.i.provider.YahooFinanceScraper - Extracted 0 OHLCV data points for AERGP
2025-05-30 00:47:29 [main] INFO  c.i.provider.YahooFinanceProvider - Downloaded and saved 0 data points for AERGP
2025-05-30 00:47:29 [main] INFO  c.investment.MarketDataDownloaderApp - ✓ Successfully processed AERGP
2025-05-30 00:47:29 [main] INFO  c.investment.MarketDataDownloaderApp - Processing instrument 190/10009: AERT (Aeries Technology, Inc.)
2025-05-30 00:47:29 [main] INFO  c.investment.MarketDataDownloaderApp - Downloading data for AERT from 1963-05-30 to 2025-05-30 (attempt 1/4)
2025-05-30 00:47:29 [main] INFO  c.i.provider.YahooFinanceProvider - Requesting data from Yahoo Finance for symbol: AERT, from 1963-05-30 to 2025-05-30
2025-05-30 00:47:29 [main] INFO  c.i.provider.YahooFinanceScraper - Scraping data from Yahoo Finance: https://finance.yahoo.com/quote/AERT/history/?period1=-*********&period2=**********&interval=1d
2025-05-30 00:47:31 [main] INFO  c.i.provider.YahooFinanceScraper - Saved HTML content to file: C:\Users\<USER>\Downloads\yahoo-finance-html\yahoo_finance_AERT_1748537251.html
2025-05-30 00:47:31 [main] INFO  c.i.provider.YahooFinanceScraper - Read HTML content from file: C:\Users\<USER>\Downloads\yahoo-finance-html\yahoo_finance_AERT_1748537251.html
2025-05-30 00:47:31 [main] INFO  c.i.provider.YahooFinanceScraper - Extracted 694 OHLCV data points for AERT
2025-05-30 00:47:31 [main] INFO  c.i.database.DatabaseManager - Saved 694 OHLCV data points for AERT
2025-05-30 00:47:31 [main] INFO  c.i.provider.YahooFinanceProvider - Downloaded and saved 694 data points for AERT
2025-05-30 00:47:31 [main] INFO  c.investment.MarketDataDownloaderApp - ✓ Successfully processed AERT
2025-05-30 00:47:32 [main] INFO  c.investment.MarketDataDownloaderApp - Processing instrument 191/10009: AERTW (Aeries Technology, Inc.)
2025-05-30 00:47:32 [main] INFO  c.investment.MarketDataDownloaderApp - Downloading data for AERTW from 1963-05-30 to 2025-05-30 (attempt 1/4)
2025-05-30 00:47:32 [main] INFO  c.i.provider.YahooFinanceProvider - Requesting data from Yahoo Finance for symbol: AERTW, from 1963-05-30 to 2025-05-30
2025-05-30 00:47:32 [main] INFO  c.i.provider.YahooFinanceScraper - Scraping data from Yahoo Finance: https://finance.yahoo.com/quote/AERTW/history/?period1=-*********&period2=**********&interval=1d
2025-05-30 00:47:34 [main] INFO  c.i.provider.YahooFinanceScraper - Saved HTML content to file: C:\Users\<USER>\Downloads\yahoo-finance-html\yahoo_finance_AERTW_1748537254.html
2025-05-30 00:47:34 [main] INFO  c.i.provider.YahooFinanceScraper - Read HTML content from file: C:\Users\<USER>\Downloads\yahoo-finance-html\yahoo_finance_AERTW_1748537254.html
2025-05-30 00:47:34 [main] INFO  c.i.provider.YahooFinanceScraper - Extracted 1 OHLCV data points for AERTW
2025-05-30 00:47:34 [main] INFO  c.i.database.DatabaseManager - Saved 1 OHLCV data points for AERTW
2025-05-30 00:47:34 [main] INFO  c.i.provider.YahooFinanceProvider - Downloaded and saved 1 data points for AERTW
2025-05-30 00:47:34 [main] INFO  c.investment.MarketDataDownloaderApp - ✓ Successfully processed AERTW
2025-05-30 00:47:34 [main] INFO  c.investment.MarketDataDownloaderApp - Processing instrument 192/10009: AES (AES CORP)
2025-05-30 00:47:34 [main] INFO  c.investment.MarketDataDownloaderApp - Downloading data for AES from 1963-05-30 to 2025-05-30 (attempt 1/4)
2025-05-30 00:47:34 [main] INFO  c.i.provider.YahooFinanceProvider - Requesting data from Yahoo Finance for symbol: AES, from 1963-05-30 to 2025-05-30
2025-05-30 00:47:34 [main] INFO  c.i.provider.YahooFinanceScraper - Scraping data from Yahoo Finance: https://finance.yahoo.com/quote/AES/history/?period1=-*********&period2=**********&interval=1d
2025-05-30 00:47:38 [main] INFO  c.i.provider.YahooFinanceScraper - Saved HTML content to file: C:\Users\<USER>\Downloads\yahoo-finance-html\yahoo_finance_AES_1748537258.html
2025-05-30 00:47:38 [main] INFO  c.i.provider.YahooFinanceScraper - Read HTML content from file: C:\Users\<USER>\Downloads\yahoo-finance-html\yahoo_finance_AES_1748537258.html
2025-05-30 00:47:38 [main] INFO  c.i.provider.YahooFinanceScraper - Extracted 8543 OHLCV data points for AES
2025-05-30 00:47:41 [main] INFO  c.i.database.DatabaseManager - Saved 8543 OHLCV data points for AES
2025-05-30 00:47:41 [main] INFO  c.i.provider.YahooFinanceProvider - Downloaded and saved 8543 data points for AES
2025-05-30 00:47:41 [main] INFO  c.investment.MarketDataDownloaderApp - ✓ Successfully processed AES
2025-05-30 00:47:41 [main] INFO  c.investment.MarketDataDownloaderApp - Processing instrument 193/10009: AESI (Atlas Energy Solutions Inc.)
2025-05-30 00:47:41 [main] INFO  c.investment.MarketDataDownloaderApp - Downloading data for AESI from 1963-05-30 to 2025-05-30 (attempt 1/4)
2025-05-30 00:47:42 [main] INFO  c.i.provider.YahooFinanceProvider - Requesting data from Yahoo Finance for symbol: AESI, from 1963-05-30 to 2025-05-30
2025-05-30 00:47:42 [main] INFO  c.i.provider.YahooFinanceScraper - Scraping data from Yahoo Finance: https://finance.yahoo.com/quote/AESI/history/?period1=-*********&period2=**********&interval=1d
2025-05-30 00:47:42 [main] INFO  c.i.provider.YahooFinanceScraper - Saved HTML content to file: C:\Users\<USER>\Downloads\yahoo-finance-html\yahoo_finance_AESI_1748537262.html
2025-05-30 00:47:42 [main] INFO  c.i.provider.YahooFinanceScraper - Read HTML content from file: C:\Users\<USER>\Downloads\yahoo-finance-html\yahoo_finance_AESI_1748537262.html
2025-05-30 00:47:42 [main] INFO  c.i.provider.YahooFinanceScraper - Extracted 558 OHLCV data points for AESI
2025-05-30 00:47:43 [main] INFO  c.i.database.DatabaseManager - Saved 558 OHLCV data points for AESI
2025-05-30 00:47:43 [main] INFO  c.i.provider.YahooFinanceProvider - Downloaded and saved 558 data points for AESI
2025-05-30 00:47:43 [main] INFO  c.investment.MarketDataDownloaderApp - ✓ Successfully processed AESI
2025-05-30 00:47:43 [main] INFO  c.investment.MarketDataDownloaderApp - Processing instrument 194/10009: AETHF (Aether Global Innovations Corp.)
2025-05-30 00:47:43 [main] INFO  c.investment.MarketDataDownloaderApp - Downloading data for AETHF from 1963-05-30 to 2025-05-30 (attempt 1/4)
2025-05-30 00:47:43 [main] INFO  c.i.provider.YahooFinanceProvider - Requesting data from Yahoo Finance for symbol: AETHF, from 1963-05-30 to 2025-05-30
2025-05-30 00:47:43 [main] INFO  c.i.provider.YahooFinanceScraper - Scraping data from Yahoo Finance: https://finance.yahoo.com/quote/AETHF/history/?period1=-*********&period2=**********&interval=1d
2025-05-30 00:47:46 [main] INFO  c.i.provider.YahooFinanceScraper - Saved HTML content to file: C:\Users\<USER>\Downloads\yahoo-finance-html\yahoo_finance_AETHF_1748537266.html
2025-05-30 00:47:46 [main] INFO  c.i.provider.YahooFinanceScraper - Read HTML content from file: C:\Users\<USER>\Downloads\yahoo-finance-html\yahoo_finance_AETHF_1748537266.html
2025-05-30 00:47:46 [main] INFO  c.i.provider.YahooFinanceScraper - Extracted 1405 OHLCV data points for AETHF
2025-05-30 00:47:46 [main] INFO  c.i.database.DatabaseManager - Saved 1405 OHLCV data points for AETHF
2025-05-30 00:47:46 [main] INFO  c.i.provider.YahooFinanceProvider - Downloaded and saved 1405 data points for AETHF
2025-05-30 00:47:46 [main] INFO  c.investment.MarketDataDownloaderApp - ✓ Successfully processed AETHF
2025-05-30 00:47:47 [main] INFO  c.investment.MarketDataDownloaderApp - Processing instrument 195/10009: AEVA (Aeva Technologies, Inc.)
2025-05-30 00:47:47 [main] INFO  c.investment.MarketDataDownloaderApp - Downloading data for AEVA from 1963-05-30 to 2025-05-30 (attempt 1/4)
2025-05-30 00:47:47 [main] INFO  c.i.provider.YahooFinanceProvider - Requesting data from Yahoo Finance for symbol: AEVA, from 1963-05-30 to 2025-05-30
2025-05-30 00:47:47 [main] INFO  c.i.provider.YahooFinanceScraper - Scraping data from Yahoo Finance: https://finance.yahoo.com/quote/AEVA/history/?period1=-*********&period2=**********&interval=1d
2025-05-30 00:47:49 [main] INFO  c.i.provider.YahooFinanceScraper - Saved HTML content to file: C:\Users\<USER>\Downloads\yahoo-finance-html\yahoo_finance_AEVA_1748537269.html
2025-05-30 00:47:49 [main] INFO  c.i.provider.YahooFinanceScraper - Read HTML content from file: C:\Users\<USER>\Downloads\yahoo-finance-html\yahoo_finance_AEVA_1748537269.html
2025-05-30 00:47:49 [main] INFO  c.i.provider.YahooFinanceScraper - Extracted 1271 OHLCV data points for AEVA
2025-05-30 00:47:49 [main] INFO  c.i.database.DatabaseManager - Saved 1271 OHLCV data points for AEVA
2025-05-30 00:47:49 [main] INFO  c.i.provider.YahooFinanceProvider - Downloaded and saved 1271 data points for AEVA
2025-05-30 00:47:49 [main] INFO  c.investment.MarketDataDownloaderApp - ✓ Successfully processed AEVA
2025-05-30 00:47:50 [main] INFO  c.investment.MarketDataDownloaderApp - Processing instrument 196/10009: AEVAW (Aeva Technologies, Inc.)
2025-05-30 00:47:50 [main] INFO  c.investment.MarketDataDownloaderApp - Downloading data for AEVAW from 1963-05-30 to 2025-05-30 (attempt 1/4)
2025-05-30 00:47:50 [main] INFO  c.i.provider.YahooFinanceProvider - Requesting data from Yahoo Finance for symbol: AEVAW, from 1963-05-30 to 2025-05-30
2025-05-30 00:47:50 [main] INFO  c.i.provider.YahooFinanceScraper - Scraping data from Yahoo Finance: https://finance.yahoo.com/quote/AEVAW/history/?period1=-*********&period2=**********&interval=1d
2025-05-30 00:47:51 [main] INFO  c.i.provider.YahooFinanceScraper - Saved HTML content to file: C:\Users\<USER>\Downloads\yahoo-finance-html\yahoo_finance_AEVAW_1748537271.html
2025-05-30 00:47:51 [main] INFO  c.i.provider.YahooFinanceScraper - Read HTML content from file: C:\Users\<USER>\Downloads\yahoo-finance-html\yahoo_finance_AEVAW_1748537271.html
2025-05-30 00:47:51 [main] INFO  c.i.provider.YahooFinanceScraper - Extracted 1 OHLCV data points for AEVAW
2025-05-30 00:47:51 [main] INFO  c.i.database.DatabaseManager - Saved 1 OHLCV data points for AEVAW
2025-05-30 00:47:51 [main] INFO  c.i.provider.YahooFinanceProvider - Downloaded and saved 1 data points for AEVAW
2025-05-30 00:47:51 [main] INFO  c.investment.MarketDataDownloaderApp - ✓ Successfully processed AEVAW
2025-05-30 00:47:51 [main] INFO  c.investment.MarketDataDownloaderApp - Processing instrument 197/10009: AEYE (AUDIOEYE INC)
2025-05-30 00:47:51 [main] INFO  c.investment.MarketDataDownloaderApp - Downloading data for AEYE from 1963-05-30 to 2025-05-30 (attempt 1/4)
2025-05-30 00:47:51 [main] INFO  c.i.provider.YahooFinanceProvider - Requesting data from Yahoo Finance for symbol: AEYE, from 1963-05-30 to 2025-05-30
2025-05-30 00:47:51 [main] INFO  c.i.provider.YahooFinanceScraper - Scraping data from Yahoo Finance: https://finance.yahoo.com/quote/AEYE/history/?period1=-*********&period2=**********&interval=1d
2025-05-30 00:47:54 [main] INFO  c.i.provider.YahooFinanceScraper - Saved HTML content to file: C:\Users\<USER>\Downloads\yahoo-finance-html\yahoo_finance_AEYE_1748537274.html
2025-05-30 00:47:54 [main] INFO  c.i.provider.YahooFinanceScraper - Read HTML content from file: C:\Users\<USER>\Downloads\yahoo-finance-html\yahoo_finance_AEYE_1748537274.html
2025-05-30 00:47:54 [main] INFO  c.i.provider.YahooFinanceScraper - Extracted 2973 OHLCV data points for AEYE
2025-05-30 00:47:55 [main] INFO  c.i.database.DatabaseManager - Saved 2973 OHLCV data points for AEYE
2025-05-30 00:47:55 [main] INFO  c.i.provider.YahooFinanceProvider - Downloaded and saved 2973 data points for AEYE
2025-05-30 00:47:55 [main] INFO  c.investment.MarketDataDownloaderApp - ✓ Successfully processed AEYE
2025-05-30 00:47:55 [main] INFO  c.investment.MarketDataDownloaderApp - Processing instrument 198/10009: AFB (ALLIANCEBERNSTEIN NATIONAL MUNICIPAL INCOME FUND)
2025-05-30 00:47:55 [main] INFO  c.investment.MarketDataDownloaderApp - Downloading data for AFB from 1963-05-30 to 2025-05-30 (attempt 1/4)
2025-05-30 00:47:55 [main] INFO  c.i.provider.YahooFinanceProvider - Requesting data from Yahoo Finance for symbol: AFB, from 1963-05-30 to 2025-05-30
2025-05-30 00:47:55 [main] INFO  c.i.provider.YahooFinanceScraper - Scraping data from Yahoo Finance: https://finance.yahoo.com/quote/AFB/history/?period1=-*********&period2=**********&interval=1d
2025-05-30 00:48:01 [main] INFO  c.i.provider.YahooFinanceScraper - Saved HTML content to file: C:\Users\<USER>\Downloads\yahoo-finance-html\yahoo_finance_AFB_1748537281.html
2025-05-30 00:48:01 [main] INFO  c.i.provider.YahooFinanceScraper - Read HTML content from file: C:\Users\<USER>\Downloads\yahoo-finance-html\yahoo_finance_AFB_1748537281.html
2025-05-30 00:48:01 [main] INFO  c.i.provider.YahooFinanceScraper - Extracted 5872 OHLCV data points for AFB
2025-05-30 00:48:03 [main] INFO  c.i.database.DatabaseManager - Saved 5872 OHLCV data points for AFB
2025-05-30 00:48:03 [main] INFO  c.i.provider.YahooFinanceProvider - Downloaded and saved 5872 data points for AFB
2025-05-30 00:48:03 [main] INFO  c.investment.MarketDataDownloaderApp - ✓ Successfully processed AFB
2025-05-30 00:48:03 [main] INFO  c.investment.MarketDataDownloaderApp - Processing instrument 199/10009: AFBI (Affinity Bancshares, Inc.)
2025-05-30 00:48:03 [main] INFO  c.investment.MarketDataDownloaderApp - Downloading data for AFBI from 1963-05-30 to 2025-05-30 (attempt 1/4)
2025-05-30 00:48:03 [main] INFO  c.i.provider.YahooFinanceProvider - Requesting data from Yahoo Finance for symbol: AFBI, from 1963-05-30 to 2025-05-30
2025-05-30 00:48:03 [main] INFO  c.i.provider.YahooFinanceScraper - Scraping data from Yahoo Finance: https://finance.yahoo.com/quote/AFBI/history/?period1=-*********&period2=**********&interval=1d
2025-05-30 00:48:06 [main] INFO  c.i.provider.YahooFinanceScraper - Saved HTML content to file: C:\Users\<USER>\Downloads\yahoo-finance-html\yahoo_finance_AFBI_1748537286.html
2025-05-30 00:48:06 [main] INFO  c.i.provider.YahooFinanceScraper - Read HTML content from file: C:\Users\<USER>\Downloads\yahoo-finance-html\yahoo_finance_AFBI_1748537286.html
2025-05-30 00:48:06 [main] INFO  c.i.provider.YahooFinanceScraper - Extracted 1945 OHLCV data points for AFBI
2025-05-30 00:48:06 [main] INFO  c.i.database.DatabaseManager - Saved 1945 OHLCV data points for AFBI
2025-05-30 00:48:06 [main] INFO  c.i.provider.YahooFinanceProvider - Downloaded and saved 1945 data points for AFBI
2025-05-30 00:48:06 [main] INFO  c.investment.MarketDataDownloaderApp - ✓ Successfully processed AFBI
2025-05-30 00:48:07 [main] INFO  c.investment.MarketDataDownloaderApp - Processing instrument 200/10009: AFBL (AFB Ltd)
2025-05-30 00:48:07 [main] INFO  c.investment.MarketDataDownloaderApp - Downloading data for AFBL from 1963-05-30 to 2025-05-30 (attempt 1/4)
2025-05-30 00:48:07 [main] INFO  c.i.provider.YahooFinanceProvider - Requesting data from Yahoo Finance for symbol: AFBL, from 1963-05-30 to 2025-05-30
2025-05-30 00:48:07 [main] INFO  c.i.provider.YahooFinanceScraper - Scraping data from Yahoo Finance: https://finance.yahoo.com/quote/AFBL/history/?period1=-*********&period2=**********&interval=1d
2025-05-30 00:48:08 [main] INFO  c.i.provider.YahooFinanceScraper - Saved HTML content to file: C:\Users\<USER>\Downloads\yahoo-finance-html\yahoo_finance_AFBL_1748537288.html
2025-05-30 00:48:08 [main] INFO  c.i.provider.YahooFinanceScraper - Read HTML content from file: C:\Users\<USER>\Downloads\yahoo-finance-html\yahoo_finance_AFBL_1748537288.html
2025-05-30 00:48:08 [main] INFO  c.i.provider.YahooFinanceScraper - Extracted 24 OHLCV data points for AFBL
2025-05-30 00:48:08 [main] INFO  c.i.database.DatabaseManager - Saved 24 OHLCV data points for AFBL
2025-05-30 00:48:08 [main] INFO  c.i.provider.YahooFinanceProvider - Downloaded and saved 24 data points for AFBL
2025-05-30 00:48:08 [main] INFO  c.investment.MarketDataDownloaderApp - ✓ Successfully processed AFBL
2025-05-30 00:48:08 [main] INFO  c.investment.MarketDataDownloaderApp - Processing instrument 201/10009: AFCG (Advanced Flower Capital Inc.)
2025-05-30 00:48:08 [main] INFO  c.investment.MarketDataDownloaderApp - Downloading data for AFCG from 1963-05-30 to 2025-05-30 (attempt 1/4)
2025-05-30 00:48:08 [main] INFO  c.i.provider.YahooFinanceProvider - Requesting data from Yahoo Finance for symbol: AFCG, from 1963-05-30 to 2025-05-30
2025-05-30 00:48:08 [main] INFO  c.i.provider.YahooFinanceScraper - Scraping data from Yahoo Finance: https://finance.yahoo.com/quote/AFCG/history/?period1=-*********&period2=**********&interval=1d
2025-05-30 00:48:10 [main] INFO  c.i.provider.YahooFinanceScraper - Saved HTML content to file: C:\Users\<USER>\Downloads\yahoo-finance-html\yahoo_finance_AFCG_1748537290.html
2025-05-30 00:48:10 [main] INFO  c.i.provider.YahooFinanceScraper - Read HTML content from file: C:\Users\<USER>\Downloads\yahoo-finance-html\yahoo_finance_AFCG_1748537290.html
2025-05-30 00:48:10 [main] INFO  c.i.provider.YahooFinanceScraper - Extracted 1054 OHLCV data points for AFCG
2025-05-30 00:48:10 [main] INFO  c.i.database.DatabaseManager - Saved 1054 OHLCV data points for AFCG
2025-05-30 00:48:10 [main] INFO  c.i.provider.YahooFinanceProvider - Downloaded and saved 1054 data points for AFCG
2025-05-30 00:48:10 [main] INFO  c.investment.MarketDataDownloaderApp - ✓ Successfully processed AFCG
2025-05-30 00:48:11 [main] INFO  c.investment.MarketDataDownloaderApp - Processing instrument 202/10009: AFG (AMERICAN FINANCIAL GROUP INC)
2025-05-30 00:48:11 [main] INFO  c.investment.MarketDataDownloaderApp - Downloading data for AFG from 1963-05-30 to 2025-05-30 (attempt 1/4)
2025-05-30 00:48:11 [main] INFO  c.i.provider.YahooFinanceProvider - Requesting data from Yahoo Finance for symbol: AFG, from 1963-05-30 to 2025-05-30
2025-05-30 00:48:11 [main] INFO  c.i.provider.YahooFinanceScraper - Scraping data from Yahoo Finance: https://finance.yahoo.com/quote/AFG/history/?period1=-*********&period2=**********&interval=1d
2025-05-30 00:48:20 [main] ERROR c.i.provider.YahooFinanceProvider - Error downloading data for instrument: AFG
okhttp3.internal.http2.StreamResetException: stream was reset: NO_ERROR
	at okhttp3.internal.http2.Http2Stream$FramingSource.read(Http2Stream.kt:355)
	at okhttp3.internal.connection.Exchange$ResponseBodySource.read(Exchange.kt:276)
	at okio.RealBufferedSource.read(RealBufferedSource.kt:189)
	at okio.RealBufferedSource.exhausted(RealBufferedSource.kt:197)
	at okio.InflaterSource.refill(InflaterSource.kt:112)
	at okio.InflaterSource.readOrInflate(InflaterSource.kt:76)
	at okio.InflaterSource.read(InflaterSource.kt:49)
	at okio.GzipSource.read(GzipSource.kt:69)
	at okio.Buffer.writeAll(Buffer.kt:1290)
	at okio.RealBufferedSource.readString(RealBufferedSource.kt:95)
	at okhttp3.ResponseBody.string(ResponseBody.kt:187)
	at com.investment.provider.YahooFinanceScraper.scrapeHistoricalData(YahooFinanceScraper.java:153)
	at com.investment.provider.YahooFinanceProvider.downloadHistoricalData(YahooFinanceProvider.java:32)
	at com.investment.MarketDataDownloaderApp.downloadDataForInstrument(MarketDataDownloaderApp.java:145)
	at com.investment.MarketDataDownloaderApp.downloadMarketDataForInstruments(MarketDataDownloaderApp.java:73)
	at com.investment.MarketDataDownloaderApp.main(MarketDataDownloaderApp.java:44)
2025-05-30 00:48:20 [main] WARN  c.investment.MarketDataDownloaderApp - Attempt 1/4 failed for AFG: Failed to download historical data. Retrying in 1000ms...
2025-05-30 00:48:21 [main] INFO  c.investment.MarketDataDownloaderApp - Downloading data for AFG from 1963-05-30 to 2025-05-30 (attempt 2/4)
2025-05-30 00:48:21 [main] INFO  c.i.provider.YahooFinanceProvider - Requesting data from Yahoo Finance for symbol: AFG, from 1963-05-30 to 2025-05-30
2025-05-30 00:48:21 [main] INFO  c.i.provider.YahooFinanceScraper - Scraping data from Yahoo Finance: https://finance.yahoo.com/quote/AFG/history/?period1=-*********&period2=**********&interval=1d
2025-05-30 00:48:27 [main] INFO  c.i.provider.YahooFinanceScraper - Saved HTML content to file: C:\Users\<USER>\Downloads\yahoo-finance-html\yahoo_finance_AFG_1748537307.html
2025-05-30 00:48:27 [main] INFO  c.i.provider.YahooFinanceScraper - Read HTML content from file: C:\Users\<USER>\Downloads\yahoo-finance-html\yahoo_finance_AFG_1748537307.html
2025-05-30 00:48:27 [main] INFO  c.i.provider.YahooFinanceScraper - Extracted 11391 OHLCV data points for AFG
2025-05-30 00:48:30 [main] INFO  c.i.database.DatabaseManager - Saved 11391 OHLCV data points for AFG
2025-05-30 00:48:30 [main] INFO  c.i.provider.YahooFinanceProvider - Downloaded and saved 11391 data points for AFG
2025-05-30 00:48:30 [main] INFO  c.investment.MarketDataDownloaderApp - ✓ Successfully processed AFG
2025-05-30 00:48:30 [main] INFO  c.investment.MarketDataDownloaderApp - Processing instrument 203/10009: AFGB (AMERICAN FINANCIAL GROUP INC)
2025-05-30 00:48:30 [main] INFO  c.investment.MarketDataDownloaderApp - Downloading data for AFGB from 1963-05-30 to 2025-05-30 (attempt 1/4)
2025-05-30 00:48:30 [main] INFO  c.i.provider.YahooFinanceProvider - Requesting data from Yahoo Finance for symbol: AFGB, from 1963-05-30 to 2025-05-30
2025-05-30 00:48:30 [main] INFO  c.i.provider.YahooFinanceScraper - Scraping data from Yahoo Finance: https://finance.yahoo.com/quote/AFGB/history/?period1=-*********&period2=**********&interval=1d
2025-05-30 00:48:32 [main] INFO  c.i.provider.YahooFinanceScraper - Saved HTML content to file: C:\Users\<USER>\Downloads\yahoo-finance-html\yahoo_finance_AFGB_1748537312.html
2025-05-30 00:48:32 [main] INFO  c.i.provider.YahooFinanceScraper - Read HTML content from file: C:\Users\<USER>\Downloads\yahoo-finance-html\yahoo_finance_AFGB_1748537312.html
2025-05-30 00:48:32 [main] INFO  c.i.provider.YahooFinanceScraper - Extracted 1296 OHLCV data points for AFGB
2025-05-30 00:48:33 [main] INFO  c.i.database.DatabaseManager - Saved 1296 OHLCV data points for AFGB
2025-05-30 00:48:33 [main] INFO  c.i.provider.YahooFinanceProvider - Downloaded and saved 1296 data points for AFGB
2025-05-30 00:48:33 [main] INFO  c.investment.MarketDataDownloaderApp - ✓ Successfully processed AFGB
2025-05-30 00:48:33 [main] INFO  c.investment.MarketDataDownloaderApp - Processing instrument 204/10009: AFGC (AMERICAN FINANCIAL GROUP INC)
2025-05-30 00:48:33 [main] INFO  c.investment.MarketDataDownloaderApp - Downloading data for AFGC from 1963-05-30 to 2025-05-30 (attempt 1/4)
2025-05-30 00:48:33 [main] INFO  c.i.provider.YahooFinanceProvider - Requesting data from Yahoo Finance for symbol: AFGC, from 1963-05-30 to 2025-05-30
2025-05-30 00:48:33 [main] INFO  c.i.provider.YahooFinanceScraper - Scraping data from Yahoo Finance: https://finance.yahoo.com/quote/AFGC/history/?period1=-*********&period2=**********&interval=1d
2025-05-30 00:48:35 [main] INFO  c.i.provider.YahooFinanceScraper - Saved HTML content to file: C:\Users\<USER>\Downloads\yahoo-finance-html\yahoo_finance_AFGC_1748537315.html
2025-05-30 00:48:35 [main] INFO  c.i.provider.YahooFinanceScraper - Read HTML content from file: C:\Users\<USER>\Downloads\yahoo-finance-html\yahoo_finance_AFGC_1748537315.html
2025-05-30 00:48:35 [main] INFO  c.i.provider.YahooFinanceScraper - Extracted 1347 OHLCV data points for AFGC
2025-05-30 00:48:36 [main] INFO  c.i.database.DatabaseManager - Saved 1347 OHLCV data points for AFGC
2025-05-30 00:48:36 [main] INFO  c.i.provider.YahooFinanceProvider - Downloaded and saved 1347 data points for AFGC
2025-05-30 00:48:36 [main] INFO  c.investment.MarketDataDownloaderApp - ✓ Successfully processed AFGC
2025-05-30 00:48:36 [main] INFO  c.investment.MarketDataDownloaderApp - Processing instrument 205/10009: AFGD (AMERICAN FINANCIAL GROUP INC)
2025-05-30 00:48:36 [main] INFO  c.investment.MarketDataDownloaderApp - Downloading data for AFGD from 1963-05-30 to 2025-05-30 (attempt 1/4)
2025-05-30 00:48:36 [main] INFO  c.i.provider.YahooFinanceProvider - Requesting data from Yahoo Finance for symbol: AFGD, from 1963-05-30 to 2025-05-30
2025-05-30 00:48:36 [main] INFO  c.i.provider.YahooFinanceScraper - Scraping data from Yahoo Finance: https://finance.yahoo.com/quote/AFGD/history/?period1=-*********&period2=**********&interval=1d
2025-05-30 00:48:38 [main] INFO  c.i.provider.YahooFinanceScraper - Saved HTML content to file: C:\Users\<USER>\Downloads\yahoo-finance-html\yahoo_finance_AFGD_1748537318.html
2025-05-30 00:48:38 [main] INFO  c.i.provider.YahooFinanceScraper - Read HTML content from file: C:\Users\<USER>\Downloads\yahoo-finance-html\yahoo_finance_AFGD_1748537318.html
2025-05-30 00:48:38 [main] INFO  c.i.provider.YahooFinanceScraper - Extracted 1252 OHLCV data points for AFGD
2025-05-30 00:48:38 [main] INFO  c.i.database.DatabaseManager - Saved 1252 OHLCV data points for AFGD
2025-05-30 00:48:38 [main] INFO  c.i.provider.YahooFinanceProvider - Downloaded and saved 1252 data points for AFGD
2025-05-30 00:48:38 [main] INFO  c.investment.MarketDataDownloaderApp - ✓ Successfully processed AFGD
2025-05-30 00:48:39 [main] INFO  c.investment.MarketDataDownloaderApp - Processing instrument 206/10009: AFGE (AMERICAN FINANCIAL GROUP INC)
2025-05-30 00:48:39 [main] INFO  c.investment.MarketDataDownloaderApp - Downloading data for AFGE from 1963-05-30 to 2025-05-30 (attempt 1/4)
2025-05-30 00:48:39 [main] INFO  c.i.provider.YahooFinanceProvider - Requesting data from Yahoo Finance for symbol: AFGE, from 1963-05-30 to 2025-05-30
2025-05-30 00:48:39 [main] INFO  c.i.provider.YahooFinanceScraper - Scraping data from Yahoo Finance: https://finance.yahoo.com/quote/AFGE/history/?period1=-*********&period2=**********&interval=1d
2025-05-30 00:48:40 [main] INFO  c.i.provider.YahooFinanceScraper - Saved HTML content to file: C:\Users\<USER>\Downloads\yahoo-finance-html\yahoo_finance_AFGE_1748537320.html
2025-05-30 00:48:40 [main] INFO  c.i.provider.YahooFinanceScraper - Read HTML content from file: C:\Users\<USER>\Downloads\yahoo-finance-html\yahoo_finance_AFGE_1748537320.html
2025-05-30 00:48:40 [main] INFO  c.i.provider.YahooFinanceScraper - Extracted 1149 OHLCV data points for AFGE
2025-05-30 00:48:41 [main] INFO  c.i.database.DatabaseManager - Saved 1149 OHLCV data points for AFGE
2025-05-30 00:48:41 [main] INFO  c.i.provider.YahooFinanceProvider - Downloaded and saved 1149 data points for AFGE
2025-05-30 00:48:41 [main] INFO  c.investment.MarketDataDownloaderApp - ✓ Successfully processed AFGE
2025-05-30 00:48:41 [main] INFO  c.investment.MarketDataDownloaderApp - Processing instrument 207/10009: AFIB (Acutus Medical, Inc.)
2025-05-30 00:48:41 [main] INFO  c.investment.MarketDataDownloaderApp - Downloading data for AFIB from 1963-05-30 to 2025-05-30 (attempt 1/4)
2025-05-30 00:48:41 [main] INFO  c.i.provider.YahooFinanceProvider - Requesting data from Yahoo Finance for symbol: AFIB, from 1963-05-30 to 2025-05-30
2025-05-30 00:48:41 [main] INFO  c.i.provider.YahooFinanceScraper - Scraping data from Yahoo Finance: https://finance.yahoo.com/quote/AFIB/history/?period1=-*********&period2=**********&interval=1d
2025-05-30 00:48:43 [main] INFO  c.i.provider.YahooFinanceScraper - Saved HTML content to file: C:\Users\<USER>\Downloads\yahoo-finance-html\yahoo_finance_AFIB_1748537323.html
2025-05-30 00:48:43 [main] INFO  c.i.provider.YahooFinanceScraper - Read HTML content from file: C:\Users\<USER>\Downloads\yahoo-finance-html\yahoo_finance_AFIB_1748537323.html
2025-05-30 00:48:43 [main] INFO  c.i.provider.YahooFinanceScraper - Extracted 1199 OHLCV data points for AFIB
2025-05-30 00:48:44 [main] INFO  c.i.database.DatabaseManager - Saved 1199 OHLCV data points for AFIB
2025-05-30 00:48:44 [main] INFO  c.i.provider.YahooFinanceProvider - Downloaded and saved 1199 data points for AFIB
2025-05-30 00:48:44 [main] INFO  c.investment.MarketDataDownloaderApp - ✓ Successfully processed AFIB
2025-05-30 00:48:44 [main] INFO  c.investment.MarketDataDownloaderApp - Processing instrument 208/10009: AFJK (Aimei Health Technology Co., Ltd.)
2025-05-30 00:48:44 [main] INFO  c.investment.MarketDataDownloaderApp - Downloading data for AFJK from 1963-05-30 to 2025-05-30 (attempt 1/4)
2025-05-30 00:48:44 [main] INFO  c.i.provider.YahooFinanceProvider - Requesting data from Yahoo Finance for symbol: AFJK, from 1963-05-30 to 2025-05-30
2025-05-30 00:48:44 [main] INFO  c.i.provider.YahooFinanceScraper - Scraping data from Yahoo Finance: https://finance.yahoo.com/quote/AFJK/history/?period1=-*********&period2=**********&interval=1d
2025-05-30 00:48:46 [main] INFO  c.i.provider.YahooFinanceScraper - Saved HTML content to file: C:\Users\<USER>\Downloads\yahoo-finance-html\yahoo_finance_AFJK_1748537326.html
2025-05-30 00:48:46 [main] INFO  c.i.provider.YahooFinanceScraper - Read HTML content from file: C:\Users\<USER>\Downloads\yahoo-finance-html\yahoo_finance_AFJK_1748537326.html
2025-05-30 00:48:46 [main] INFO  c.i.provider.YahooFinanceScraper - Extracted 233 OHLCV data points for AFJK
2025-05-30 00:48:46 [main] INFO  c.i.database.DatabaseManager - Saved 233 OHLCV data points for AFJK
2025-05-30 00:48:46 [main] INFO  c.i.provider.YahooFinanceProvider - Downloaded and saved 233 data points for AFJK
2025-05-30 00:48:46 [main] INFO  c.investment.MarketDataDownloaderApp - ✓ Successfully processed AFJK
2025-05-30 00:48:46 [main] INFO  c.investment.MarketDataDownloaderApp - Processing instrument 209/10009: AFJKR (Aimei Health Technology Co., Ltd.)
2025-05-30 00:48:46 [main] INFO  c.investment.MarketDataDownloaderApp - Downloading data for AFJKR from 1963-05-30 to 2025-05-30 (attempt 1/4)
2025-05-30 00:48:46 [main] INFO  c.i.provider.YahooFinanceProvider - Requesting data from Yahoo Finance for symbol: AFJKR, from 1963-05-30 to 2025-05-30
2025-05-30 00:48:46 [main] INFO  c.i.provider.YahooFinanceScraper - Scraping data from Yahoo Finance: https://finance.yahoo.com/quote/AFJKR/history/?period1=-*********&period2=**********&interval=1d
2025-05-30 00:48:48 [main] INFO  c.i.provider.YahooFinanceScraper - Saved HTML content to file: C:\Users\<USER>\Downloads\yahoo-finance-html\yahoo_finance_AFJKR_1748537328.html
2025-05-30 00:48:48 [main] INFO  c.i.provider.YahooFinanceScraper - Read HTML content from file: C:\Users\<USER>\Downloads\yahoo-finance-html\yahoo_finance_AFJKR_1748537328.html
2025-05-30 00:48:48 [main] INFO  c.i.provider.YahooFinanceScraper - Extracted 0 OHLCV data points for AFJKR
2025-05-30 00:48:48 [main] INFO  c.i.provider.YahooFinanceProvider - Downloaded and saved 0 data points for AFJKR
2025-05-30 00:48:48 [main] INFO  c.investment.MarketDataDownloaderApp - ✓ Successfully processed AFJKR
2025-05-30 00:48:48 [main] INFO  c.investment.MarketDataDownloaderApp - Processing instrument 210/10009: AFJKU (Aimei Health Technology Co., Ltd.)
2025-05-30 00:48:48 [main] INFO  c.investment.MarketDataDownloaderApp - Downloading data for AFJKU from 1963-05-30 to 2025-05-30 (attempt 1/4)
2025-05-30 00:48:48 [main] INFO  c.i.provider.YahooFinanceProvider - Requesting data from Yahoo Finance for symbol: AFJKU, from 1963-05-30 to 2025-05-30
2025-05-30 00:48:48 [main] INFO  c.i.provider.YahooFinanceScraper - Scraping data from Yahoo Finance: https://finance.yahoo.com/quote/AFJKU/history/?period1=-*********&period2=**********&interval=1d
2025-05-30 00:48:50 [main] INFO  c.i.provider.YahooFinanceScraper - Saved HTML content to file: C:\Users\<USER>\Downloads\yahoo-finance-html\yahoo_finance_AFJKU_1748537330.html
2025-05-30 00:48:50 [main] INFO  c.i.provider.YahooFinanceScraper - Read HTML content from file: C:\Users\<USER>\Downloads\yahoo-finance-html\yahoo_finance_AFJKU_1748537330.html
2025-05-30 00:48:50 [main] INFO  c.i.provider.YahooFinanceScraper - Extracted 96 OHLCV data points for AFJKU
2025-05-30 00:48:50 [main] INFO  c.i.database.DatabaseManager - Saved 96 OHLCV data points for AFJKU
2025-05-30 00:48:50 [main] INFO  c.i.provider.YahooFinanceProvider - Downloaded and saved 96 data points for AFJKU
2025-05-30 00:48:50 [main] INFO  c.investment.MarketDataDownloaderApp - ✓ Successfully processed AFJKU
2025-05-30 00:48:51 [main] INFO  c.investment.MarketDataDownloaderApp - Processing instrument 211/10009: AFL (AFLAC INC)
2025-05-30 00:48:51 [main] INFO  c.investment.MarketDataDownloaderApp - Downloading data for AFL from 1963-05-30 to 2025-05-30 (attempt 1/4)
2025-05-30 00:48:51 [main] INFO  c.i.provider.YahooFinanceProvider - Requesting data from Yahoo Finance for symbol: AFL, from 1963-05-30 to 2025-05-30
2025-05-30 00:48:51 [main] INFO  c.i.provider.YahooFinanceScraper - Scraping data from Yahoo Finance: https://finance.yahoo.com/quote/AFL/history/?period1=-*********&period2=**********&interval=1d
2025-05-30 00:48:59 [main] INFO  c.i.provider.YahooFinanceScraper - Saved HTML content to file: C:\Users\<USER>\Downloads\yahoo-finance-html\yahoo_finance_AFL_1748537339.html
2025-05-30 00:48:59 [main] INFO  c.i.provider.YahooFinanceScraper - Read HTML content from file: C:\Users\<USER>\Downloads\yahoo-finance-html\yahoo_finance_AFL_1748537339.html
2025-05-30 00:48:59 [main] INFO  c.i.provider.YahooFinanceScraper - Extracted 11393 OHLCV data points for AFL
2025-05-30 00:49:02 [main] INFO  c.i.database.DatabaseManager - Saved 11393 OHLCV data points for AFL
2025-05-30 00:49:02 [main] INFO  c.i.provider.YahooFinanceProvider - Downloaded and saved 11393 data points for AFL
2025-05-30 00:49:02 [main] INFO  c.investment.MarketDataDownloaderApp - ✓ Successfully processed AFL
2025-05-30 00:49:03 [main] INFO  c.investment.MarketDataDownloaderApp - Processing instrument 212/10009: AFLYY (AIR FRANCE-KLM /FI)
2025-05-30 00:49:03 [main] INFO  c.investment.MarketDataDownloaderApp - Downloading data for AFLYY from 1963-05-30 to 2025-05-30 (attempt 1/4)
2025-05-30 00:49:03 [main] INFO  c.i.provider.YahooFinanceProvider - Requesting data from Yahoo Finance for symbol: AFLYY, from 1963-05-30 to 2025-05-30
2025-05-30 00:49:03 [main] INFO  c.i.provider.YahooFinanceScraper - Scraping data from Yahoo Finance: https://finance.yahoo.com/quote/AFLYY/history/?period1=-*********&period2=**********&interval=1d
2025-05-30 00:49:08 [main] INFO  c.i.provider.YahooFinanceScraper - Saved HTML content to file: C:\Users\<USER>\Downloads\yahoo-finance-html\yahoo_finance_AFLYY_1748537348.html
2025-05-30 00:49:08 [main] INFO  c.i.provider.YahooFinanceScraper - Read HTML content from file: C:\Users\<USER>\Downloads\yahoo-finance-html\yahoo_finance_AFLYY_1748537348.html
2025-05-30 00:49:08 [main] INFO  c.i.provider.YahooFinanceScraper - Extracted 5215 OHLCV data points for AFLYY
2025-05-30 00:49:09 [main] INFO  c.i.database.DatabaseManager - Saved 5215 OHLCV data points for AFLYY
2025-05-30 00:49:09 [main] INFO  c.i.provider.YahooFinanceProvider - Downloaded and saved 5215 data points for AFLYY
2025-05-30 00:49:09 [main] INFO  c.investment.MarketDataDownloaderApp - ✓ Successfully processed AFLYY
2025-05-30 00:49:10 [main] INFO  c.investment.MarketDataDownloaderApp - Processing instrument 213/10009: AFMD (Affimed N.V.)
2025-05-30 00:49:10 [main] INFO  c.investment.MarketDataDownloaderApp - Downloading data for AFMD from 1963-05-30 to 2025-05-30 (attempt 1/4)
2025-05-30 00:49:10 [main] INFO  c.i.provider.YahooFinanceProvider - Requesting data from Yahoo Finance for symbol: AFMD, from 1963-05-30 to 2025-05-30
2025-05-30 00:49:10 [main] INFO  c.i.provider.YahooFinanceScraper - Scraping data from Yahoo Finance: https://finance.yahoo.com/quote/AFMD/history/?period1=-*********&period2=**********&interval=1d
2025-05-30 00:49:13 [main] INFO  c.i.provider.YahooFinanceScraper - Saved HTML content to file: C:\Users\<USER>\Downloads\yahoo-finance-html\yahoo_finance_AFMD_1748537353.html
2025-05-30 00:49:13 [main] INFO  c.i.provider.YahooFinanceScraper - Read HTML content from file: C:\Users\<USER>\Downloads\yahoo-finance-html\yahoo_finance_AFMD_1748537353.html
2025-05-30 00:49:13 [main] INFO  c.i.provider.YahooFinanceScraper - Extracted 2692 OHLCV data points for AFMD
2025-05-30 00:49:14 [main] INFO  c.i.database.DatabaseManager - Saved 2692 OHLCV data points for AFMD
2025-05-30 00:49:14 [main] INFO  c.i.provider.YahooFinanceProvider - Downloaded and saved 2692 data points for AFMD
2025-05-30 00:49:14 [main] INFO  c.investment.MarketDataDownloaderApp - ✓ Successfully processed AFMD
2025-05-30 23:25:29 [Test worker] INFO  c.i.service.CsvInstrumentService - Starting CSV processing - file: instruments.csv, dryRun: true, maxInstruments: 1000, skipDuplicates: true, validateData: true
2025-05-30 23:25:29 [Test worker] INFO  c.i.service.CsvInstrumentService - Loaded 0 existing symbols from database
2025-05-30 23:25:29 [Test worker] INFO  c.i.service.CsvInstrumentService - CSV processing completed - Total rows: 3, Valid: 3, Invalid: 0, Processed: 3
2025-05-30 23:25:29 [Test worker] INFO  c.i.service.CsvInstrumentService - Starting CSV processing - file: instruments.csv, dryRun: false, maxInstruments: 1000, skipDuplicates: true, validateData: true
2025-05-30 23:25:29 [Test worker] INFO  c.i.service.CsvInstrumentService - Loaded 0 existing symbols from database
2025-05-30 23:25:29 [Test worker] INFO  c.i.service.CsvInstrumentService - CSV processing completed - Total rows: 2, Valid: 2, Invalid: 0, Processed: 2
2025-05-30 23:25:29 [Test worker] INFO  c.i.service.CsvInstrumentService - Starting CSV processing - file: instruments.csv, dryRun: true, maxInstruments: 1000, skipDuplicates: true, validateData: true
2025-05-30 23:25:29 [Test worker] INFO  c.i.service.CsvInstrumentService - Loaded 2 existing symbols from database
2025-05-30 23:25:29 [Test worker] INFO  c.i.service.CsvInstrumentService - CSV processing completed - Total rows: 3, Valid: 3, Invalid: 0, Processed: 1
2025-05-30 23:25:29 [Test worker] INFO  c.i.service.CsvInstrumentService - Starting CSV processing - file: instruments.csv, dryRun: true, maxInstruments: 1000, skipDuplicates: true, validateData: true
2025-05-30 23:25:29 [Test worker] INFO  c.i.service.CsvInstrumentService - Loaded 0 existing symbols from database
2025-05-30 23:25:29 [Test worker] INFO  c.i.service.CsvInstrumentService - CSV processing completed - Total rows: 5, Valid: 5, Invalid: 0, Processed: 5
2025-05-30 23:25:29 [Test worker] INFO  c.i.service.CsvInstrumentService - Starting CSV processing - file: instruments.csv, dryRun: true, maxInstruments: 1000, skipDuplicates: true, validateData: true
2025-05-30 23:25:29 [Test worker] ERROR c.i.service.CsvInstrumentService - Unexpected error during CSV processing
java.lang.NullPointerException: Cannot invoke "java.util.Collection.size()" because "c" is null
	at java.base/java.util.HashSet.<init>(HashSet.java:120)
	at com.investment.service.CsvInstrumentService.processCsvFile(CsvInstrumentService.java:84)
	at com.investment.service.CsvInstrumentService$processCsvFile.call(Unknown Source)
	at org.codehaus.groovy.runtime.callsite.CallSiteArray.defaultCall(CallSiteArray.java:47)
	at org.codehaus.groovy.runtime.callsite.AbstractCallSite.call(AbstractCallSite.java:125)
	at com.investment.service.CsvInstrumentServiceSpec.$spock_feature_0_4(CsvInstrumentServiceSpec.groovy:128)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.spockframework.util.ReflectionUtil.invokeMethod(ReflectionUtil.java:187)
	at org.spockframework.runtime.model.MethodInfo.lambda$new$0(MethodInfo.java:49)
	at org.spockframework.runtime.model.MethodInfo.invoke(MethodInfo.java:156)
	at org.spockframework.runtime.extension.MethodInvocation.proceed(MethodInvocation.java:102)
	at org.spockframework.junit4.ExceptionAdapterInterceptor.intercept(ExceptionAdapterInterceptor.java:13)
	at org.spockframework.runtime.extension.MethodInvocation.proceed(MethodInvocation.java:101)
	at org.spockframework.runtime.PlatformSpecRunner.invoke(PlatformSpecRunner.java:398)
	at org.spockframework.runtime.PlatformSpecRunner.runFeatureMethod(PlatformSpecRunner.java:324)
	at org.spockframework.runtime.IterationNode.execute(IterationNode.java:50)
	at org.spockframework.runtime.SimpleFeatureNode.execute(SimpleFeatureNode.java:58)
	at org.spockframework.runtime.SimpleFeatureNode.execute(SimpleFeatureNode.java:15)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$6(NodeTestTask.java:151)
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$8(NodeTestTask.java:141)
	at org.spockframework.runtime.SpockNode.sneakyInvoke(SpockNode.java:40)
	at org.spockframework.runtime.IterationNode.lambda$around$0(IterationNode.java:67)
	at org.spockframework.runtime.PlatformSpecRunner.lambda$createMethodInfoForDoRunIteration$5(PlatformSpecRunner.java:236)
	at org.spockframework.runtime.model.MethodInfo.invoke(MethodInfo.java:156)
	at org.spockframework.runtime.extension.MethodInvocation.proceed(MethodInvocation.java:102)
	at org.spockframework.junit4.ExceptionAdapterInterceptor.intercept(ExceptionAdapterInterceptor.java:13)
	at org.spockframework.runtime.extension.MethodInvocation.proceed(MethodInvocation.java:101)
	at org.spockframework.runtime.PlatformSpecRunner.invoke(PlatformSpecRunner.java:398)
	at org.spockframework.runtime.PlatformSpecRunner.runIteration(PlatformSpecRunner.java:218)
	at org.spockframework.runtime.IterationNode.around(IterationNode.java:67)
	at org.spockframework.runtime.SimpleFeatureNode.lambda$around$0(SimpleFeatureNode.java:52)
	at org.spockframework.runtime.SpockNode.sneakyInvoke(SpockNode.java:40)
	at org.spockframework.runtime.FeatureNode.lambda$around$0(FeatureNode.java:41)
	at org.spockframework.runtime.PlatformSpecRunner.lambda$createMethodInfoForDoRunFeature$4(PlatformSpecRunner.java:199)
	at org.spockframework.runtime.model.MethodInfo.invoke(MethodInfo.java:156)
	at org.spockframework.runtime.PlatformSpecRunner.invokeRaw(PlatformSpecRunner.java:407)
	at org.spockframework.runtime.PlatformSpecRunner.invoke(PlatformSpecRunner.java:390)
	at org.spockframework.runtime.PlatformSpecRunner.runFeature(PlatformSpecRunner.java:192)
	at org.spockframework.runtime.FeatureNode.around(FeatureNode.java:41)
	at org.spockframework.runtime.SimpleFeatureNode.around(SimpleFeatureNode.java:52)
	at org.spockframework.runtime.SimpleFeatureNode.around(SimpleFeatureNode.java:15)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$9(NodeTestTask.java:139)
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.executeRecursively(NodeTestTask.java:138)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.execute(NodeTestTask.java:95)
	at java.base/java.util.ArrayList.forEach(ArrayList.java:1596)
	at org.junit.platform.engine.support.hierarchical.SameThreadHierarchicalTestExecutorService.invokeAll(SameThreadHierarchicalTestExecutorService.java:41)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$6(NodeTestTask.java:155)
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$8(NodeTestTask.java:141)
	at org.spockframework.runtime.SpockNode.sneakyInvoke(SpockNode.java:40)
	at org.spockframework.runtime.SpecNode.lambda$around$0(SpecNode.java:63)
	at org.spockframework.runtime.PlatformSpecRunner.lambda$createMethodInfoForDoRunSpec$0(PlatformSpecRunner.java:61)
	at org.spockframework.runtime.model.MethodInfo.invoke(MethodInfo.java:156)
	at org.spockframework.runtime.PlatformSpecRunner.invokeRaw(PlatformSpecRunner.java:407)
	at org.spockframework.runtime.PlatformSpecRunner.invoke(PlatformSpecRunner.java:390)
	at org.spockframework.runtime.PlatformSpecRunner.runSpec(PlatformSpecRunner.java:55)
	at org.spockframework.runtime.SpecNode.around(SpecNode.java:63)
	at org.spockframework.runtime.SpecNode.around(SpecNode.java:11)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$9(NodeTestTask.java:139)
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.executeRecursively(NodeTestTask.java:138)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.execute(NodeTestTask.java:95)
	at java.base/java.util.ArrayList.forEach(ArrayList.java:1596)
	at org.junit.platform.engine.support.hierarchical.SameThreadHierarchicalTestExecutorService.invokeAll(SameThreadHierarchicalTestExecutorService.java:41)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$6(NodeTestTask.java:155)
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$8(NodeTestTask.java:141)
	at org.junit.platform.engine.support.hierarchical.Node.around(Node.java:137)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$9(NodeTestTask.java:139)
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.executeRecursively(NodeTestTask.java:138)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.execute(NodeTestTask.java:95)
	at org.junit.platform.engine.support.hierarchical.SameThreadHierarchicalTestExecutorService.submit(SameThreadHierarchicalTestExecutorService.java:35)
	at org.junit.platform.engine.support.hierarchical.HierarchicalTestExecutor.execute(HierarchicalTestExecutor.java:57)
	at org.junit.platform.engine.support.hierarchical.HierarchicalTestEngine.execute(HierarchicalTestEngine.java:54)
	at org.junit.platform.launcher.core.EngineExecutionOrchestrator.execute(EngineExecutionOrchestrator.java:198)
	at org.junit.platform.launcher.core.EngineExecutionOrchestrator.execute(EngineExecutionOrchestrator.java:169)
	at org.junit.platform.launcher.core.EngineExecutionOrchestrator.execute(EngineExecutionOrchestrator.java:93)
	at org.junit.platform.launcher.core.EngineExecutionOrchestrator.lambda$execute$0(EngineExecutionOrchestrator.java:58)
	at org.junit.platform.launcher.core.EngineExecutionOrchestrator.withInterceptedStreams(EngineExecutionOrchestrator.java:141)
	at org.junit.platform.launcher.core.EngineExecutionOrchestrator.execute(EngineExecutionOrchestrator.java:57)
	at org.junit.platform.launcher.core.DefaultLauncher.execute(DefaultLauncher.java:103)
	at org.junit.platform.launcher.core.DefaultLauncher.execute(DefaultLauncher.java:85)
	at org.junit.platform.launcher.core.DelegatingLauncher.execute(DelegatingLauncher.java:47)
	at org.gradle.api.internal.tasks.testing.junitplatform.JUnitPlatformTestClassProcessor$CollectAllTestClassesExecutor.processAllTestClasses(JUnitPlatformTestClassProcessor.java:124)
	at org.gradle.api.internal.tasks.testing.junitplatform.JUnitPlatformTestClassProcessor$CollectAllTestClassesExecutor.access$000(JUnitPlatformTestClassProcessor.java:99)
	at org.gradle.api.internal.tasks.testing.junitplatform.JUnitPlatformTestClassProcessor.stop(JUnitPlatformTestClassProcessor.java:94)
	at org.gradle.api.internal.tasks.testing.SuiteTestClassProcessor.stop(SuiteTestClassProcessor.java:63)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.gradle.internal.dispatch.ReflectionDispatch.dispatch(ReflectionDispatch.java:36)
	at org.gradle.internal.dispatch.ReflectionDispatch.dispatch(ReflectionDispatch.java:24)
	at org.gradle.internal.dispatch.ContextClassLoaderDispatch.dispatch(ContextClassLoaderDispatch.java:33)
	at org.gradle.internal.dispatch.ProxyDispatchAdapter$DispatchingInvocationHandler.invoke(ProxyDispatchAdapter.java:92)
	at jdk.proxy1/jdk.proxy1.$Proxy4.stop(Unknown Source)
	at org.gradle.api.internal.tasks.testing.worker.TestWorker$3.run(TestWorker.java:200)
	at org.gradle.api.internal.tasks.testing.worker.TestWorker.executeAndMaintainThreadName(TestWorker.java:132)
	at org.gradle.api.internal.tasks.testing.worker.TestWorker.execute(TestWorker.java:103)
	at org.gradle.api.internal.tasks.testing.worker.TestWorker.execute(TestWorker.java:63)
	at org.gradle.process.internal.worker.child.ActionExecutionWorker.execute(ActionExecutionWorker.java:56)
	at org.gradle.process.internal.worker.child.SystemApplicationClassLoaderWorker.call(SystemApplicationClassLoaderWorker.java:121)
	at org.gradle.process.internal.worker.child.SystemApplicationClassLoaderWorker.call(SystemApplicationClassLoaderWorker.java:71)
	at worker.org.gradle.process.internal.worker.GradleWorkerMain.run(GradleWorkerMain.java:69)
	at worker.org.gradle.process.internal.worker.GradleWorkerMain.main(GradleWorkerMain.java:74)
2025-05-30 23:25:29 [Test worker] INFO  c.i.service.CsvInstrumentService - Starting CSV processing - file: empty.csv, dryRun: true, maxInstruments: 1000, skipDuplicates: true, validateData: true
2025-05-30 23:25:29 [Test worker] INFO  c.i.service.CsvInstrumentService - Starting CSV processing - file: document.txt, dryRun: true, maxInstruments: 1000, skipDuplicates: true, validateData: true
2025-05-30 23:25:29 [Test worker] INFO  c.i.service.CsvInstrumentService - Starting CSV processing - file: instruments.csv, dryRun: true, maxInstruments: 3, skipDuplicates: true, validateData: true
2025-05-30 23:25:29 [Test worker] INFO  c.i.service.CsvInstrumentService - Loaded 0 existing symbols from database
2025-05-30 23:25:29 [Test worker] INFO  c.i.service.CsvInstrumentService - Reached maximum instruments limit: 3
2025-05-30 23:25:29 [Test worker] INFO  c.i.service.CsvInstrumentService - CSV processing completed - Total rows: 4, Valid: 3, Invalid: 0, Processed: 3
2025-05-30 23:25:29 [Test worker] INFO  c.i.service.CsvInstrumentService - Starting CSV processing - file: instruments.csv, dryRun: true, maxInstruments: 1000, skipDuplicates: true, validateData: true
2025-05-30 23:25:29 [Test worker] INFO  c.i.service.CsvInstrumentService - Loaded 0 existing symbols from database
2025-05-30 23:25:29 [Test worker] INFO  c.i.service.CsvInstrumentService - CSV processing completed - Total rows: 3, Valid: 1, Invalid: 2, Processed: 1
2025-05-30 23:25:29 [Test worker] INFO  c.i.service.CsvInstrumentService - Starting CSV processing - file: instruments.csv, dryRun: true, maxInstruments: 1000, skipDuplicates: true, validateData: true
2025-05-30 23:25:29 [Test worker] INFO  c.i.service.CsvInstrumentService - Loaded 0 existing symbols from database
2025-05-30 23:25:29 [Test worker] INFO  c.i.service.CsvInstrumentService - CSV processing completed - Total rows: 4, Valid: 4, Invalid: 0, Processed: 4
2025-05-30 23:26:23 [Test worker] INFO  c.i.service.CsvInstrumentService - Starting CSV processing - file: instruments.csv, dryRun: true, maxInstruments: 1000, skipDuplicates: true, validateData: true
2025-05-30 23:26:23 [Test worker] INFO  c.i.service.CsvInstrumentService - Loaded 0 existing symbols from database
2025-05-30 23:26:23 [Test worker] INFO  c.i.service.CsvInstrumentService - CSV processing completed - Total rows: 3, Valid: 3, Invalid: 0, Processed: 3
2025-05-30 23:26:24 [Test worker] INFO  c.i.service.CsvInstrumentService - Starting CSV processing - file: instruments.csv, dryRun: false, maxInstruments: 1000, skipDuplicates: true, validateData: true
2025-05-30 23:26:24 [Test worker] INFO  c.i.service.CsvInstrumentService - Loaded 0 existing symbols from database
2025-05-30 23:26:24 [Test worker] INFO  c.i.service.CsvInstrumentService - CSV processing completed - Total rows: 2, Valid: 2, Invalid: 0, Processed: 2
2025-05-30 23:26:24 [Test worker] INFO  c.i.service.CsvInstrumentService - Starting CSV processing - file: instruments.csv, dryRun: true, maxInstruments: 1000, skipDuplicates: true, validateData: true
2025-05-30 23:26:24 [Test worker] INFO  c.i.service.CsvInstrumentService - Loaded 2 existing symbols from database
2025-05-30 23:26:24 [Test worker] INFO  c.i.service.CsvInstrumentService - CSV processing completed - Total rows: 3, Valid: 3, Invalid: 0, Processed: 1
2025-05-30 23:26:24 [Test worker] INFO  c.i.service.CsvInstrumentService - Starting CSV processing - file: instruments.csv, dryRun: true, maxInstruments: 1000, skipDuplicates: true, validateData: true
2025-05-30 23:26:24 [Test worker] INFO  c.i.service.CsvInstrumentService - Loaded 0 existing symbols from database
2025-05-30 23:26:24 [Test worker] INFO  c.i.service.CsvInstrumentService - CSV processing completed - Total rows: 5, Valid: 5, Invalid: 0, Processed: 5
2025-05-30 23:26:24 [Test worker] INFO  c.i.service.CsvInstrumentService - Starting CSV processing - file: instruments.csv, dryRun: true, maxInstruments: 1000, skipDuplicates: true, validateData: true
2025-05-30 23:26:24 [Test worker] ERROR c.i.service.CsvInstrumentService - Unexpected error during CSV processing
java.lang.NullPointerException: Cannot invoke "java.util.Collection.size()" because "c" is null
	at java.base/java.util.HashSet.<init>(HashSet.java:120)
	at com.investment.service.CsvInstrumentService.processCsvFile(CsvInstrumentService.java:84)
	at com.investment.service.CsvInstrumentService$processCsvFile.call(Unknown Source)
	at org.codehaus.groovy.runtime.callsite.CallSiteArray.defaultCall(CallSiteArray.java:47)
	at org.codehaus.groovy.runtime.callsite.AbstractCallSite.call(AbstractCallSite.java:125)
	at com.investment.service.CsvInstrumentServiceSpec.$spock_feature_0_4(CsvInstrumentServiceSpec.groovy:128)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.spockframework.util.ReflectionUtil.invokeMethod(ReflectionUtil.java:187)
	at org.spockframework.runtime.model.MethodInfo.lambda$new$0(MethodInfo.java:49)
	at org.spockframework.runtime.model.MethodInfo.invoke(MethodInfo.java:156)
	at org.spockframework.runtime.extension.MethodInvocation.proceed(MethodInvocation.java:102)
	at org.spockframework.junit4.ExceptionAdapterInterceptor.intercept(ExceptionAdapterInterceptor.java:13)
	at org.spockframework.runtime.extension.MethodInvocation.proceed(MethodInvocation.java:101)
	at org.spockframework.runtime.PlatformSpecRunner.invoke(PlatformSpecRunner.java:398)
	at org.spockframework.runtime.PlatformSpecRunner.runFeatureMethod(PlatformSpecRunner.java:324)
	at org.spockframework.runtime.IterationNode.execute(IterationNode.java:50)
	at org.spockframework.runtime.SimpleFeatureNode.execute(SimpleFeatureNode.java:58)
	at org.spockframework.runtime.SimpleFeatureNode.execute(SimpleFeatureNode.java:15)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$6(NodeTestTask.java:151)
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$8(NodeTestTask.java:141)
	at org.spockframework.runtime.SpockNode.sneakyInvoke(SpockNode.java:40)
	at org.spockframework.runtime.IterationNode.lambda$around$0(IterationNode.java:67)
	at org.spockframework.runtime.PlatformSpecRunner.lambda$createMethodInfoForDoRunIteration$5(PlatformSpecRunner.java:236)
	at org.spockframework.runtime.model.MethodInfo.invoke(MethodInfo.java:156)
	at org.spockframework.runtime.extension.MethodInvocation.proceed(MethodInvocation.java:102)
	at org.spockframework.junit4.ExceptionAdapterInterceptor.intercept(ExceptionAdapterInterceptor.java:13)
	at org.spockframework.runtime.extension.MethodInvocation.proceed(MethodInvocation.java:101)
	at org.spockframework.runtime.PlatformSpecRunner.invoke(PlatformSpecRunner.java:398)
	at org.spockframework.runtime.PlatformSpecRunner.runIteration(PlatformSpecRunner.java:218)
	at org.spockframework.runtime.IterationNode.around(IterationNode.java:67)
	at org.spockframework.runtime.SimpleFeatureNode.lambda$around$0(SimpleFeatureNode.java:52)
	at org.spockframework.runtime.SpockNode.sneakyInvoke(SpockNode.java:40)
	at org.spockframework.runtime.FeatureNode.lambda$around$0(FeatureNode.java:41)
	at org.spockframework.runtime.PlatformSpecRunner.lambda$createMethodInfoForDoRunFeature$4(PlatformSpecRunner.java:199)
	at org.spockframework.runtime.model.MethodInfo.invoke(MethodInfo.java:156)
	at org.spockframework.runtime.PlatformSpecRunner.invokeRaw(PlatformSpecRunner.java:407)
	at org.spockframework.runtime.PlatformSpecRunner.invoke(PlatformSpecRunner.java:390)
	at org.spockframework.runtime.PlatformSpecRunner.runFeature(PlatformSpecRunner.java:192)
	at org.spockframework.runtime.FeatureNode.around(FeatureNode.java:41)
	at org.spockframework.runtime.SimpleFeatureNode.around(SimpleFeatureNode.java:52)
	at org.spockframework.runtime.SimpleFeatureNode.around(SimpleFeatureNode.java:15)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$9(NodeTestTask.java:139)
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.executeRecursively(NodeTestTask.java:138)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.execute(NodeTestTask.java:95)
	at java.base/java.util.ArrayList.forEach(ArrayList.java:1596)
	at org.junit.platform.engine.support.hierarchical.SameThreadHierarchicalTestExecutorService.invokeAll(SameThreadHierarchicalTestExecutorService.java:41)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$6(NodeTestTask.java:155)
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$8(NodeTestTask.java:141)
	at org.spockframework.runtime.SpockNode.sneakyInvoke(SpockNode.java:40)
	at org.spockframework.runtime.SpecNode.lambda$around$0(SpecNode.java:63)
	at org.spockframework.runtime.PlatformSpecRunner.lambda$createMethodInfoForDoRunSpec$0(PlatformSpecRunner.java:61)
	at org.spockframework.runtime.model.MethodInfo.invoke(MethodInfo.java:156)
	at org.spockframework.runtime.PlatformSpecRunner.invokeRaw(PlatformSpecRunner.java:407)
	at org.spockframework.runtime.PlatformSpecRunner.invoke(PlatformSpecRunner.java:390)
	at org.spockframework.runtime.PlatformSpecRunner.runSpec(PlatformSpecRunner.java:55)
	at org.spockframework.runtime.SpecNode.around(SpecNode.java:63)
	at org.spockframework.runtime.SpecNode.around(SpecNode.java:11)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$9(NodeTestTask.java:139)
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.executeRecursively(NodeTestTask.java:138)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.execute(NodeTestTask.java:95)
	at java.base/java.util.ArrayList.forEach(ArrayList.java:1596)
	at org.junit.platform.engine.support.hierarchical.SameThreadHierarchicalTestExecutorService.invokeAll(SameThreadHierarchicalTestExecutorService.java:41)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$6(NodeTestTask.java:155)
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$8(NodeTestTask.java:141)
	at org.junit.platform.engine.support.hierarchical.Node.around(Node.java:137)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$9(NodeTestTask.java:139)
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.executeRecursively(NodeTestTask.java:138)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.execute(NodeTestTask.java:95)
	at org.junit.platform.engine.support.hierarchical.SameThreadHierarchicalTestExecutorService.submit(SameThreadHierarchicalTestExecutorService.java:35)
	at org.junit.platform.engine.support.hierarchical.HierarchicalTestExecutor.execute(HierarchicalTestExecutor.java:57)
	at org.junit.platform.engine.support.hierarchical.HierarchicalTestEngine.execute(HierarchicalTestEngine.java:54)
	at org.junit.platform.launcher.core.EngineExecutionOrchestrator.execute(EngineExecutionOrchestrator.java:198)
	at org.junit.platform.launcher.core.EngineExecutionOrchestrator.execute(EngineExecutionOrchestrator.java:169)
	at org.junit.platform.launcher.core.EngineExecutionOrchestrator.execute(EngineExecutionOrchestrator.java:93)
	at org.junit.platform.launcher.core.EngineExecutionOrchestrator.lambda$execute$0(EngineExecutionOrchestrator.java:58)
	at org.junit.platform.launcher.core.EngineExecutionOrchestrator.withInterceptedStreams(EngineExecutionOrchestrator.java:141)
	at org.junit.platform.launcher.core.EngineExecutionOrchestrator.execute(EngineExecutionOrchestrator.java:57)
	at org.junit.platform.launcher.core.DefaultLauncher.execute(DefaultLauncher.java:103)
	at org.junit.platform.launcher.core.DefaultLauncher.execute(DefaultLauncher.java:85)
	at org.junit.platform.launcher.core.DelegatingLauncher.execute(DelegatingLauncher.java:47)
	at org.gradle.api.internal.tasks.testing.junitplatform.JUnitPlatformTestClassProcessor$CollectAllTestClassesExecutor.processAllTestClasses(JUnitPlatformTestClassProcessor.java:124)
	at org.gradle.api.internal.tasks.testing.junitplatform.JUnitPlatformTestClassProcessor$CollectAllTestClassesExecutor.access$000(JUnitPlatformTestClassProcessor.java:99)
	at org.gradle.api.internal.tasks.testing.junitplatform.JUnitPlatformTestClassProcessor.stop(JUnitPlatformTestClassProcessor.java:94)
	at org.gradle.api.internal.tasks.testing.SuiteTestClassProcessor.stop(SuiteTestClassProcessor.java:63)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.gradle.internal.dispatch.ReflectionDispatch.dispatch(ReflectionDispatch.java:36)
	at org.gradle.internal.dispatch.ReflectionDispatch.dispatch(ReflectionDispatch.java:24)
	at org.gradle.internal.dispatch.ContextClassLoaderDispatch.dispatch(ContextClassLoaderDispatch.java:33)
	at org.gradle.internal.dispatch.ProxyDispatchAdapter$DispatchingInvocationHandler.invoke(ProxyDispatchAdapter.java:92)
	at jdk.proxy1/jdk.proxy1.$Proxy4.stop(Unknown Source)
	at org.gradle.api.internal.tasks.testing.worker.TestWorker$3.run(TestWorker.java:200)
	at org.gradle.api.internal.tasks.testing.worker.TestWorker.executeAndMaintainThreadName(TestWorker.java:132)
	at org.gradle.api.internal.tasks.testing.worker.TestWorker.execute(TestWorker.java:103)
	at org.gradle.api.internal.tasks.testing.worker.TestWorker.execute(TestWorker.java:63)
	at org.gradle.process.internal.worker.child.ActionExecutionWorker.execute(ActionExecutionWorker.java:56)
	at org.gradle.process.internal.worker.child.SystemApplicationClassLoaderWorker.call(SystemApplicationClassLoaderWorker.java:121)
	at org.gradle.process.internal.worker.child.SystemApplicationClassLoaderWorker.call(SystemApplicationClassLoaderWorker.java:71)
	at worker.org.gradle.process.internal.worker.GradleWorkerMain.run(GradleWorkerMain.java:69)
	at worker.org.gradle.process.internal.worker.GradleWorkerMain.main(GradleWorkerMain.java:74)
2025-05-30 23:26:24 [Test worker] INFO  c.i.service.CsvInstrumentService - Starting CSV processing - file: empty.csv, dryRun: true, maxInstruments: 1000, skipDuplicates: true, validateData: true
2025-05-30 23:26:24 [Test worker] INFO  c.i.service.CsvInstrumentService - Starting CSV processing - file: document.txt, dryRun: true, maxInstruments: 1000, skipDuplicates: true, validateData: true
2025-05-30 23:26:24 [Test worker] INFO  c.i.service.CsvInstrumentService - Starting CSV processing - file: instruments.csv, dryRun: true, maxInstruments: 3, skipDuplicates: true, validateData: true
2025-05-30 23:26:24 [Test worker] INFO  c.i.service.CsvInstrumentService - Loaded 0 existing symbols from database
2025-05-30 23:26:24 [Test worker] INFO  c.i.service.CsvInstrumentService - Reached maximum instruments limit: 3
2025-05-30 23:26:24 [Test worker] INFO  c.i.service.CsvInstrumentService - CSV processing completed - Total rows: 4, Valid: 3, Invalid: 0, Processed: 3
2025-05-30 23:26:24 [Test worker] INFO  c.i.service.CsvInstrumentService - Starting CSV processing - file: instruments.csv, dryRun: true, maxInstruments: 1000, skipDuplicates: true, validateData: true
2025-05-30 23:26:24 [Test worker] INFO  c.i.service.CsvInstrumentService - Loaded 0 existing symbols from database
2025-05-30 23:26:24 [Test worker] INFO  c.i.service.CsvInstrumentService - CSV processing completed - Total rows: 3, Valid: 1, Invalid: 2, Processed: 1
2025-05-30 23:26:24 [Test worker] INFO  c.i.service.CsvInstrumentService - Starting CSV processing - file: instruments.csv, dryRun: true, maxInstruments: 1000, skipDuplicates: true, validateData: true
2025-05-30 23:26:24 [Test worker] INFO  c.i.service.CsvInstrumentService - Loaded 0 existing symbols from database
2025-05-30 23:26:24 [Test worker] INFO  c.i.service.CsvInstrumentService - CSV processing completed - Total rows: 4, Valid: 4, Invalid: 0, Processed: 4
2025-05-30 23:27:00 [Test worker] INFO  c.i.service.CsvInstrumentService - Starting CSV processing - file: instruments.csv, dryRun: true, maxInstruments: 1000, skipDuplicates: true, validateData: true
2025-05-30 23:27:00 [Test worker] INFO  c.i.service.CsvInstrumentService - Loaded 0 existing symbols from database
2025-05-30 23:27:00 [Test worker] INFO  c.i.service.CsvInstrumentService - CSV processing completed - Total rows: 3, Valid: 3, Invalid: 0, Processed: 3
2025-05-30 23:27:00 [Test worker] INFO  c.i.service.CsvInstrumentService - Starting CSV processing - file: instruments.csv, dryRun: false, maxInstruments: 1000, skipDuplicates: true, validateData: true
2025-05-30 23:27:00 [Test worker] INFO  c.i.service.CsvInstrumentService - Loaded 0 existing symbols from database
2025-05-30 23:27:00 [Test worker] INFO  c.i.service.CsvInstrumentService - CSV processing completed - Total rows: 2, Valid: 2, Invalid: 0, Processed: 2
2025-05-30 23:27:00 [Test worker] INFO  c.i.service.CsvInstrumentService - Starting CSV processing - file: instruments.csv, dryRun: true, maxInstruments: 1000, skipDuplicates: true, validateData: true
2025-05-30 23:27:00 [Test worker] INFO  c.i.service.CsvInstrumentService - Loaded 2 existing symbols from database
2025-05-30 23:27:00 [Test worker] INFO  c.i.service.CsvInstrumentService - CSV processing completed - Total rows: 3, Valid: 3, Invalid: 0, Processed: 1
2025-05-30 23:27:00 [Test worker] INFO  c.i.service.CsvInstrumentService - Starting CSV processing - file: instruments.csv, dryRun: true, maxInstruments: 1000, skipDuplicates: true, validateData: true
2025-05-30 23:27:00 [Test worker] INFO  c.i.service.CsvInstrumentService - Loaded 0 existing symbols from database
2025-05-30 23:27:00 [Test worker] INFO  c.i.service.CsvInstrumentService - CSV processing completed - Total rows: 5, Valid: 5, Invalid: 0, Processed: 5
2025-05-30 23:27:00 [Test worker] INFO  c.i.service.CsvInstrumentService - Starting CSV processing - file: instruments.csv, dryRun: true, maxInstruments: 1000, skipDuplicates: true, validateData: true
2025-05-30 23:27:00 [Test worker] INFO  c.i.service.CsvInstrumentService - Loaded 0 existing symbols from database
2025-05-30 23:27:00 [Test worker] INFO  c.i.service.CsvInstrumentService - Starting CSV processing - file: empty.csv, dryRun: true, maxInstruments: 1000, skipDuplicates: true, validateData: true
2025-05-30 23:27:00 [Test worker] INFO  c.i.service.CsvInstrumentService - Starting CSV processing - file: document.txt, dryRun: true, maxInstruments: 1000, skipDuplicates: true, validateData: true
2025-05-30 23:27:00 [Test worker] INFO  c.i.service.CsvInstrumentService - Starting CSV processing - file: instruments.csv, dryRun: true, maxInstruments: 3, skipDuplicates: true, validateData: true
2025-05-30 23:27:00 [Test worker] INFO  c.i.service.CsvInstrumentService - Loaded 0 existing symbols from database
2025-05-30 23:27:00 [Test worker] INFO  c.i.service.CsvInstrumentService - Reached maximum instruments limit: 3
2025-05-30 23:27:00 [Test worker] INFO  c.i.service.CsvInstrumentService - CSV processing completed - Total rows: 4, Valid: 3, Invalid: 0, Processed: 3
2025-05-30 23:27:00 [Test worker] INFO  c.i.service.CsvInstrumentService - Starting CSV processing - file: instruments.csv, dryRun: true, maxInstruments: 1000, skipDuplicates: true, validateData: true
2025-05-30 23:27:00 [Test worker] INFO  c.i.service.CsvInstrumentService - Loaded 0 existing symbols from database
2025-05-30 23:27:00 [Test worker] INFO  c.i.service.CsvInstrumentService - CSV processing completed - Total rows: 3, Valid: 1, Invalid: 2, Processed: 1
2025-05-30 23:27:01 [Test worker] INFO  c.i.service.CsvInstrumentService - Starting CSV processing - file: instruments.csv, dryRun: true, maxInstruments: 1000, skipDuplicates: true, validateData: true
2025-05-30 23:27:01 [Test worker] INFO  c.i.service.CsvInstrumentService - Loaded 0 existing symbols from database
2025-05-30 23:27:01 [Test worker] INFO  c.i.service.CsvInstrumentService - CSV processing completed - Total rows: 4, Valid: 4, Invalid: 0, Processed: 4
2025-05-30 23:27:25 [Test worker] INFO  c.i.a.c.InstrumentController - Starting symbol validation (dry-run) - forceRefresh: false
2025-05-30 23:27:25 [Test worker] INFO  c.i.a.c.InstrumentController - Symbol validation (dry-run) completed: DRY RUN: Found 2 invalid symbols out of 10 total. Would delete 2 instruments and 15 OHLCV records.
2025-05-30 23:27:25 [Test worker] INFO  c.i.a.c.InstrumentController - Starting symbol validation (dry-run) - forceRefresh: true
2025-05-30 23:27:25 [Test worker] INFO  c.i.a.c.InstrumentController - Symbol validation (dry-run) completed: DRY RUN: Found 0 invalid symbols out of 5 total. Would delete 0 instruments and 0 OHLCV records.
2025-05-30 23:27:25 [Test worker] INFO  c.i.a.c.InstrumentController - Starting symbol validation (dry-run) - forceRefresh: false
2025-05-30 23:27:25 [Test worker] ERROR c.i.a.c.InstrumentController - Error during symbol validation (dry-run)
java.lang.RuntimeException: Service error
	at java.base/jdk.internal.reflect.DirectConstructorHandleAccessor.newInstance(DirectConstructorHandleAccessor.java:62)
	at java.base/java.lang.reflect.Constructor.newInstanceWithCaller(Constructor.java:502)
	at java.base/java.lang.reflect.Constructor.newInstance(Constructor.java:486)
	at org.codehaus.groovy.reflection.CachedConstructor.invoke(CachedConstructor.java:72)
	at org.codehaus.groovy.runtime.callsite.ConstructorSite$ConstructorSiteNoUnwrapNoCoerce.callConstructor(ConstructorSite.java:105)
	at org.codehaus.groovy.runtime.callsite.CallSiteArray.defaultCallConstructor(CallSiteArray.java:59)
	at org.codehaus.groovy.runtime.callsite.AbstractCallSite.callConstructor(AbstractCallSite.java:263)
	at org.codehaus.groovy.runtime.callsite.AbstractCallSite.callConstructor(AbstractCallSite.java:277)
	at com.investment.api.controller.InstrumentControllerSpec$__spock_feature_0_2_closure1.doCall(InstrumentControllerSpec.groovy:66)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.codehaus.groovy.reflection.CachedMethod.invoke(CachedMethod.java:107)
	at groovy.lang.MetaMethod.doMethodInvoke(MetaMethod.java:323)
	at org.codehaus.groovy.runtime.metaclass.ClosureMetaClass.invokeMethod(ClosureMetaClass.java:274)
	at groovy.lang.MetaClassImpl.invokeMethod(MetaClassImpl.java:1030)
	at groovy.lang.Closure.call(Closure.java:427)
	at org.spockframework.runtime.GroovyRuntimeUtil.invokeClosure(GroovyRuntimeUtil.java:200)
	at org.spockframework.mock.response.CodeResponseGenerator.invokeClosure(CodeResponseGenerator.java:54)
	at org.spockframework.mock.response.CodeResponseGenerator.doRespond(CodeResponseGenerator.java:37)
	at org.spockframework.mock.response.SingleResponseGenerator.respond(SingleResponseGenerator.java:32)
	at org.spockframework.mock.response.ResponseGeneratorChain.respond(ResponseGeneratorChain.java:44)
	at org.spockframework.mock.runtime.MockInteraction.accept(MockInteraction.java:73)
	at org.spockframework.mock.runtime.MockInteractionDecorator.accept(MockInteractionDecorator.java:50)
	at org.spockframework.mock.runtime.InteractionScope$1.accept(InteractionScope.java:57)
	at org.spockframework.mock.runtime.MockController.handle(MockController.java:40)
	at org.spockframework.mock.runtime.JavaMockInterceptor.intercept(JavaMockInterceptor.java:86)
	at org.spockframework.mock.runtime.ByteBuddyInterceptorAdapter.interceptNonAbstract(ByteBuddyInterceptorAdapter.java:35)
	at com.investment.service.SymbolValidationService$SpockMock$793107299.validateSymbols(Unknown Source)
	at com.investment.api.controller.InstrumentController.validateSymbolsDryRun(InstrumentController.java:78)
	at com.investment.api.controller.InstrumentController$validateSymbolsDryRun.call(Unknown Source)
	at org.codehaus.groovy.runtime.callsite.CallSiteArray.defaultCall(CallSiteArray.java:47)
	at org.codehaus.groovy.runtime.callsite.AbstractCallSite.call(AbstractCallSite.java:125)
	at org.codehaus.groovy.runtime.callsite.AbstractCallSite.call(AbstractCallSite.java:139)
	at com.investment.api.controller.InstrumentControllerSpec.$spock_feature_0_2(InstrumentControllerSpec.groovy:69)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.spockframework.util.ReflectionUtil.invokeMethod(ReflectionUtil.java:187)
	at org.spockframework.runtime.model.MethodInfo.lambda$new$0(MethodInfo.java:49)
	at org.spockframework.runtime.model.MethodInfo.invoke(MethodInfo.java:156)
	at org.spockframework.runtime.extension.MethodInvocation.proceed(MethodInvocation.java:102)
	at org.spockframework.junit4.ExceptionAdapterInterceptor.intercept(ExceptionAdapterInterceptor.java:13)
	at org.spockframework.runtime.extension.MethodInvocation.proceed(MethodInvocation.java:101)
	at org.spockframework.runtime.PlatformSpecRunner.invoke(PlatformSpecRunner.java:398)
	at org.spockframework.runtime.PlatformSpecRunner.runFeatureMethod(PlatformSpecRunner.java:324)
	at org.spockframework.runtime.IterationNode.execute(IterationNode.java:50)
	at org.spockframework.runtime.SimpleFeatureNode.execute(SimpleFeatureNode.java:58)
	at org.spockframework.runtime.SimpleFeatureNode.execute(SimpleFeatureNode.java:15)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$6(NodeTestTask.java:151)
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$8(NodeTestTask.java:141)
	at org.spockframework.runtime.SpockNode.sneakyInvoke(SpockNode.java:40)
	at org.spockframework.runtime.IterationNode.lambda$around$0(IterationNode.java:67)
	at org.spockframework.runtime.PlatformSpecRunner.lambda$createMethodInfoForDoRunIteration$5(PlatformSpecRunner.java:236)
	at org.spockframework.runtime.model.MethodInfo.invoke(MethodInfo.java:156)
	at org.spockframework.runtime.extension.MethodInvocation.proceed(MethodInvocation.java:102)
	at org.spockframework.junit4.ExceptionAdapterInterceptor.intercept(ExceptionAdapterInterceptor.java:13)
	at org.spockframework.runtime.extension.MethodInvocation.proceed(MethodInvocation.java:101)
	at org.spockframework.runtime.PlatformSpecRunner.invoke(PlatformSpecRunner.java:398)
	at org.spockframework.runtime.PlatformSpecRunner.runIteration(PlatformSpecRunner.java:218)
	at org.spockframework.runtime.IterationNode.around(IterationNode.java:67)
	at org.spockframework.runtime.SimpleFeatureNode.lambda$around$0(SimpleFeatureNode.java:52)
	at org.spockframework.runtime.SpockNode.sneakyInvoke(SpockNode.java:40)
	at org.spockframework.runtime.FeatureNode.lambda$around$0(FeatureNode.java:41)
	at org.spockframework.runtime.PlatformSpecRunner.lambda$createMethodInfoForDoRunFeature$4(PlatformSpecRunner.java:199)
	at org.spockframework.runtime.model.MethodInfo.invoke(MethodInfo.java:156)
	at org.spockframework.runtime.PlatformSpecRunner.invokeRaw(PlatformSpecRunner.java:407)
	at org.spockframework.runtime.PlatformSpecRunner.invoke(PlatformSpecRunner.java:390)
	at org.spockframework.runtime.PlatformSpecRunner.runFeature(PlatformSpecRunner.java:192)
	at org.spockframework.runtime.FeatureNode.around(FeatureNode.java:41)
	at org.spockframework.runtime.SimpleFeatureNode.around(SimpleFeatureNode.java:52)
	at org.spockframework.runtime.SimpleFeatureNode.around(SimpleFeatureNode.java:15)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$9(NodeTestTask.java:139)
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.executeRecursively(NodeTestTask.java:138)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.execute(NodeTestTask.java:95)
	at java.base/java.util.ArrayList.forEach(ArrayList.java:1596)
	at org.junit.platform.engine.support.hierarchical.SameThreadHierarchicalTestExecutorService.invokeAll(SameThreadHierarchicalTestExecutorService.java:41)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$6(NodeTestTask.java:155)
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$8(NodeTestTask.java:141)
	at org.spockframework.runtime.SpockNode.sneakyInvoke(SpockNode.java:40)
	at org.spockframework.runtime.SpecNode.lambda$around$0(SpecNode.java:63)
	at org.spockframework.runtime.PlatformSpecRunner.lambda$createMethodInfoForDoRunSpec$0(PlatformSpecRunner.java:61)
	at org.spockframework.runtime.model.MethodInfo.invoke(MethodInfo.java:156)
	at org.spockframework.runtime.PlatformSpecRunner.invokeRaw(PlatformSpecRunner.java:407)
	at org.spockframework.runtime.PlatformSpecRunner.invoke(PlatformSpecRunner.java:390)
	at org.spockframework.runtime.PlatformSpecRunner.runSpec(PlatformSpecRunner.java:55)
	at org.spockframework.runtime.SpecNode.around(SpecNode.java:63)
	at org.spockframework.runtime.SpecNode.around(SpecNode.java:11)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$9(NodeTestTask.java:139)
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.executeRecursively(NodeTestTask.java:138)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.execute(NodeTestTask.java:95)
	at java.base/java.util.ArrayList.forEach(ArrayList.java:1596)
	at org.junit.platform.engine.support.hierarchical.SameThreadHierarchicalTestExecutorService.invokeAll(SameThreadHierarchicalTestExecutorService.java:41)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$6(NodeTestTask.java:155)
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$8(NodeTestTask.java:141)
	at org.junit.platform.engine.support.hierarchical.Node.around(Node.java:137)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$9(NodeTestTask.java:139)
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.executeRecursively(NodeTestTask.java:138)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.execute(NodeTestTask.java:95)
	at org.junit.platform.engine.support.hierarchical.SameThreadHierarchicalTestExecutorService.submit(SameThreadHierarchicalTestExecutorService.java:35)
	at org.junit.platform.engine.support.hierarchical.HierarchicalTestExecutor.execute(HierarchicalTestExecutor.java:57)
	at org.junit.platform.engine.support.hierarchical.HierarchicalTestEngine.execute(HierarchicalTestEngine.java:54)
	at org.junit.platform.launcher.core.EngineExecutionOrchestrator.execute(EngineExecutionOrchestrator.java:198)
	at org.junit.platform.launcher.core.EngineExecutionOrchestrator.execute(EngineExecutionOrchestrator.java:169)
	at org.junit.platform.launcher.core.EngineExecutionOrchestrator.execute(EngineExecutionOrchestrator.java:93)
	at org.junit.platform.launcher.core.EngineExecutionOrchestrator.lambda$execute$0(EngineExecutionOrchestrator.java:58)
	at org.junit.platform.launcher.core.EngineExecutionOrchestrator.withInterceptedStreams(EngineExecutionOrchestrator.java:141)
	at org.junit.platform.launcher.core.EngineExecutionOrchestrator.execute(EngineExecutionOrchestrator.java:57)
	at org.junit.platform.launcher.core.DefaultLauncher.execute(DefaultLauncher.java:103)
	at org.junit.platform.launcher.core.DefaultLauncher.execute(DefaultLauncher.java:85)
	at org.junit.platform.launcher.core.DelegatingLauncher.execute(DelegatingLauncher.java:47)
	at org.gradle.api.internal.tasks.testing.junitplatform.JUnitPlatformTestClassProcessor$CollectAllTestClassesExecutor.processAllTestClasses(JUnitPlatformTestClassProcessor.java:124)
	at org.gradle.api.internal.tasks.testing.junitplatform.JUnitPlatformTestClassProcessor$CollectAllTestClassesExecutor.access$000(JUnitPlatformTestClassProcessor.java:99)
	at org.gradle.api.internal.tasks.testing.junitplatform.JUnitPlatformTestClassProcessor.stop(JUnitPlatformTestClassProcessor.java:94)
	at org.gradle.api.internal.tasks.testing.SuiteTestClassProcessor.stop(SuiteTestClassProcessor.java:63)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.gradle.internal.dispatch.ReflectionDispatch.dispatch(ReflectionDispatch.java:36)
	at org.gradle.internal.dispatch.ReflectionDispatch.dispatch(ReflectionDispatch.java:24)
	at org.gradle.internal.dispatch.ContextClassLoaderDispatch.dispatch(ContextClassLoaderDispatch.java:33)
	at org.gradle.internal.dispatch.ProxyDispatchAdapter$DispatchingInvocationHandler.invoke(ProxyDispatchAdapter.java:92)
	at jdk.proxy1/jdk.proxy1.$Proxy4.stop(Unknown Source)
	at org.gradle.api.internal.tasks.testing.worker.TestWorker$3.run(TestWorker.java:200)
	at org.gradle.api.internal.tasks.testing.worker.TestWorker.executeAndMaintainThreadName(TestWorker.java:132)
	at org.gradle.api.internal.tasks.testing.worker.TestWorker.execute(TestWorker.java:103)
	at org.gradle.api.internal.tasks.testing.worker.TestWorker.execute(TestWorker.java:63)
	at org.gradle.process.internal.worker.child.ActionExecutionWorker.execute(ActionExecutionWorker.java:56)
	at org.gradle.process.internal.worker.child.SystemApplicationClassLoaderWorker.call(SystemApplicationClassLoaderWorker.java:121)
	at org.gradle.process.internal.worker.child.SystemApplicationClassLoaderWorker.call(SystemApplicationClassLoaderWorker.java:71)
	at worker.org.gradle.process.internal.worker.GradleWorkerMain.run(GradleWorkerMain.java:69)
	at worker.org.gradle.process.internal.worker.GradleWorkerMain.main(GradleWorkerMain.java:74)
2025-05-30 23:27:25 [Test worker] INFO  c.i.a.c.InstrumentController - Starting symbol validation - dryRun: false, forceRefresh: false
2025-05-30 23:27:25 [Test worker] WARN  c.i.a.c.InstrumentController - PERFORMING ACTUAL CLEANUP - This will permanently delete invalid symbols and their data
2025-05-30 23:27:25 [Test worker] INFO  c.i.a.c.InstrumentController - Symbol cleanup completed: CLEANUP COMPLETED: Deleted 2 invalid symbols out of 10 total. Removed 2 instruments and 15 OHLCV records.
2025-05-30 23:27:25 [Test worker] INFO  c.i.a.c.InstrumentController - Starting symbol validation - dryRun: true, forceRefresh: false
2025-05-30 23:27:25 [Test worker] INFO  c.i.a.c.InstrumentController - Symbol validation (dry-run) completed: DRY RUN: Found 2 invalid symbols out of 10 total. Would delete 2 instruments and 15 OHLCV records.
2025-05-30 23:27:25 [Test worker] ERROR c.i.a.c.InstrumentController - Error retrieving SEC cache status
java.lang.RuntimeException: Cache error
	at java.base/jdk.internal.reflect.DirectConstructorHandleAccessor.newInstance(DirectConstructorHandleAccessor.java:62)
	at java.base/java.lang.reflect.Constructor.newInstanceWithCaller(Constructor.java:502)
	at java.base/java.lang.reflect.Constructor.newInstance(Constructor.java:486)
	at org.codehaus.groovy.reflection.CachedConstructor.invoke(CachedConstructor.java:72)
	at org.codehaus.groovy.runtime.callsite.ConstructorSite$ConstructorSiteNoUnwrapNoCoerce.callConstructor(ConstructorSite.java:105)
	at org.codehaus.groovy.runtime.callsite.CallSiteArray.defaultCallConstructor(CallSiteArray.java:59)
	at org.codehaus.groovy.runtime.callsite.AbstractCallSite.callConstructor(AbstractCallSite.java:263)
	at org.codehaus.groovy.runtime.callsite.AbstractCallSite.callConstructor(AbstractCallSite.java:277)
	at com.investment.api.controller.InstrumentControllerSpec$__spock_feature_0_7_closure2.doCall(InstrumentControllerSpec.groovy:141)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.codehaus.groovy.reflection.CachedMethod.invoke(CachedMethod.java:107)
	at groovy.lang.MetaMethod.doMethodInvoke(MetaMethod.java:323)
	at org.codehaus.groovy.runtime.metaclass.ClosureMetaClass.invokeMethod(ClosureMetaClass.java:274)
	at groovy.lang.MetaClassImpl.invokeMethod(MetaClassImpl.java:1030)
	at groovy.lang.Closure.call(Closure.java:427)
	at org.spockframework.runtime.GroovyRuntimeUtil.invokeClosure(GroovyRuntimeUtil.java:200)
	at org.spockframework.mock.response.CodeResponseGenerator.invokeClosure(CodeResponseGenerator.java:54)
	at org.spockframework.mock.response.CodeResponseGenerator.doRespond(CodeResponseGenerator.java:37)
	at org.spockframework.mock.response.SingleResponseGenerator.respond(SingleResponseGenerator.java:32)
	at org.spockframework.mock.response.ResponseGeneratorChain.respond(ResponseGeneratorChain.java:44)
	at org.spockframework.mock.runtime.MockInteraction.accept(MockInteraction.java:73)
	at org.spockframework.mock.runtime.MockInteractionDecorator.accept(MockInteractionDecorator.java:50)
	at org.spockframework.mock.runtime.InteractionScope$1.accept(InteractionScope.java:57)
	at org.spockframework.mock.runtime.MockController.handle(MockController.java:40)
	at org.spockframework.mock.runtime.JavaMockInterceptor.intercept(JavaMockInterceptor.java:86)
	at org.spockframework.mock.runtime.ByteBuddyInterceptorAdapter.interceptNonAbstract(ByteBuddyInterceptorAdapter.java:35)
	at com.investment.service.SymbolValidationService$SpockMock$793107299.getCacheStatus(Unknown Source)
	at com.investment.api.controller.InstrumentController.getSecCacheStatus(InstrumentController.java:175)
	at com.investment.api.controller.InstrumentController$getSecCacheStatus$1.call(Unknown Source)
	at org.codehaus.groovy.runtime.callsite.CallSiteArray.defaultCall(CallSiteArray.java:47)
	at org.codehaus.groovy.runtime.callsite.AbstractCallSite.call(AbstractCallSite.java:125)
	at org.codehaus.groovy.runtime.callsite.AbstractCallSite.call(AbstractCallSite.java:130)
	at com.investment.api.controller.InstrumentControllerSpec.$spock_feature_0_7(InstrumentControllerSpec.groovy:144)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.spockframework.util.ReflectionUtil.invokeMethod(ReflectionUtil.java:187)
	at org.spockframework.runtime.model.MethodInfo.lambda$new$0(MethodInfo.java:49)
	at org.spockframework.runtime.model.MethodInfo.invoke(MethodInfo.java:156)
	at org.spockframework.runtime.extension.MethodInvocation.proceed(MethodInvocation.java:102)
	at org.spockframework.junit4.ExceptionAdapterInterceptor.intercept(ExceptionAdapterInterceptor.java:13)
	at org.spockframework.runtime.extension.MethodInvocation.proceed(MethodInvocation.java:101)
	at org.spockframework.runtime.PlatformSpecRunner.invoke(PlatformSpecRunner.java:398)
	at org.spockframework.runtime.PlatformSpecRunner.runFeatureMethod(PlatformSpecRunner.java:324)
	at org.spockframework.runtime.IterationNode.execute(IterationNode.java:50)
	at org.spockframework.runtime.SimpleFeatureNode.execute(SimpleFeatureNode.java:58)
	at org.spockframework.runtime.SimpleFeatureNode.execute(SimpleFeatureNode.java:15)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$6(NodeTestTask.java:151)
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$8(NodeTestTask.java:141)
	at org.spockframework.runtime.SpockNode.sneakyInvoke(SpockNode.java:40)
	at org.spockframework.runtime.IterationNode.lambda$around$0(IterationNode.java:67)
	at org.spockframework.runtime.PlatformSpecRunner.lambda$createMethodInfoForDoRunIteration$5(PlatformSpecRunner.java:236)
	at org.spockframework.runtime.model.MethodInfo.invoke(MethodInfo.java:156)
	at org.spockframework.runtime.extension.MethodInvocation.proceed(MethodInvocation.java:102)
	at org.spockframework.junit4.ExceptionAdapterInterceptor.intercept(ExceptionAdapterInterceptor.java:13)
	at org.spockframework.runtime.extension.MethodInvocation.proceed(MethodInvocation.java:101)
	at org.spockframework.runtime.PlatformSpecRunner.invoke(PlatformSpecRunner.java:398)
	at org.spockframework.runtime.PlatformSpecRunner.runIteration(PlatformSpecRunner.java:218)
	at org.spockframework.runtime.IterationNode.around(IterationNode.java:67)
	at org.spockframework.runtime.SimpleFeatureNode.lambda$around$0(SimpleFeatureNode.java:52)
	at org.spockframework.runtime.SpockNode.sneakyInvoke(SpockNode.java:40)
	at org.spockframework.runtime.FeatureNode.lambda$around$0(FeatureNode.java:41)
	at org.spockframework.runtime.PlatformSpecRunner.lambda$createMethodInfoForDoRunFeature$4(PlatformSpecRunner.java:199)
	at org.spockframework.runtime.model.MethodInfo.invoke(MethodInfo.java:156)
	at org.spockframework.runtime.PlatformSpecRunner.invokeRaw(PlatformSpecRunner.java:407)
	at org.spockframework.runtime.PlatformSpecRunner.invoke(PlatformSpecRunner.java:390)
	at org.spockframework.runtime.PlatformSpecRunner.runFeature(PlatformSpecRunner.java:192)
	at org.spockframework.runtime.FeatureNode.around(FeatureNode.java:41)
	at org.spockframework.runtime.SimpleFeatureNode.around(SimpleFeatureNode.java:52)
	at org.spockframework.runtime.SimpleFeatureNode.around(SimpleFeatureNode.java:15)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$9(NodeTestTask.java:139)
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.executeRecursively(NodeTestTask.java:138)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.execute(NodeTestTask.java:95)
	at java.base/java.util.ArrayList.forEach(ArrayList.java:1596)
	at org.junit.platform.engine.support.hierarchical.SameThreadHierarchicalTestExecutorService.invokeAll(SameThreadHierarchicalTestExecutorService.java:41)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$6(NodeTestTask.java:155)
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$8(NodeTestTask.java:141)
	at org.spockframework.runtime.SpockNode.sneakyInvoke(SpockNode.java:40)
	at org.spockframework.runtime.SpecNode.lambda$around$0(SpecNode.java:63)
	at org.spockframework.runtime.PlatformSpecRunner.lambda$createMethodInfoForDoRunSpec$0(PlatformSpecRunner.java:61)
	at org.spockframework.runtime.model.MethodInfo.invoke(MethodInfo.java:156)
	at org.spockframework.runtime.PlatformSpecRunner.invokeRaw(PlatformSpecRunner.java:407)
	at org.spockframework.runtime.PlatformSpecRunner.invoke(PlatformSpecRunner.java:390)
	at org.spockframework.runtime.PlatformSpecRunner.runSpec(PlatformSpecRunner.java:55)
	at org.spockframework.runtime.SpecNode.around(SpecNode.java:63)
	at org.spockframework.runtime.SpecNode.around(SpecNode.java:11)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$9(NodeTestTask.java:139)
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.executeRecursively(NodeTestTask.java:138)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.execute(NodeTestTask.java:95)
	at java.base/java.util.ArrayList.forEach(ArrayList.java:1596)
	at org.junit.platform.engine.support.hierarchical.SameThreadHierarchicalTestExecutorService.invokeAll(SameThreadHierarchicalTestExecutorService.java:41)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$6(NodeTestTask.java:155)
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$8(NodeTestTask.java:141)
	at org.junit.platform.engine.support.hierarchical.Node.around(Node.java:137)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$9(NodeTestTask.java:139)
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.executeRecursively(NodeTestTask.java:138)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.execute(NodeTestTask.java:95)
	at org.junit.platform.engine.support.hierarchical.SameThreadHierarchicalTestExecutorService.submit(SameThreadHierarchicalTestExecutorService.java:35)
	at org.junit.platform.engine.support.hierarchical.HierarchicalTestExecutor.execute(HierarchicalTestExecutor.java:57)
	at org.junit.platform.engine.support.hierarchical.HierarchicalTestEngine.execute(HierarchicalTestEngine.java:54)
	at org.junit.platform.launcher.core.EngineExecutionOrchestrator.execute(EngineExecutionOrchestrator.java:198)
	at org.junit.platform.launcher.core.EngineExecutionOrchestrator.execute(EngineExecutionOrchestrator.java:169)
	at org.junit.platform.launcher.core.EngineExecutionOrchestrator.execute(EngineExecutionOrchestrator.java:93)
	at org.junit.platform.launcher.core.EngineExecutionOrchestrator.lambda$execute$0(EngineExecutionOrchestrator.java:58)
	at org.junit.platform.launcher.core.EngineExecutionOrchestrator.withInterceptedStreams(EngineExecutionOrchestrator.java:141)
	at org.junit.platform.launcher.core.EngineExecutionOrchestrator.execute(EngineExecutionOrchestrator.java:57)
	at org.junit.platform.launcher.core.DefaultLauncher.execute(DefaultLauncher.java:103)
	at org.junit.platform.launcher.core.DefaultLauncher.execute(DefaultLauncher.java:85)
	at org.junit.platform.launcher.core.DelegatingLauncher.execute(DelegatingLauncher.java:47)
	at org.gradle.api.internal.tasks.testing.junitplatform.JUnitPlatformTestClassProcessor$CollectAllTestClassesExecutor.processAllTestClasses(JUnitPlatformTestClassProcessor.java:124)
	at org.gradle.api.internal.tasks.testing.junitplatform.JUnitPlatformTestClassProcessor$CollectAllTestClassesExecutor.access$000(JUnitPlatformTestClassProcessor.java:99)
	at org.gradle.api.internal.tasks.testing.junitplatform.JUnitPlatformTestClassProcessor.stop(JUnitPlatformTestClassProcessor.java:94)
	at org.gradle.api.internal.tasks.testing.SuiteTestClassProcessor.stop(SuiteTestClassProcessor.java:63)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.gradle.internal.dispatch.ReflectionDispatch.dispatch(ReflectionDispatch.java:36)
	at org.gradle.internal.dispatch.ReflectionDispatch.dispatch(ReflectionDispatch.java:24)
	at org.gradle.internal.dispatch.ContextClassLoaderDispatch.dispatch(ContextClassLoaderDispatch.java:33)
	at org.gradle.internal.dispatch.ProxyDispatchAdapter$DispatchingInvocationHandler.invoke(ProxyDispatchAdapter.java:92)
	at jdk.proxy1/jdk.proxy1.$Proxy4.stop(Unknown Source)
	at org.gradle.api.internal.tasks.testing.worker.TestWorker$3.run(TestWorker.java:200)
	at org.gradle.api.internal.tasks.testing.worker.TestWorker.executeAndMaintainThreadName(TestWorker.java:132)
	at org.gradle.api.internal.tasks.testing.worker.TestWorker.execute(TestWorker.java:103)
	at org.gradle.api.internal.tasks.testing.worker.TestWorker.execute(TestWorker.java:63)
	at org.gradle.process.internal.worker.child.ActionExecutionWorker.execute(ActionExecutionWorker.java:56)
	at org.gradle.process.internal.worker.child.SystemApplicationClassLoaderWorker.call(SystemApplicationClassLoaderWorker.java:121)
	at org.gradle.process.internal.worker.child.SystemApplicationClassLoaderWorker.call(SystemApplicationClassLoaderWorker.java:71)
	at worker.org.gradle.process.internal.worker.GradleWorkerMain.run(GradleWorkerMain.java:69)
	at worker.org.gradle.process.internal.worker.GradleWorkerMain.main(GradleWorkerMain.java:74)
2025-05-30 23:27:25 [Test worker] INFO  c.i.a.c.InstrumentController - Starting SEC synchronization (dry-run) - forceRefresh: false, maxInstruments: 1000
2025-05-30 23:27:25 [Test worker] INFO  c.i.a.c.InstrumentController - SEC synchronization (dry-run) completed: DRY RUN: Found 50 missing symbols out of 5000 SEC symbols. Would add 50 new instruments to database.
2025-05-30 23:27:25 [Test worker] INFO  c.i.a.c.InstrumentController - Starting SEC synchronization (dry-run) - forceRefresh: true, maxInstruments: 500
2025-05-30 23:27:25 [Test worker] INFO  c.i.a.c.InstrumentController - SEC synchronization (dry-run) completed: DRY RUN: Found 25 missing symbols out of 5000 SEC symbols. Would add 25 new instruments to database.
2025-05-30 23:27:25 [Test worker] INFO  c.i.a.c.InstrumentController - Starting SEC synchronization (dry-run) - forceRefresh: false, maxInstruments: 1000
2025-05-30 23:27:25 [Test worker] ERROR c.i.a.c.InstrumentController - Error during SEC synchronization (dry-run)
java.lang.RuntimeException: SEC API error
	at java.base/jdk.internal.reflect.DirectConstructorHandleAccessor.newInstance(DirectConstructorHandleAccessor.java:62)
	at java.base/java.lang.reflect.Constructor.newInstanceWithCaller(Constructor.java:502)
	at java.base/java.lang.reflect.Constructor.newInstance(Constructor.java:486)
	at org.codehaus.groovy.reflection.CachedConstructor.invoke(CachedConstructor.java:72)
	at org.codehaus.groovy.runtime.callsite.ConstructorSite$ConstructorSiteNoUnwrapNoCoerce.callConstructor(ConstructorSite.java:105)
	at org.codehaus.groovy.runtime.callsite.CallSiteArray.defaultCallConstructor(CallSiteArray.java:59)
	at org.codehaus.groovy.runtime.callsite.AbstractCallSite.callConstructor(AbstractCallSite.java:263)
	at org.codehaus.groovy.runtime.callsite.AbstractCallSite.callConstructor(AbstractCallSite.java:277)
	at com.investment.api.controller.InstrumentControllerSpec$__spock_feature_0_10_closure3.doCall(InstrumentControllerSpec.groovy:185)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.codehaus.groovy.reflection.CachedMethod.invoke(CachedMethod.java:107)
	at groovy.lang.MetaMethod.doMethodInvoke(MetaMethod.java:323)
	at org.codehaus.groovy.runtime.metaclass.ClosureMetaClass.invokeMethod(ClosureMetaClass.java:274)
	at groovy.lang.MetaClassImpl.invokeMethod(MetaClassImpl.java:1030)
	at groovy.lang.Closure.call(Closure.java:427)
	at org.spockframework.runtime.GroovyRuntimeUtil.invokeClosure(GroovyRuntimeUtil.java:200)
	at org.spockframework.mock.response.CodeResponseGenerator.invokeClosure(CodeResponseGenerator.java:54)
	at org.spockframework.mock.response.CodeResponseGenerator.doRespond(CodeResponseGenerator.java:37)
	at org.spockframework.mock.response.SingleResponseGenerator.respond(SingleResponseGenerator.java:32)
	at org.spockframework.mock.response.ResponseGeneratorChain.respond(ResponseGeneratorChain.java:44)
	at org.spockframework.mock.runtime.MockInteraction.accept(MockInteraction.java:73)
	at org.spockframework.mock.runtime.MockInteractionDecorator.accept(MockInteractionDecorator.java:50)
	at org.spockframework.mock.runtime.InteractionScope$1.accept(InteractionScope.java:57)
	at org.spockframework.mock.runtime.MockController.handle(MockController.java:40)
	at org.spockframework.mock.runtime.JavaMockInterceptor.intercept(JavaMockInterceptor.java:86)
	at org.spockframework.mock.runtime.ByteBuddyInterceptorAdapter.interceptNonAbstract(ByteBuddyInterceptorAdapter.java:35)
	at com.investment.service.SecSynchronizationService$SpockMock$1909315777.synchronizeInstruments(Unknown Source)
	at com.investment.api.controller.InstrumentController.syncSecDataDryRun(InstrumentController.java:218)
	at com.investment.api.controller.InstrumentController$syncSecDataDryRun$2.call(Unknown Source)
	at org.codehaus.groovy.runtime.callsite.CallSiteArray.defaultCall(CallSiteArray.java:47)
	at org.codehaus.groovy.runtime.callsite.AbstractCallSite.call(AbstractCallSite.java:125)
	at org.codehaus.groovy.runtime.callsite.AbstractCallSite.call(AbstractCallSite.java:148)
	at com.investment.api.controller.InstrumentControllerSpec.$spock_feature_0_10(InstrumentControllerSpec.groovy:188)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.spockframework.util.ReflectionUtil.invokeMethod(ReflectionUtil.java:187)
	at org.spockframework.runtime.model.MethodInfo.lambda$new$0(MethodInfo.java:49)
	at org.spockframework.runtime.model.MethodInfo.invoke(MethodInfo.java:156)
	at org.spockframework.runtime.extension.MethodInvocation.proceed(MethodInvocation.java:102)
	at org.spockframework.junit4.ExceptionAdapterInterceptor.intercept(ExceptionAdapterInterceptor.java:13)
	at org.spockframework.runtime.extension.MethodInvocation.proceed(MethodInvocation.java:101)
	at org.spockframework.runtime.PlatformSpecRunner.invoke(PlatformSpecRunner.java:398)
	at org.spockframework.runtime.PlatformSpecRunner.runFeatureMethod(PlatformSpecRunner.java:324)
	at org.spockframework.runtime.IterationNode.execute(IterationNode.java:50)
	at org.spockframework.runtime.SimpleFeatureNode.execute(SimpleFeatureNode.java:58)
	at org.spockframework.runtime.SimpleFeatureNode.execute(SimpleFeatureNode.java:15)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$6(NodeTestTask.java:151)
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$8(NodeTestTask.java:141)
	at org.spockframework.runtime.SpockNode.sneakyInvoke(SpockNode.java:40)
	at org.spockframework.runtime.IterationNode.lambda$around$0(IterationNode.java:67)
	at org.spockframework.runtime.PlatformSpecRunner.lambda$createMethodInfoForDoRunIteration$5(PlatformSpecRunner.java:236)
	at org.spockframework.runtime.model.MethodInfo.invoke(MethodInfo.java:156)
	at org.spockframework.runtime.extension.MethodInvocation.proceed(MethodInvocation.java:102)
	at org.spockframework.junit4.ExceptionAdapterInterceptor.intercept(ExceptionAdapterInterceptor.java:13)
	at org.spockframework.runtime.extension.MethodInvocation.proceed(MethodInvocation.java:101)
	at org.spockframework.runtime.PlatformSpecRunner.invoke(PlatformSpecRunner.java:398)
	at org.spockframework.runtime.PlatformSpecRunner.runIteration(PlatformSpecRunner.java:218)
	at org.spockframework.runtime.IterationNode.around(IterationNode.java:67)
	at org.spockframework.runtime.SimpleFeatureNode.lambda$around$0(SimpleFeatureNode.java:52)
	at org.spockframework.runtime.SpockNode.sneakyInvoke(SpockNode.java:40)
	at org.spockframework.runtime.FeatureNode.lambda$around$0(FeatureNode.java:41)
	at org.spockframework.runtime.PlatformSpecRunner.lambda$createMethodInfoForDoRunFeature$4(PlatformSpecRunner.java:199)
	at org.spockframework.runtime.model.MethodInfo.invoke(MethodInfo.java:156)
	at org.spockframework.runtime.PlatformSpecRunner.invokeRaw(PlatformSpecRunner.java:407)
	at org.spockframework.runtime.PlatformSpecRunner.invoke(PlatformSpecRunner.java:390)
	at org.spockframework.runtime.PlatformSpecRunner.runFeature(PlatformSpecRunner.java:192)
	at org.spockframework.runtime.FeatureNode.around(FeatureNode.java:41)
	at org.spockframework.runtime.SimpleFeatureNode.around(SimpleFeatureNode.java:52)
	at org.spockframework.runtime.SimpleFeatureNode.around(SimpleFeatureNode.java:15)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$9(NodeTestTask.java:139)
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.executeRecursively(NodeTestTask.java:138)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.execute(NodeTestTask.java:95)
	at java.base/java.util.ArrayList.forEach(ArrayList.java:1596)
	at org.junit.platform.engine.support.hierarchical.SameThreadHierarchicalTestExecutorService.invokeAll(SameThreadHierarchicalTestExecutorService.java:41)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$6(NodeTestTask.java:155)
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$8(NodeTestTask.java:141)
	at org.spockframework.runtime.SpockNode.sneakyInvoke(SpockNode.java:40)
	at org.spockframework.runtime.SpecNode.lambda$around$0(SpecNode.java:63)
	at org.spockframework.runtime.PlatformSpecRunner.lambda$createMethodInfoForDoRunSpec$0(PlatformSpecRunner.java:61)
	at org.spockframework.runtime.model.MethodInfo.invoke(MethodInfo.java:156)
	at org.spockframework.runtime.PlatformSpecRunner.invokeRaw(PlatformSpecRunner.java:407)
	at org.spockframework.runtime.PlatformSpecRunner.invoke(PlatformSpecRunner.java:390)
	at org.spockframework.runtime.PlatformSpecRunner.runSpec(PlatformSpecRunner.java:55)
	at org.spockframework.runtime.SpecNode.around(SpecNode.java:63)
	at org.spockframework.runtime.SpecNode.around(SpecNode.java:11)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$9(NodeTestTask.java:139)
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.executeRecursively(NodeTestTask.java:138)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.execute(NodeTestTask.java:95)
	at java.base/java.util.ArrayList.forEach(ArrayList.java:1596)
	at org.junit.platform.engine.support.hierarchical.SameThreadHierarchicalTestExecutorService.invokeAll(SameThreadHierarchicalTestExecutorService.java:41)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$6(NodeTestTask.java:155)
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$8(NodeTestTask.java:141)
	at org.junit.platform.engine.support.hierarchical.Node.around(Node.java:137)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$9(NodeTestTask.java:139)
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.executeRecursively(NodeTestTask.java:138)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.execute(NodeTestTask.java:95)
	at org.junit.platform.engine.support.hierarchical.SameThreadHierarchicalTestExecutorService.submit(SameThreadHierarchicalTestExecutorService.java:35)
	at org.junit.platform.engine.support.hierarchical.HierarchicalTestExecutor.execute(HierarchicalTestExecutor.java:57)
	at org.junit.platform.engine.support.hierarchical.HierarchicalTestEngine.execute(HierarchicalTestEngine.java:54)
	at org.junit.platform.launcher.core.EngineExecutionOrchestrator.execute(EngineExecutionOrchestrator.java:198)
	at org.junit.platform.launcher.core.EngineExecutionOrchestrator.execute(EngineExecutionOrchestrator.java:169)
	at org.junit.platform.launcher.core.EngineExecutionOrchestrator.execute(EngineExecutionOrchestrator.java:93)
	at org.junit.platform.launcher.core.EngineExecutionOrchestrator.lambda$execute$0(EngineExecutionOrchestrator.java:58)
	at org.junit.platform.launcher.core.EngineExecutionOrchestrator.withInterceptedStreams(EngineExecutionOrchestrator.java:141)
	at org.junit.platform.launcher.core.EngineExecutionOrchestrator.execute(EngineExecutionOrchestrator.java:57)
	at org.junit.platform.launcher.core.DefaultLauncher.execute(DefaultLauncher.java:103)
	at org.junit.platform.launcher.core.DefaultLauncher.execute(DefaultLauncher.java:85)
	at org.junit.platform.launcher.core.DelegatingLauncher.execute(DelegatingLauncher.java:47)
	at org.gradle.api.internal.tasks.testing.junitplatform.JUnitPlatformTestClassProcessor$CollectAllTestClassesExecutor.processAllTestClasses(JUnitPlatformTestClassProcessor.java:124)
	at org.gradle.api.internal.tasks.testing.junitplatform.JUnitPlatformTestClassProcessor$CollectAllTestClassesExecutor.access$000(JUnitPlatformTestClassProcessor.java:99)
	at org.gradle.api.internal.tasks.testing.junitplatform.JUnitPlatformTestClassProcessor.stop(JUnitPlatformTestClassProcessor.java:94)
	at org.gradle.api.internal.tasks.testing.SuiteTestClassProcessor.stop(SuiteTestClassProcessor.java:63)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.gradle.internal.dispatch.ReflectionDispatch.dispatch(ReflectionDispatch.java:36)
	at org.gradle.internal.dispatch.ReflectionDispatch.dispatch(ReflectionDispatch.java:24)
	at org.gradle.internal.dispatch.ContextClassLoaderDispatch.dispatch(ContextClassLoaderDispatch.java:33)
	at org.gradle.internal.dispatch.ProxyDispatchAdapter$DispatchingInvocationHandler.invoke(ProxyDispatchAdapter.java:92)
	at jdk.proxy1/jdk.proxy1.$Proxy4.stop(Unknown Source)
	at org.gradle.api.internal.tasks.testing.worker.TestWorker$3.run(TestWorker.java:200)
	at org.gradle.api.internal.tasks.testing.worker.TestWorker.executeAndMaintainThreadName(TestWorker.java:132)
	at org.gradle.api.internal.tasks.testing.worker.TestWorker.execute(TestWorker.java:103)
	at org.gradle.api.internal.tasks.testing.worker.TestWorker.execute(TestWorker.java:63)
	at org.gradle.process.internal.worker.child.ActionExecutionWorker.execute(ActionExecutionWorker.java:56)
	at org.gradle.process.internal.worker.child.SystemApplicationClassLoaderWorker.call(SystemApplicationClassLoaderWorker.java:121)
	at org.gradle.process.internal.worker.child.SystemApplicationClassLoaderWorker.call(SystemApplicationClassLoaderWorker.java:71)
	at worker.org.gradle.process.internal.worker.GradleWorkerMain.run(GradleWorkerMain.java:69)
	at worker.org.gradle.process.internal.worker.GradleWorkerMain.main(GradleWorkerMain.java:74)
2025-05-30 23:27:25 [Test worker] INFO  c.i.a.c.InstrumentController - Starting SEC synchronization - dryRun: false, forceRefresh: false, maxInstruments: 100
2025-05-30 23:27:25 [Test worker] WARN  c.i.a.c.InstrumentController - PERFORMING ACTUAL SYNCHRONIZATION - This will add new instruments to the database
2025-05-30 23:27:25 [Test worker] INFO  c.i.a.c.InstrumentController - SEC synchronization completed: SYNC COMPLETED: Added 2 new instruments out of 50 missing symbols. Database now has 102 instruments from SEC data.
2025-05-30 23:27:25 [Test worker] INFO  c.i.a.c.InstrumentController - Starting SEC synchronization - dryRun: true, forceRefresh: true, maxInstruments: 200
2025-05-30 23:27:25 [Test worker] INFO  c.i.a.c.InstrumentController - SEC synchronization (dry-run) completed: DRY RUN: Found 30 missing symbols out of 5000 SEC symbols. Would add 30 new instruments to database.
2025-05-30 23:27:25 [Test worker] INFO  c.i.a.c.InstrumentController - Starting SEC synchronization - dryRun: true, forceRefresh: false, maxInstruments: 100
2025-05-30 23:27:25 [Test worker] ERROR c.i.a.c.InstrumentController - Error during SEC synchronization
java.lang.RuntimeException: Database error
	at java.base/jdk.internal.reflect.DirectConstructorHandleAccessor.newInstance(DirectConstructorHandleAccessor.java:62)
	at java.base/java.lang.reflect.Constructor.newInstanceWithCaller(Constructor.java:502)
	at java.base/java.lang.reflect.Constructor.newInstance(Constructor.java:486)
	at org.codehaus.groovy.reflection.CachedConstructor.invoke(CachedConstructor.java:72)
	at org.codehaus.groovy.runtime.callsite.ConstructorSite$ConstructorSiteNoUnwrapNoCoerce.callConstructor(ConstructorSite.java:105)
	at org.codehaus.groovy.runtime.callsite.CallSiteArray.defaultCallConstructor(CallSiteArray.java:59)
	at org.codehaus.groovy.runtime.callsite.AbstractCallSite.callConstructor(AbstractCallSite.java:263)
	at org.codehaus.groovy.runtime.callsite.AbstractCallSite.callConstructor(AbstractCallSite.java:277)
	at com.investment.api.controller.InstrumentControllerSpec$__spock_feature_0_14_closure4.doCall(InstrumentControllerSpec.groovy:245)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.codehaus.groovy.reflection.CachedMethod.invoke(CachedMethod.java:107)
	at groovy.lang.MetaMethod.doMethodInvoke(MetaMethod.java:323)
	at org.codehaus.groovy.runtime.metaclass.ClosureMetaClass.invokeMethod(ClosureMetaClass.java:274)
	at groovy.lang.MetaClassImpl.invokeMethod(MetaClassImpl.java:1030)
	at groovy.lang.Closure.call(Closure.java:427)
	at org.spockframework.runtime.GroovyRuntimeUtil.invokeClosure(GroovyRuntimeUtil.java:200)
	at org.spockframework.mock.response.CodeResponseGenerator.invokeClosure(CodeResponseGenerator.java:54)
	at org.spockframework.mock.response.CodeResponseGenerator.doRespond(CodeResponseGenerator.java:37)
	at org.spockframework.mock.response.SingleResponseGenerator.respond(SingleResponseGenerator.java:32)
	at org.spockframework.mock.response.ResponseGeneratorChain.respond(ResponseGeneratorChain.java:44)
	at org.spockframework.mock.runtime.MockInteraction.accept(MockInteraction.java:73)
	at org.spockframework.mock.runtime.MockInteractionDecorator.accept(MockInteractionDecorator.java:50)
	at org.spockframework.mock.runtime.InteractionScope$1.accept(InteractionScope.java:57)
	at org.spockframework.mock.runtime.MockController.handle(MockController.java:40)
	at org.spockframework.mock.runtime.JavaMockInterceptor.intercept(JavaMockInterceptor.java:86)
	at org.spockframework.mock.runtime.ByteBuddyInterceptorAdapter.interceptNonAbstract(ByteBuddyInterceptorAdapter.java:35)
	at com.investment.service.SecSynchronizationService$SpockMock$1909315777.synchronizeInstruments(Unknown Source)
	at com.investment.api.controller.InstrumentController.syncSecData(InstrumentController.java:276)
	at com.investment.api.controller.InstrumentController$syncSecData$3.call(Unknown Source)
	at org.codehaus.groovy.runtime.callsite.CallSiteArray.defaultCall(CallSiteArray.java:47)
	at org.codehaus.groovy.runtime.callsite.AbstractCallSite.call(AbstractCallSite.java:125)
	at org.codehaus.groovy.runtime.callsite.AbstractCallSite.call(AbstractCallSite.java:139)
	at com.investment.api.controller.InstrumentControllerSpec.$spock_feature_0_14(InstrumentControllerSpec.groovy:248)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.spockframework.util.ReflectionUtil.invokeMethod(ReflectionUtil.java:187)
	at org.spockframework.runtime.model.MethodInfo.lambda$new$0(MethodInfo.java:49)
	at org.spockframework.runtime.model.MethodInfo.invoke(MethodInfo.java:156)
	at org.spockframework.runtime.extension.MethodInvocation.proceed(MethodInvocation.java:102)
	at org.spockframework.junit4.ExceptionAdapterInterceptor.intercept(ExceptionAdapterInterceptor.java:13)
	at org.spockframework.runtime.extension.MethodInvocation.proceed(MethodInvocation.java:101)
	at org.spockframework.runtime.PlatformSpecRunner.invoke(PlatformSpecRunner.java:398)
	at org.spockframework.runtime.PlatformSpecRunner.runFeatureMethod(PlatformSpecRunner.java:324)
	at org.spockframework.runtime.IterationNode.execute(IterationNode.java:50)
	at org.spockframework.runtime.SimpleFeatureNode.execute(SimpleFeatureNode.java:58)
	at org.spockframework.runtime.SimpleFeatureNode.execute(SimpleFeatureNode.java:15)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$6(NodeTestTask.java:151)
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$8(NodeTestTask.java:141)
	at org.spockframework.runtime.SpockNode.sneakyInvoke(SpockNode.java:40)
	at org.spockframework.runtime.IterationNode.lambda$around$0(IterationNode.java:67)
	at org.spockframework.runtime.PlatformSpecRunner.lambda$createMethodInfoForDoRunIteration$5(PlatformSpecRunner.java:236)
	at org.spockframework.runtime.model.MethodInfo.invoke(MethodInfo.java:156)
	at org.spockframework.runtime.extension.MethodInvocation.proceed(MethodInvocation.java:102)
	at org.spockframework.junit4.ExceptionAdapterInterceptor.intercept(ExceptionAdapterInterceptor.java:13)
	at org.spockframework.runtime.extension.MethodInvocation.proceed(MethodInvocation.java:101)
	at org.spockframework.runtime.PlatformSpecRunner.invoke(PlatformSpecRunner.java:398)
	at org.spockframework.runtime.PlatformSpecRunner.runIteration(PlatformSpecRunner.java:218)
	at org.spockframework.runtime.IterationNode.around(IterationNode.java:67)
	at org.spockframework.runtime.SimpleFeatureNode.lambda$around$0(SimpleFeatureNode.java:52)
	at org.spockframework.runtime.SpockNode.sneakyInvoke(SpockNode.java:40)
	at org.spockframework.runtime.FeatureNode.lambda$around$0(FeatureNode.java:41)
	at org.spockframework.runtime.PlatformSpecRunner.lambda$createMethodInfoForDoRunFeature$4(PlatformSpecRunner.java:199)
	at org.spockframework.runtime.model.MethodInfo.invoke(MethodInfo.java:156)
	at org.spockframework.runtime.PlatformSpecRunner.invokeRaw(PlatformSpecRunner.java:407)
	at org.spockframework.runtime.PlatformSpecRunner.invoke(PlatformSpecRunner.java:390)
	at org.spockframework.runtime.PlatformSpecRunner.runFeature(PlatformSpecRunner.java:192)
	at org.spockframework.runtime.FeatureNode.around(FeatureNode.java:41)
	at org.spockframework.runtime.SimpleFeatureNode.around(SimpleFeatureNode.java:52)
	at org.spockframework.runtime.SimpleFeatureNode.around(SimpleFeatureNode.java:15)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$9(NodeTestTask.java:139)
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.executeRecursively(NodeTestTask.java:138)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.execute(NodeTestTask.java:95)
	at java.base/java.util.ArrayList.forEach(ArrayList.java:1596)
	at org.junit.platform.engine.support.hierarchical.SameThreadHierarchicalTestExecutorService.invokeAll(SameThreadHierarchicalTestExecutorService.java:41)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$6(NodeTestTask.java:155)
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$8(NodeTestTask.java:141)
	at org.spockframework.runtime.SpockNode.sneakyInvoke(SpockNode.java:40)
	at org.spockframework.runtime.SpecNode.lambda$around$0(SpecNode.java:63)
	at org.spockframework.runtime.PlatformSpecRunner.lambda$createMethodInfoForDoRunSpec$0(PlatformSpecRunner.java:61)
	at org.spockframework.runtime.model.MethodInfo.invoke(MethodInfo.java:156)
	at org.spockframework.runtime.PlatformSpecRunner.invokeRaw(PlatformSpecRunner.java:407)
	at org.spockframework.runtime.PlatformSpecRunner.invoke(PlatformSpecRunner.java:390)
	at org.spockframework.runtime.PlatformSpecRunner.runSpec(PlatformSpecRunner.java:55)
	at org.spockframework.runtime.SpecNode.around(SpecNode.java:63)
	at org.spockframework.runtime.SpecNode.around(SpecNode.java:11)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$9(NodeTestTask.java:139)
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.executeRecursively(NodeTestTask.java:138)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.execute(NodeTestTask.java:95)
	at java.base/java.util.ArrayList.forEach(ArrayList.java:1596)
	at org.junit.platform.engine.support.hierarchical.SameThreadHierarchicalTestExecutorService.invokeAll(SameThreadHierarchicalTestExecutorService.java:41)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$6(NodeTestTask.java:155)
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$8(NodeTestTask.java:141)
	at org.junit.platform.engine.support.hierarchical.Node.around(Node.java:137)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$9(NodeTestTask.java:139)
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.executeRecursively(NodeTestTask.java:138)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.execute(NodeTestTask.java:95)
	at org.junit.platform.engine.support.hierarchical.SameThreadHierarchicalTestExecutorService.submit(SameThreadHierarchicalTestExecutorService.java:35)
	at org.junit.platform.engine.support.hierarchical.HierarchicalTestExecutor.execute(HierarchicalTestExecutor.java:57)
	at org.junit.platform.engine.support.hierarchical.HierarchicalTestEngine.execute(HierarchicalTestEngine.java:54)
	at org.junit.platform.launcher.core.EngineExecutionOrchestrator.execute(EngineExecutionOrchestrator.java:198)
	at org.junit.platform.launcher.core.EngineExecutionOrchestrator.execute(EngineExecutionOrchestrator.java:169)
	at org.junit.platform.launcher.core.EngineExecutionOrchestrator.execute(EngineExecutionOrchestrator.java:93)
	at org.junit.platform.launcher.core.EngineExecutionOrchestrator.lambda$execute$0(EngineExecutionOrchestrator.java:58)
	at org.junit.platform.launcher.core.EngineExecutionOrchestrator.withInterceptedStreams(EngineExecutionOrchestrator.java:141)
	at org.junit.platform.launcher.core.EngineExecutionOrchestrator.execute(EngineExecutionOrchestrator.java:57)
	at org.junit.platform.launcher.core.DefaultLauncher.execute(DefaultLauncher.java:103)
	at org.junit.platform.launcher.core.DefaultLauncher.execute(DefaultLauncher.java:85)
	at org.junit.platform.launcher.core.DelegatingLauncher.execute(DelegatingLauncher.java:47)
	at org.gradle.api.internal.tasks.testing.junitplatform.JUnitPlatformTestClassProcessor$CollectAllTestClassesExecutor.processAllTestClasses(JUnitPlatformTestClassProcessor.java:124)
	at org.gradle.api.internal.tasks.testing.junitplatform.JUnitPlatformTestClassProcessor$CollectAllTestClassesExecutor.access$000(JUnitPlatformTestClassProcessor.java:99)
	at org.gradle.api.internal.tasks.testing.junitplatform.JUnitPlatformTestClassProcessor.stop(JUnitPlatformTestClassProcessor.java:94)
	at org.gradle.api.internal.tasks.testing.SuiteTestClassProcessor.stop(SuiteTestClassProcessor.java:63)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.gradle.internal.dispatch.ReflectionDispatch.dispatch(ReflectionDispatch.java:36)
	at org.gradle.internal.dispatch.ReflectionDispatch.dispatch(ReflectionDispatch.java:24)
	at org.gradle.internal.dispatch.ContextClassLoaderDispatch.dispatch(ContextClassLoaderDispatch.java:33)
	at org.gradle.internal.dispatch.ProxyDispatchAdapter$DispatchingInvocationHandler.invoke(ProxyDispatchAdapter.java:92)
	at jdk.proxy1/jdk.proxy1.$Proxy4.stop(Unknown Source)
	at org.gradle.api.internal.tasks.testing.worker.TestWorker$3.run(TestWorker.java:200)
	at org.gradle.api.internal.tasks.testing.worker.TestWorker.executeAndMaintainThreadName(TestWorker.java:132)
	at org.gradle.api.internal.tasks.testing.worker.TestWorker.execute(TestWorker.java:103)
	at org.gradle.api.internal.tasks.testing.worker.TestWorker.execute(TestWorker.java:63)
	at org.gradle.process.internal.worker.child.ActionExecutionWorker.execute(ActionExecutionWorker.java:56)
	at org.gradle.process.internal.worker.child.SystemApplicationClassLoaderWorker.call(SystemApplicationClassLoaderWorker.java:121)
	at org.gradle.process.internal.worker.child.SystemApplicationClassLoaderWorker.call(SystemApplicationClassLoaderWorker.java:71)
	at worker.org.gradle.process.internal.worker.GradleWorkerMain.run(GradleWorkerMain.java:69)
	at worker.org.gradle.process.internal.worker.GradleWorkerMain.main(GradleWorkerMain.java:74)
2025-05-30 23:27:25 [Test worker] INFO  c.i.a.c.InstrumentController - Starting SEC synchronization - dryRun: true, forceRefresh: false, maxInstruments: 1000
2025-05-30 23:27:25 [Test worker] INFO  c.i.a.c.InstrumentController - SEC synchronization (dry-run) completed: DRY RUN: Found 50 missing symbols out of 5000 SEC symbols. Would add 50 new instruments to database.
2025-05-30 23:27:25 [Test worker] INFO  c.i.a.c.InstrumentController - Starting CSV upload processing - file: instruments.csv, size: 289 bytes, dryRun: true, maxInstruments: 1000, skipDuplicates: true, validateData: true
2025-05-30 23:27:25 [Test worker] INFO  c.i.a.c.InstrumentController - CSV validation completed: DRY RUN: Processed 2 valid rows out of 2 total rows. Would add/update 2 instruments. Found 0 validation errors.
2025-05-30 23:27:25 [Test worker] INFO  c.i.a.c.InstrumentController - Starting CSV upload processing - file: instruments.csv, size: 191 bytes, dryRun: false, maxInstruments: 1000, skipDuplicates: true, validateData: true
2025-05-30 23:27:25 [Test worker] WARN  c.i.a.c.InstrumentController - PERFORMING ACTUAL CSV IMPORT - This will add/update instruments in the database
2025-05-30 23:27:25 [Test worker] INFO  c.i.a.c.InstrumentController - CSV import completed: CSV UPLOAD COMPLETED: Processed 1 instruments from 1 valid rows. Added 1 new instruments, updated 0 existing instruments, skipped 0 duplicates.
2025-05-30 23:27:25 [Test worker] INFO  c.i.a.c.InstrumentController - Starting CSV upload processing - file: invalid.csv, size: 15 bytes, dryRun: true, maxInstruments: 1000, skipDuplicates: true, validateData: true
2025-05-30 23:27:25 [Test worker] ERROR c.i.a.c.InstrumentController - Error during CSV processing
java.lang.RuntimeException: Processing error
	at java.base/jdk.internal.reflect.DirectConstructorHandleAccessor.newInstance(DirectConstructorHandleAccessor.java:62)
	at java.base/java.lang.reflect.Constructor.newInstanceWithCaller(Constructor.java:502)
	at java.base/java.lang.reflect.Constructor.newInstance(Constructor.java:486)
	at org.codehaus.groovy.reflection.CachedConstructor.invoke(CachedConstructor.java:72)
	at org.codehaus.groovy.runtime.callsite.ConstructorSite$ConstructorSiteNoUnwrapNoCoerce.callConstructor(ConstructorSite.java:105)
	at org.codehaus.groovy.runtime.callsite.CallSiteArray.defaultCallConstructor(CallSiteArray.java:59)
	at org.codehaus.groovy.runtime.callsite.AbstractCallSite.callConstructor(AbstractCallSite.java:263)
	at org.codehaus.groovy.runtime.callsite.AbstractCallSite.callConstructor(AbstractCallSite.java:277)
	at com.investment.api.controller.InstrumentControllerSpec$__spock_feature_0_20_closure5.doCall(InstrumentControllerSpec.groovy:337)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.codehaus.groovy.reflection.CachedMethod.invoke(CachedMethod.java:107)
	at groovy.lang.MetaMethod.doMethodInvoke(MetaMethod.java:323)
	at org.codehaus.groovy.runtime.metaclass.ClosureMetaClass.invokeMethod(ClosureMetaClass.java:274)
	at groovy.lang.MetaClassImpl.invokeMethod(MetaClassImpl.java:1030)
	at groovy.lang.Closure.call(Closure.java:427)
	at org.spockframework.runtime.GroovyRuntimeUtil.invokeClosure(GroovyRuntimeUtil.java:200)
	at org.spockframework.mock.response.CodeResponseGenerator.invokeClosure(CodeResponseGenerator.java:54)
	at org.spockframework.mock.response.CodeResponseGenerator.doRespond(CodeResponseGenerator.java:37)
	at org.spockframework.mock.response.SingleResponseGenerator.respond(SingleResponseGenerator.java:32)
	at org.spockframework.mock.response.ResponseGeneratorChain.respond(ResponseGeneratorChain.java:44)
	at org.spockframework.mock.runtime.MockInteraction.accept(MockInteraction.java:73)
	at org.spockframework.mock.runtime.MockInteractionDecorator.accept(MockInteractionDecorator.java:50)
	at org.spockframework.mock.runtime.InteractionScope$1.accept(InteractionScope.java:57)
	at org.spockframework.mock.runtime.MockController.handle(MockController.java:40)
	at org.spockframework.mock.runtime.JavaMockInterceptor.intercept(JavaMockInterceptor.java:86)
	at org.spockframework.mock.runtime.ByteBuddyInterceptorAdapter.interceptNonAbstract(ByteBuddyInterceptorAdapter.java:35)
	at com.investment.service.CsvInstrumentService$SpockMock$574285064.processCsvFile(Unknown Source)
	at com.investment.api.controller.InstrumentController.uploadCsv(InstrumentController.java:355)
	at com.investment.api.controller.InstrumentController$uploadCsv$4.call(Unknown Source)
	at org.codehaus.groovy.runtime.callsite.CallSiteArray.defaultCall(CallSiteArray.java:47)
	at org.codehaus.groovy.runtime.callsite.AbstractCallSite.call(AbstractCallSite.java:125)
	at com.investment.api.controller.InstrumentControllerSpec.$spock_feature_0_20(InstrumentControllerSpec.groovy:340)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.spockframework.util.ReflectionUtil.invokeMethod(ReflectionUtil.java:187)
	at org.spockframework.runtime.model.MethodInfo.lambda$new$0(MethodInfo.java:49)
	at org.spockframework.runtime.model.MethodInfo.invoke(MethodInfo.java:156)
	at org.spockframework.runtime.extension.MethodInvocation.proceed(MethodInvocation.java:102)
	at org.spockframework.junit4.ExceptionAdapterInterceptor.intercept(ExceptionAdapterInterceptor.java:13)
	at org.spockframework.runtime.extension.MethodInvocation.proceed(MethodInvocation.java:101)
	at org.spockframework.runtime.PlatformSpecRunner.invoke(PlatformSpecRunner.java:398)
	at org.spockframework.runtime.PlatformSpecRunner.runFeatureMethod(PlatformSpecRunner.java:324)
	at org.spockframework.runtime.IterationNode.execute(IterationNode.java:50)
	at org.spockframework.runtime.SimpleFeatureNode.execute(SimpleFeatureNode.java:58)
	at org.spockframework.runtime.SimpleFeatureNode.execute(SimpleFeatureNode.java:15)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$6(NodeTestTask.java:151)
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$8(NodeTestTask.java:141)
	at org.spockframework.runtime.SpockNode.sneakyInvoke(SpockNode.java:40)
	at org.spockframework.runtime.IterationNode.lambda$around$0(IterationNode.java:67)
	at org.spockframework.runtime.PlatformSpecRunner.lambda$createMethodInfoForDoRunIteration$5(PlatformSpecRunner.java:236)
	at org.spockframework.runtime.model.MethodInfo.invoke(MethodInfo.java:156)
	at org.spockframework.runtime.extension.MethodInvocation.proceed(MethodInvocation.java:102)
	at org.spockframework.junit4.ExceptionAdapterInterceptor.intercept(ExceptionAdapterInterceptor.java:13)
	at org.spockframework.runtime.extension.MethodInvocation.proceed(MethodInvocation.java:101)
	at org.spockframework.runtime.PlatformSpecRunner.invoke(PlatformSpecRunner.java:398)
	at org.spockframework.runtime.PlatformSpecRunner.runIteration(PlatformSpecRunner.java:218)
	at org.spockframework.runtime.IterationNode.around(IterationNode.java:67)
	at org.spockframework.runtime.SimpleFeatureNode.lambda$around$0(SimpleFeatureNode.java:52)
	at org.spockframework.runtime.SpockNode.sneakyInvoke(SpockNode.java:40)
	at org.spockframework.runtime.FeatureNode.lambda$around$0(FeatureNode.java:41)
	at org.spockframework.runtime.PlatformSpecRunner.lambda$createMethodInfoForDoRunFeature$4(PlatformSpecRunner.java:199)
	at org.spockframework.runtime.model.MethodInfo.invoke(MethodInfo.java:156)
	at org.spockframework.runtime.PlatformSpecRunner.invokeRaw(PlatformSpecRunner.java:407)
	at org.spockframework.runtime.PlatformSpecRunner.invoke(PlatformSpecRunner.java:390)
	at org.spockframework.runtime.PlatformSpecRunner.runFeature(PlatformSpecRunner.java:192)
	at org.spockframework.runtime.FeatureNode.around(FeatureNode.java:41)
	at org.spockframework.runtime.SimpleFeatureNode.around(SimpleFeatureNode.java:52)
	at org.spockframework.runtime.SimpleFeatureNode.around(SimpleFeatureNode.java:15)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$9(NodeTestTask.java:139)
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.executeRecursively(NodeTestTask.java:138)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.execute(NodeTestTask.java:95)
	at java.base/java.util.ArrayList.forEach(ArrayList.java:1596)
	at org.junit.platform.engine.support.hierarchical.SameThreadHierarchicalTestExecutorService.invokeAll(SameThreadHierarchicalTestExecutorService.java:41)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$6(NodeTestTask.java:155)
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$8(NodeTestTask.java:141)
	at org.spockframework.runtime.SpockNode.sneakyInvoke(SpockNode.java:40)
	at org.spockframework.runtime.SpecNode.lambda$around$0(SpecNode.java:63)
	at org.spockframework.runtime.PlatformSpecRunner.lambda$createMethodInfoForDoRunSpec$0(PlatformSpecRunner.java:61)
	at org.spockframework.runtime.model.MethodInfo.invoke(MethodInfo.java:156)
	at org.spockframework.runtime.PlatformSpecRunner.invokeRaw(PlatformSpecRunner.java:407)
	at org.spockframework.runtime.PlatformSpecRunner.invoke(PlatformSpecRunner.java:390)
	at org.spockframework.runtime.PlatformSpecRunner.runSpec(PlatformSpecRunner.java:55)
	at org.spockframework.runtime.SpecNode.around(SpecNode.java:63)
	at org.spockframework.runtime.SpecNode.around(SpecNode.java:11)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$9(NodeTestTask.java:139)
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.executeRecursively(NodeTestTask.java:138)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.execute(NodeTestTask.java:95)
	at java.base/java.util.ArrayList.forEach(ArrayList.java:1596)
	at org.junit.platform.engine.support.hierarchical.SameThreadHierarchicalTestExecutorService.invokeAll(SameThreadHierarchicalTestExecutorService.java:41)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$6(NodeTestTask.java:155)
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$8(NodeTestTask.java:141)
	at org.junit.platform.engine.support.hierarchical.Node.around(Node.java:137)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$9(NodeTestTask.java:139)
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.executeRecursively(NodeTestTask.java:138)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.execute(NodeTestTask.java:95)
	at org.junit.platform.engine.support.hierarchical.SameThreadHierarchicalTestExecutorService.submit(SameThreadHierarchicalTestExecutorService.java:35)
	at org.junit.platform.engine.support.hierarchical.HierarchicalTestExecutor.execute(HierarchicalTestExecutor.java:57)
	at org.junit.platform.engine.support.hierarchical.HierarchicalTestEngine.execute(HierarchicalTestEngine.java:54)
	at org.junit.platform.launcher.core.EngineExecutionOrchestrator.execute(EngineExecutionOrchestrator.java:198)
	at org.junit.platform.launcher.core.EngineExecutionOrchestrator.execute(EngineExecutionOrchestrator.java:169)
	at org.junit.platform.launcher.core.EngineExecutionOrchestrator.execute(EngineExecutionOrchestrator.java:93)
	at org.junit.platform.launcher.core.EngineExecutionOrchestrator.lambda$execute$0(EngineExecutionOrchestrator.java:58)
	at org.junit.platform.launcher.core.EngineExecutionOrchestrator.withInterceptedStreams(EngineExecutionOrchestrator.java:141)
	at org.junit.platform.launcher.core.EngineExecutionOrchestrator.execute(EngineExecutionOrchestrator.java:57)
	at org.junit.platform.launcher.core.DefaultLauncher.execute(DefaultLauncher.java:103)
	at org.junit.platform.launcher.core.DefaultLauncher.execute(DefaultLauncher.java:85)
	at org.junit.platform.launcher.core.DelegatingLauncher.execute(DelegatingLauncher.java:47)
	at org.gradle.api.internal.tasks.testing.junitplatform.JUnitPlatformTestClassProcessor$CollectAllTestClassesExecutor.processAllTestClasses(JUnitPlatformTestClassProcessor.java:124)
	at org.gradle.api.internal.tasks.testing.junitplatform.JUnitPlatformTestClassProcessor$CollectAllTestClassesExecutor.access$000(JUnitPlatformTestClassProcessor.java:99)
	at org.gradle.api.internal.tasks.testing.junitplatform.JUnitPlatformTestClassProcessor.stop(JUnitPlatformTestClassProcessor.java:94)
	at org.gradle.api.internal.tasks.testing.SuiteTestClassProcessor.stop(SuiteTestClassProcessor.java:63)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.gradle.internal.dispatch.ReflectionDispatch.dispatch(ReflectionDispatch.java:36)
	at org.gradle.internal.dispatch.ReflectionDispatch.dispatch(ReflectionDispatch.java:24)
	at org.gradle.internal.dispatch.ContextClassLoaderDispatch.dispatch(ContextClassLoaderDispatch.java:33)
	at org.gradle.internal.dispatch.ProxyDispatchAdapter$DispatchingInvocationHandler.invoke(ProxyDispatchAdapter.java:92)
	at jdk.proxy1/jdk.proxy1.$Proxy4.stop(Unknown Source)
	at org.gradle.api.internal.tasks.testing.worker.TestWorker$3.run(TestWorker.java:200)
	at org.gradle.api.internal.tasks.testing.worker.TestWorker.executeAndMaintainThreadName(TestWorker.java:132)
	at org.gradle.api.internal.tasks.testing.worker.TestWorker.execute(TestWorker.java:103)
	at org.gradle.api.internal.tasks.testing.worker.TestWorker.execute(TestWorker.java:63)
	at org.gradle.process.internal.worker.child.ActionExecutionWorker.execute(ActionExecutionWorker.java:56)
	at org.gradle.process.internal.worker.child.SystemApplicationClassLoaderWorker.call(SystemApplicationClassLoaderWorker.java:121)
	at org.gradle.process.internal.worker.child.SystemApplicationClassLoaderWorker.call(SystemApplicationClassLoaderWorker.java:71)
	at worker.org.gradle.process.internal.worker.GradleWorkerMain.run(GradleWorkerMain.java:69)
	at worker.org.gradle.process.internal.worker.GradleWorkerMain.main(GradleWorkerMain.java:74)
2025-05-30 23:27:25 [Test worker] INFO  c.i.a.c.InstrumentController - Starting CSV upload processing - file: instruments.csv, size: 22 bytes, dryRun: false, maxInstruments: 500, skipDuplicates: false, validateData: false
2025-05-30 23:27:25 [Test worker] WARN  c.i.a.c.InstrumentController - PERFORMING ACTUAL CSV IMPORT - This will add/update instruments in the database
2025-05-30 23:27:25 [Test worker] INFO  c.i.a.c.InstrumentController - CSV import completed: CSV UPLOAD COMPLETED: Processed 1 instruments from 1 valid rows. Added 0 new instruments, updated 1 existing instruments, skipped 0 duplicates.
2025-05-30 23:27:46 [Test worker] INFO  c.i.a.c.InstrumentController - Starting symbol validation (dry-run) - forceRefresh: false
2025-05-30 23:27:46 [Test worker] INFO  c.i.a.c.InstrumentController - Symbol validation (dry-run) completed: DRY RUN: Found 2 invalid symbols out of 10 total. Would delete 2 instruments and 15 OHLCV records.
2025-05-30 23:27:46 [Test worker] INFO  c.i.a.c.InstrumentController - Starting symbol validation (dry-run) - forceRefresh: true
2025-05-30 23:27:46 [Test worker] INFO  c.i.a.c.InstrumentController - Symbol validation (dry-run) completed: DRY RUN: Found 0 invalid symbols out of 5 total. Would delete 0 instruments and 0 OHLCV records.
2025-05-30 23:27:46 [Test worker] INFO  c.i.a.c.InstrumentController - Starting symbol validation (dry-run) - forceRefresh: false
2025-05-30 23:27:46 [Test worker] ERROR c.i.a.c.InstrumentController - Error during symbol validation (dry-run)
java.lang.RuntimeException: Service error
	at java.base/jdk.internal.reflect.DirectConstructorHandleAccessor.newInstance(DirectConstructorHandleAccessor.java:62)
	at java.base/java.lang.reflect.Constructor.newInstanceWithCaller(Constructor.java:502)
	at java.base/java.lang.reflect.Constructor.newInstance(Constructor.java:486)
	at org.codehaus.groovy.reflection.CachedConstructor.invoke(CachedConstructor.java:72)
	at org.codehaus.groovy.runtime.callsite.ConstructorSite$ConstructorSiteNoUnwrapNoCoerce.callConstructor(ConstructorSite.java:105)
	at org.codehaus.groovy.runtime.callsite.CallSiteArray.defaultCallConstructor(CallSiteArray.java:59)
	at org.codehaus.groovy.runtime.callsite.AbstractCallSite.callConstructor(AbstractCallSite.java:263)
	at org.codehaus.groovy.runtime.callsite.AbstractCallSite.callConstructor(AbstractCallSite.java:277)
	at com.investment.api.controller.InstrumentControllerSpec$__spock_feature_0_2_closure1.doCall(InstrumentControllerSpec.groovy:66)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.codehaus.groovy.reflection.CachedMethod.invoke(CachedMethod.java:107)
	at groovy.lang.MetaMethod.doMethodInvoke(MetaMethod.java:323)
	at org.codehaus.groovy.runtime.metaclass.ClosureMetaClass.invokeMethod(ClosureMetaClass.java:274)
	at groovy.lang.MetaClassImpl.invokeMethod(MetaClassImpl.java:1030)
	at groovy.lang.Closure.call(Closure.java:427)
	at org.spockframework.runtime.GroovyRuntimeUtil.invokeClosure(GroovyRuntimeUtil.java:200)
	at org.spockframework.mock.response.CodeResponseGenerator.invokeClosure(CodeResponseGenerator.java:54)
	at org.spockframework.mock.response.CodeResponseGenerator.doRespond(CodeResponseGenerator.java:37)
	at org.spockframework.mock.response.SingleResponseGenerator.respond(SingleResponseGenerator.java:32)
	at org.spockframework.mock.response.ResponseGeneratorChain.respond(ResponseGeneratorChain.java:44)
	at org.spockframework.mock.runtime.MockInteraction.accept(MockInteraction.java:73)
	at org.spockframework.mock.runtime.MockInteractionDecorator.accept(MockInteractionDecorator.java:50)
	at org.spockframework.mock.runtime.InteractionScope$1.accept(InteractionScope.java:57)
	at org.spockframework.mock.runtime.MockController.handle(MockController.java:40)
	at org.spockframework.mock.runtime.JavaMockInterceptor.intercept(JavaMockInterceptor.java:86)
	at org.spockframework.mock.runtime.ByteBuddyInterceptorAdapter.interceptNonAbstract(ByteBuddyInterceptorAdapter.java:35)
	at com.investment.service.SymbolValidationService$SpockMock$606459612.validateSymbols(Unknown Source)
	at com.investment.api.controller.InstrumentController.validateSymbolsDryRun(InstrumentController.java:78)
	at com.investment.api.controller.InstrumentController$validateSymbolsDryRun.call(Unknown Source)
	at org.codehaus.groovy.runtime.callsite.CallSiteArray.defaultCall(CallSiteArray.java:47)
	at org.codehaus.groovy.runtime.callsite.AbstractCallSite.call(AbstractCallSite.java:125)
	at org.codehaus.groovy.runtime.callsite.AbstractCallSite.call(AbstractCallSite.java:139)
	at com.investment.api.controller.InstrumentControllerSpec.$spock_feature_0_2(InstrumentControllerSpec.groovy:69)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.spockframework.util.ReflectionUtil.invokeMethod(ReflectionUtil.java:187)
	at org.spockframework.runtime.model.MethodInfo.lambda$new$0(MethodInfo.java:49)
	at org.spockframework.runtime.model.MethodInfo.invoke(MethodInfo.java:156)
	at org.spockframework.runtime.extension.MethodInvocation.proceed(MethodInvocation.java:102)
	at org.spockframework.junit4.ExceptionAdapterInterceptor.intercept(ExceptionAdapterInterceptor.java:13)
	at org.spockframework.runtime.extension.MethodInvocation.proceed(MethodInvocation.java:101)
	at org.spockframework.runtime.PlatformSpecRunner.invoke(PlatformSpecRunner.java:398)
	at org.spockframework.runtime.PlatformSpecRunner.runFeatureMethod(PlatformSpecRunner.java:324)
	at org.spockframework.runtime.IterationNode.execute(IterationNode.java:50)
	at org.spockframework.runtime.SimpleFeatureNode.execute(SimpleFeatureNode.java:58)
	at org.spockframework.runtime.SimpleFeatureNode.execute(SimpleFeatureNode.java:15)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$6(NodeTestTask.java:151)
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$8(NodeTestTask.java:141)
	at org.spockframework.runtime.SpockNode.sneakyInvoke(SpockNode.java:40)
	at org.spockframework.runtime.IterationNode.lambda$around$0(IterationNode.java:67)
	at org.spockframework.runtime.PlatformSpecRunner.lambda$createMethodInfoForDoRunIteration$5(PlatformSpecRunner.java:236)
	at org.spockframework.runtime.model.MethodInfo.invoke(MethodInfo.java:156)
	at org.spockframework.runtime.extension.MethodInvocation.proceed(MethodInvocation.java:102)
	at org.spockframework.junit4.ExceptionAdapterInterceptor.intercept(ExceptionAdapterInterceptor.java:13)
	at org.spockframework.runtime.extension.MethodInvocation.proceed(MethodInvocation.java:101)
	at org.spockframework.runtime.PlatformSpecRunner.invoke(PlatformSpecRunner.java:398)
	at org.spockframework.runtime.PlatformSpecRunner.runIteration(PlatformSpecRunner.java:218)
	at org.spockframework.runtime.IterationNode.around(IterationNode.java:67)
	at org.spockframework.runtime.SimpleFeatureNode.lambda$around$0(SimpleFeatureNode.java:52)
	at org.spockframework.runtime.SpockNode.sneakyInvoke(SpockNode.java:40)
	at org.spockframework.runtime.FeatureNode.lambda$around$0(FeatureNode.java:41)
	at org.spockframework.runtime.PlatformSpecRunner.lambda$createMethodInfoForDoRunFeature$4(PlatformSpecRunner.java:199)
	at org.spockframework.runtime.model.MethodInfo.invoke(MethodInfo.java:156)
	at org.spockframework.runtime.PlatformSpecRunner.invokeRaw(PlatformSpecRunner.java:407)
	at org.spockframework.runtime.PlatformSpecRunner.invoke(PlatformSpecRunner.java:390)
	at org.spockframework.runtime.PlatformSpecRunner.runFeature(PlatformSpecRunner.java:192)
	at org.spockframework.runtime.FeatureNode.around(FeatureNode.java:41)
	at org.spockframework.runtime.SimpleFeatureNode.around(SimpleFeatureNode.java:52)
	at org.spockframework.runtime.SimpleFeatureNode.around(SimpleFeatureNode.java:15)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$9(NodeTestTask.java:139)
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.executeRecursively(NodeTestTask.java:138)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.execute(NodeTestTask.java:95)
	at java.base/java.util.ArrayList.forEach(ArrayList.java:1596)
	at org.junit.platform.engine.support.hierarchical.SameThreadHierarchicalTestExecutorService.invokeAll(SameThreadHierarchicalTestExecutorService.java:41)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$6(NodeTestTask.java:155)
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$8(NodeTestTask.java:141)
	at org.spockframework.runtime.SpockNode.sneakyInvoke(SpockNode.java:40)
	at org.spockframework.runtime.SpecNode.lambda$around$0(SpecNode.java:63)
	at org.spockframework.runtime.PlatformSpecRunner.lambda$createMethodInfoForDoRunSpec$0(PlatformSpecRunner.java:61)
	at org.spockframework.runtime.model.MethodInfo.invoke(MethodInfo.java:156)
	at org.spockframework.runtime.PlatformSpecRunner.invokeRaw(PlatformSpecRunner.java:407)
	at org.spockframework.runtime.PlatformSpecRunner.invoke(PlatformSpecRunner.java:390)
	at org.spockframework.runtime.PlatformSpecRunner.runSpec(PlatformSpecRunner.java:55)
	at org.spockframework.runtime.SpecNode.around(SpecNode.java:63)
	at org.spockframework.runtime.SpecNode.around(SpecNode.java:11)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$9(NodeTestTask.java:139)
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.executeRecursively(NodeTestTask.java:138)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.execute(NodeTestTask.java:95)
	at java.base/java.util.ArrayList.forEach(ArrayList.java:1596)
	at org.junit.platform.engine.support.hierarchical.SameThreadHierarchicalTestExecutorService.invokeAll(SameThreadHierarchicalTestExecutorService.java:41)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$6(NodeTestTask.java:155)
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$8(NodeTestTask.java:141)
	at org.junit.platform.engine.support.hierarchical.Node.around(Node.java:137)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$9(NodeTestTask.java:139)
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.executeRecursively(NodeTestTask.java:138)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.execute(NodeTestTask.java:95)
	at org.junit.platform.engine.support.hierarchical.SameThreadHierarchicalTestExecutorService.submit(SameThreadHierarchicalTestExecutorService.java:35)
	at org.junit.platform.engine.support.hierarchical.HierarchicalTestExecutor.execute(HierarchicalTestExecutor.java:57)
	at org.junit.platform.engine.support.hierarchical.HierarchicalTestEngine.execute(HierarchicalTestEngine.java:54)
	at org.junit.platform.launcher.core.EngineExecutionOrchestrator.execute(EngineExecutionOrchestrator.java:198)
	at org.junit.platform.launcher.core.EngineExecutionOrchestrator.execute(EngineExecutionOrchestrator.java:169)
	at org.junit.platform.launcher.core.EngineExecutionOrchestrator.execute(EngineExecutionOrchestrator.java:93)
	at org.junit.platform.launcher.core.EngineExecutionOrchestrator.lambda$execute$0(EngineExecutionOrchestrator.java:58)
	at org.junit.platform.launcher.core.EngineExecutionOrchestrator.withInterceptedStreams(EngineExecutionOrchestrator.java:141)
	at org.junit.platform.launcher.core.EngineExecutionOrchestrator.execute(EngineExecutionOrchestrator.java:57)
	at org.junit.platform.launcher.core.DefaultLauncher.execute(DefaultLauncher.java:103)
	at org.junit.platform.launcher.core.DefaultLauncher.execute(DefaultLauncher.java:85)
	at org.junit.platform.launcher.core.DelegatingLauncher.execute(DelegatingLauncher.java:47)
	at org.gradle.api.internal.tasks.testing.junitplatform.JUnitPlatformTestClassProcessor$CollectAllTestClassesExecutor.processAllTestClasses(JUnitPlatformTestClassProcessor.java:124)
	at org.gradle.api.internal.tasks.testing.junitplatform.JUnitPlatformTestClassProcessor$CollectAllTestClassesExecutor.access$000(JUnitPlatformTestClassProcessor.java:99)
	at org.gradle.api.internal.tasks.testing.junitplatform.JUnitPlatformTestClassProcessor.stop(JUnitPlatformTestClassProcessor.java:94)
	at org.gradle.api.internal.tasks.testing.SuiteTestClassProcessor.stop(SuiteTestClassProcessor.java:63)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.gradle.internal.dispatch.ReflectionDispatch.dispatch(ReflectionDispatch.java:36)
	at org.gradle.internal.dispatch.ReflectionDispatch.dispatch(ReflectionDispatch.java:24)
	at org.gradle.internal.dispatch.ContextClassLoaderDispatch.dispatch(ContextClassLoaderDispatch.java:33)
	at org.gradle.internal.dispatch.ProxyDispatchAdapter$DispatchingInvocationHandler.invoke(ProxyDispatchAdapter.java:92)
	at jdk.proxy1/jdk.proxy1.$Proxy4.stop(Unknown Source)
	at org.gradle.api.internal.tasks.testing.worker.TestWorker$3.run(TestWorker.java:200)
	at org.gradle.api.internal.tasks.testing.worker.TestWorker.executeAndMaintainThreadName(TestWorker.java:132)
	at org.gradle.api.internal.tasks.testing.worker.TestWorker.execute(TestWorker.java:103)
	at org.gradle.api.internal.tasks.testing.worker.TestWorker.execute(TestWorker.java:63)
	at org.gradle.process.internal.worker.child.ActionExecutionWorker.execute(ActionExecutionWorker.java:56)
	at org.gradle.process.internal.worker.child.SystemApplicationClassLoaderWorker.call(SystemApplicationClassLoaderWorker.java:121)
	at org.gradle.process.internal.worker.child.SystemApplicationClassLoaderWorker.call(SystemApplicationClassLoaderWorker.java:71)
	at worker.org.gradle.process.internal.worker.GradleWorkerMain.run(GradleWorkerMain.java:69)
	at worker.org.gradle.process.internal.worker.GradleWorkerMain.main(GradleWorkerMain.java:74)
2025-05-30 23:27:46 [Test worker] INFO  c.i.a.c.InstrumentController - Starting symbol validation - dryRun: false, forceRefresh: false
2025-05-30 23:27:46 [Test worker] WARN  c.i.a.c.InstrumentController - PERFORMING ACTUAL CLEANUP - This will permanently delete invalid symbols and their data
2025-05-30 23:27:46 [Test worker] INFO  c.i.a.c.InstrumentController - Symbol cleanup completed: CLEANUP COMPLETED: Deleted 2 invalid symbols out of 10 total. Removed 2 instruments and 15 OHLCV records.
2025-05-30 23:27:46 [Test worker] INFO  c.i.a.c.InstrumentController - Starting symbol validation - dryRun: true, forceRefresh: false
2025-05-30 23:27:46 [Test worker] INFO  c.i.a.c.InstrumentController - Symbol validation (dry-run) completed: DRY RUN: Found 2 invalid symbols out of 10 total. Would delete 2 instruments and 15 OHLCV records.
2025-05-30 23:27:46 [Test worker] ERROR c.i.a.c.InstrumentController - Error retrieving SEC cache status
java.lang.RuntimeException: Cache error
	at java.base/jdk.internal.reflect.DirectConstructorHandleAccessor.newInstance(DirectConstructorHandleAccessor.java:62)
	at java.base/java.lang.reflect.Constructor.newInstanceWithCaller(Constructor.java:502)
	at java.base/java.lang.reflect.Constructor.newInstance(Constructor.java:486)
	at org.codehaus.groovy.reflection.CachedConstructor.invoke(CachedConstructor.java:72)
	at org.codehaus.groovy.runtime.callsite.ConstructorSite$ConstructorSiteNoUnwrapNoCoerce.callConstructor(ConstructorSite.java:105)
	at org.codehaus.groovy.runtime.callsite.CallSiteArray.defaultCallConstructor(CallSiteArray.java:59)
	at org.codehaus.groovy.runtime.callsite.AbstractCallSite.callConstructor(AbstractCallSite.java:263)
	at org.codehaus.groovy.runtime.callsite.AbstractCallSite.callConstructor(AbstractCallSite.java:277)
	at com.investment.api.controller.InstrumentControllerSpec$__spock_feature_0_7_closure2.doCall(InstrumentControllerSpec.groovy:141)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.codehaus.groovy.reflection.CachedMethod.invoke(CachedMethod.java:107)
	at groovy.lang.MetaMethod.doMethodInvoke(MetaMethod.java:323)
	at org.codehaus.groovy.runtime.metaclass.ClosureMetaClass.invokeMethod(ClosureMetaClass.java:274)
	at groovy.lang.MetaClassImpl.invokeMethod(MetaClassImpl.java:1030)
	at groovy.lang.Closure.call(Closure.java:427)
	at org.spockframework.runtime.GroovyRuntimeUtil.invokeClosure(GroovyRuntimeUtil.java:200)
	at org.spockframework.mock.response.CodeResponseGenerator.invokeClosure(CodeResponseGenerator.java:54)
	at org.spockframework.mock.response.CodeResponseGenerator.doRespond(CodeResponseGenerator.java:37)
	at org.spockframework.mock.response.SingleResponseGenerator.respond(SingleResponseGenerator.java:32)
	at org.spockframework.mock.response.ResponseGeneratorChain.respond(ResponseGeneratorChain.java:44)
	at org.spockframework.mock.runtime.MockInteraction.accept(MockInteraction.java:73)
	at org.spockframework.mock.runtime.MockInteractionDecorator.accept(MockInteractionDecorator.java:50)
	at org.spockframework.mock.runtime.InteractionScope$1.accept(InteractionScope.java:57)
	at org.spockframework.mock.runtime.MockController.handle(MockController.java:40)
	at org.spockframework.mock.runtime.JavaMockInterceptor.intercept(JavaMockInterceptor.java:86)
	at org.spockframework.mock.runtime.ByteBuddyInterceptorAdapter.interceptNonAbstract(ByteBuddyInterceptorAdapter.java:35)
	at com.investment.service.SymbolValidationService$SpockMock$606459612.getCacheStatus(Unknown Source)
	at com.investment.api.controller.InstrumentController.getSecCacheStatus(InstrumentController.java:175)
	at com.investment.api.controller.InstrumentController$getSecCacheStatus$1.call(Unknown Source)
	at org.codehaus.groovy.runtime.callsite.CallSiteArray.defaultCall(CallSiteArray.java:47)
	at org.codehaus.groovy.runtime.callsite.AbstractCallSite.call(AbstractCallSite.java:125)
	at org.codehaus.groovy.runtime.callsite.AbstractCallSite.call(AbstractCallSite.java:130)
	at com.investment.api.controller.InstrumentControllerSpec.$spock_feature_0_7(InstrumentControllerSpec.groovy:144)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.spockframework.util.ReflectionUtil.invokeMethod(ReflectionUtil.java:187)
	at org.spockframework.runtime.model.MethodInfo.lambda$new$0(MethodInfo.java:49)
	at org.spockframework.runtime.model.MethodInfo.invoke(MethodInfo.java:156)
	at org.spockframework.runtime.extension.MethodInvocation.proceed(MethodInvocation.java:102)
	at org.spockframework.junit4.ExceptionAdapterInterceptor.intercept(ExceptionAdapterInterceptor.java:13)
	at org.spockframework.runtime.extension.MethodInvocation.proceed(MethodInvocation.java:101)
	at org.spockframework.runtime.PlatformSpecRunner.invoke(PlatformSpecRunner.java:398)
	at org.spockframework.runtime.PlatformSpecRunner.runFeatureMethod(PlatformSpecRunner.java:324)
	at org.spockframework.runtime.IterationNode.execute(IterationNode.java:50)
	at org.spockframework.runtime.SimpleFeatureNode.execute(SimpleFeatureNode.java:58)
	at org.spockframework.runtime.SimpleFeatureNode.execute(SimpleFeatureNode.java:15)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$6(NodeTestTask.java:151)
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$8(NodeTestTask.java:141)
	at org.spockframework.runtime.SpockNode.sneakyInvoke(SpockNode.java:40)
	at org.spockframework.runtime.IterationNode.lambda$around$0(IterationNode.java:67)
	at org.spockframework.runtime.PlatformSpecRunner.lambda$createMethodInfoForDoRunIteration$5(PlatformSpecRunner.java:236)
	at org.spockframework.runtime.model.MethodInfo.invoke(MethodInfo.java:156)
	at org.spockframework.runtime.extension.MethodInvocation.proceed(MethodInvocation.java:102)
	at org.spockframework.junit4.ExceptionAdapterInterceptor.intercept(ExceptionAdapterInterceptor.java:13)
	at org.spockframework.runtime.extension.MethodInvocation.proceed(MethodInvocation.java:101)
	at org.spockframework.runtime.PlatformSpecRunner.invoke(PlatformSpecRunner.java:398)
	at org.spockframework.runtime.PlatformSpecRunner.runIteration(PlatformSpecRunner.java:218)
	at org.spockframework.runtime.IterationNode.around(IterationNode.java:67)
	at org.spockframework.runtime.SimpleFeatureNode.lambda$around$0(SimpleFeatureNode.java:52)
	at org.spockframework.runtime.SpockNode.sneakyInvoke(SpockNode.java:40)
	at org.spockframework.runtime.FeatureNode.lambda$around$0(FeatureNode.java:41)
	at org.spockframework.runtime.PlatformSpecRunner.lambda$createMethodInfoForDoRunFeature$4(PlatformSpecRunner.java:199)
	at org.spockframework.runtime.model.MethodInfo.invoke(MethodInfo.java:156)
	at org.spockframework.runtime.PlatformSpecRunner.invokeRaw(PlatformSpecRunner.java:407)
	at org.spockframework.runtime.PlatformSpecRunner.invoke(PlatformSpecRunner.java:390)
	at org.spockframework.runtime.PlatformSpecRunner.runFeature(PlatformSpecRunner.java:192)
	at org.spockframework.runtime.FeatureNode.around(FeatureNode.java:41)
	at org.spockframework.runtime.SimpleFeatureNode.around(SimpleFeatureNode.java:52)
	at org.spockframework.runtime.SimpleFeatureNode.around(SimpleFeatureNode.java:15)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$9(NodeTestTask.java:139)
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.executeRecursively(NodeTestTask.java:138)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.execute(NodeTestTask.java:95)
	at java.base/java.util.ArrayList.forEach(ArrayList.java:1596)
	at org.junit.platform.engine.support.hierarchical.SameThreadHierarchicalTestExecutorService.invokeAll(SameThreadHierarchicalTestExecutorService.java:41)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$6(NodeTestTask.java:155)
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$8(NodeTestTask.java:141)
	at org.spockframework.runtime.SpockNode.sneakyInvoke(SpockNode.java:40)
	at org.spockframework.runtime.SpecNode.lambda$around$0(SpecNode.java:63)
	at org.spockframework.runtime.PlatformSpecRunner.lambda$createMethodInfoForDoRunSpec$0(PlatformSpecRunner.java:61)
	at org.spockframework.runtime.model.MethodInfo.invoke(MethodInfo.java:156)
	at org.spockframework.runtime.PlatformSpecRunner.invokeRaw(PlatformSpecRunner.java:407)
	at org.spockframework.runtime.PlatformSpecRunner.invoke(PlatformSpecRunner.java:390)
	at org.spockframework.runtime.PlatformSpecRunner.runSpec(PlatformSpecRunner.java:55)
	at org.spockframework.runtime.SpecNode.around(SpecNode.java:63)
	at org.spockframework.runtime.SpecNode.around(SpecNode.java:11)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$9(NodeTestTask.java:139)
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.executeRecursively(NodeTestTask.java:138)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.execute(NodeTestTask.java:95)
	at java.base/java.util.ArrayList.forEach(ArrayList.java:1596)
	at org.junit.platform.engine.support.hierarchical.SameThreadHierarchicalTestExecutorService.invokeAll(SameThreadHierarchicalTestExecutorService.java:41)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$6(NodeTestTask.java:155)
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$8(NodeTestTask.java:141)
	at org.junit.platform.engine.support.hierarchical.Node.around(Node.java:137)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$9(NodeTestTask.java:139)
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.executeRecursively(NodeTestTask.java:138)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.execute(NodeTestTask.java:95)
	at org.junit.platform.engine.support.hierarchical.SameThreadHierarchicalTestExecutorService.submit(SameThreadHierarchicalTestExecutorService.java:35)
	at org.junit.platform.engine.support.hierarchical.HierarchicalTestExecutor.execute(HierarchicalTestExecutor.java:57)
	at org.junit.platform.engine.support.hierarchical.HierarchicalTestEngine.execute(HierarchicalTestEngine.java:54)
	at org.junit.platform.launcher.core.EngineExecutionOrchestrator.execute(EngineExecutionOrchestrator.java:198)
	at org.junit.platform.launcher.core.EngineExecutionOrchestrator.execute(EngineExecutionOrchestrator.java:169)
	at org.junit.platform.launcher.core.EngineExecutionOrchestrator.execute(EngineExecutionOrchestrator.java:93)
	at org.junit.platform.launcher.core.EngineExecutionOrchestrator.lambda$execute$0(EngineExecutionOrchestrator.java:58)
	at org.junit.platform.launcher.core.EngineExecutionOrchestrator.withInterceptedStreams(EngineExecutionOrchestrator.java:141)
	at org.junit.platform.launcher.core.EngineExecutionOrchestrator.execute(EngineExecutionOrchestrator.java:57)
	at org.junit.platform.launcher.core.DefaultLauncher.execute(DefaultLauncher.java:103)
	at org.junit.platform.launcher.core.DefaultLauncher.execute(DefaultLauncher.java:85)
	at org.junit.platform.launcher.core.DelegatingLauncher.execute(DelegatingLauncher.java:47)
	at org.gradle.api.internal.tasks.testing.junitplatform.JUnitPlatformTestClassProcessor$CollectAllTestClassesExecutor.processAllTestClasses(JUnitPlatformTestClassProcessor.java:124)
	at org.gradle.api.internal.tasks.testing.junitplatform.JUnitPlatformTestClassProcessor$CollectAllTestClassesExecutor.access$000(JUnitPlatformTestClassProcessor.java:99)
	at org.gradle.api.internal.tasks.testing.junitplatform.JUnitPlatformTestClassProcessor.stop(JUnitPlatformTestClassProcessor.java:94)
	at org.gradle.api.internal.tasks.testing.SuiteTestClassProcessor.stop(SuiteTestClassProcessor.java:63)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.gradle.internal.dispatch.ReflectionDispatch.dispatch(ReflectionDispatch.java:36)
	at org.gradle.internal.dispatch.ReflectionDispatch.dispatch(ReflectionDispatch.java:24)
	at org.gradle.internal.dispatch.ContextClassLoaderDispatch.dispatch(ContextClassLoaderDispatch.java:33)
	at org.gradle.internal.dispatch.ProxyDispatchAdapter$DispatchingInvocationHandler.invoke(ProxyDispatchAdapter.java:92)
	at jdk.proxy1/jdk.proxy1.$Proxy4.stop(Unknown Source)
	at org.gradle.api.internal.tasks.testing.worker.TestWorker$3.run(TestWorker.java:200)
	at org.gradle.api.internal.tasks.testing.worker.TestWorker.executeAndMaintainThreadName(TestWorker.java:132)
	at org.gradle.api.internal.tasks.testing.worker.TestWorker.execute(TestWorker.java:103)
	at org.gradle.api.internal.tasks.testing.worker.TestWorker.execute(TestWorker.java:63)
	at org.gradle.process.internal.worker.child.ActionExecutionWorker.execute(ActionExecutionWorker.java:56)
	at org.gradle.process.internal.worker.child.SystemApplicationClassLoaderWorker.call(SystemApplicationClassLoaderWorker.java:121)
	at org.gradle.process.internal.worker.child.SystemApplicationClassLoaderWorker.call(SystemApplicationClassLoaderWorker.java:71)
	at worker.org.gradle.process.internal.worker.GradleWorkerMain.run(GradleWorkerMain.java:69)
	at worker.org.gradle.process.internal.worker.GradleWorkerMain.main(GradleWorkerMain.java:74)
2025-05-30 23:27:46 [Test worker] INFO  c.i.a.c.InstrumentController - Starting SEC synchronization (dry-run) - forceRefresh: false, maxInstruments: 1000
2025-05-30 23:27:46 [Test worker] INFO  c.i.a.c.InstrumentController - SEC synchronization (dry-run) completed: DRY RUN: Found 50 missing symbols out of 5000 SEC symbols. Would add 50 new instruments to database.
2025-05-30 23:27:46 [Test worker] INFO  c.i.a.c.InstrumentController - Starting SEC synchronization (dry-run) - forceRefresh: true, maxInstruments: 500
2025-05-30 23:27:46 [Test worker] INFO  c.i.a.c.InstrumentController - SEC synchronization (dry-run) completed: DRY RUN: Found 25 missing symbols out of 5000 SEC symbols. Would add 25 new instruments to database.
2025-05-30 23:27:46 [Test worker] INFO  c.i.a.c.InstrumentController - Starting SEC synchronization (dry-run) - forceRefresh: false, maxInstruments: 1000
2025-05-30 23:27:46 [Test worker] ERROR c.i.a.c.InstrumentController - Error during SEC synchronization (dry-run)
java.lang.RuntimeException: SEC API error
	at java.base/jdk.internal.reflect.DirectConstructorHandleAccessor.newInstance(DirectConstructorHandleAccessor.java:62)
	at java.base/java.lang.reflect.Constructor.newInstanceWithCaller(Constructor.java:502)
	at java.base/java.lang.reflect.Constructor.newInstance(Constructor.java:486)
	at org.codehaus.groovy.reflection.CachedConstructor.invoke(CachedConstructor.java:72)
	at org.codehaus.groovy.runtime.callsite.ConstructorSite$ConstructorSiteNoUnwrapNoCoerce.callConstructor(ConstructorSite.java:105)
	at org.codehaus.groovy.runtime.callsite.CallSiteArray.defaultCallConstructor(CallSiteArray.java:59)
	at org.codehaus.groovy.runtime.callsite.AbstractCallSite.callConstructor(AbstractCallSite.java:263)
	at org.codehaus.groovy.runtime.callsite.AbstractCallSite.callConstructor(AbstractCallSite.java:277)
	at com.investment.api.controller.InstrumentControllerSpec$__spock_feature_0_10_closure3.doCall(InstrumentControllerSpec.groovy:185)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.codehaus.groovy.reflection.CachedMethod.invoke(CachedMethod.java:107)
	at groovy.lang.MetaMethod.doMethodInvoke(MetaMethod.java:323)
	at org.codehaus.groovy.runtime.metaclass.ClosureMetaClass.invokeMethod(ClosureMetaClass.java:274)
	at groovy.lang.MetaClassImpl.invokeMethod(MetaClassImpl.java:1030)
	at groovy.lang.Closure.call(Closure.java:427)
	at org.spockframework.runtime.GroovyRuntimeUtil.invokeClosure(GroovyRuntimeUtil.java:200)
	at org.spockframework.mock.response.CodeResponseGenerator.invokeClosure(CodeResponseGenerator.java:54)
	at org.spockframework.mock.response.CodeResponseGenerator.doRespond(CodeResponseGenerator.java:37)
	at org.spockframework.mock.response.SingleResponseGenerator.respond(SingleResponseGenerator.java:32)
	at org.spockframework.mock.response.ResponseGeneratorChain.respond(ResponseGeneratorChain.java:44)
	at org.spockframework.mock.runtime.MockInteraction.accept(MockInteraction.java:73)
	at org.spockframework.mock.runtime.MockInteractionDecorator.accept(MockInteractionDecorator.java:50)
	at org.spockframework.mock.runtime.InteractionScope$1.accept(InteractionScope.java:57)
	at org.spockframework.mock.runtime.MockController.handle(MockController.java:40)
	at org.spockframework.mock.runtime.JavaMockInterceptor.intercept(JavaMockInterceptor.java:86)
	at org.spockframework.mock.runtime.ByteBuddyInterceptorAdapter.interceptNonAbstract(ByteBuddyInterceptorAdapter.java:35)
	at com.investment.service.SecSynchronizationService$SpockMock$239248313.synchronizeInstruments(Unknown Source)
	at com.investment.api.controller.InstrumentController.syncSecDataDryRun(InstrumentController.java:218)
	at com.investment.api.controller.InstrumentController$syncSecDataDryRun$2.call(Unknown Source)
	at org.codehaus.groovy.runtime.callsite.CallSiteArray.defaultCall(CallSiteArray.java:47)
	at org.codehaus.groovy.runtime.callsite.AbstractCallSite.call(AbstractCallSite.java:125)
	at org.codehaus.groovy.runtime.callsite.AbstractCallSite.call(AbstractCallSite.java:148)
	at com.investment.api.controller.InstrumentControllerSpec.$spock_feature_0_10(InstrumentControllerSpec.groovy:188)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.spockframework.util.ReflectionUtil.invokeMethod(ReflectionUtil.java:187)
	at org.spockframework.runtime.model.MethodInfo.lambda$new$0(MethodInfo.java:49)
	at org.spockframework.runtime.model.MethodInfo.invoke(MethodInfo.java:156)
	at org.spockframework.runtime.extension.MethodInvocation.proceed(MethodInvocation.java:102)
	at org.spockframework.junit4.ExceptionAdapterInterceptor.intercept(ExceptionAdapterInterceptor.java:13)
	at org.spockframework.runtime.extension.MethodInvocation.proceed(MethodInvocation.java:101)
	at org.spockframework.runtime.PlatformSpecRunner.invoke(PlatformSpecRunner.java:398)
	at org.spockframework.runtime.PlatformSpecRunner.runFeatureMethod(PlatformSpecRunner.java:324)
	at org.spockframework.runtime.IterationNode.execute(IterationNode.java:50)
	at org.spockframework.runtime.SimpleFeatureNode.execute(SimpleFeatureNode.java:58)
	at org.spockframework.runtime.SimpleFeatureNode.execute(SimpleFeatureNode.java:15)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$6(NodeTestTask.java:151)
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$8(NodeTestTask.java:141)
	at org.spockframework.runtime.SpockNode.sneakyInvoke(SpockNode.java:40)
	at org.spockframework.runtime.IterationNode.lambda$around$0(IterationNode.java:67)
	at org.spockframework.runtime.PlatformSpecRunner.lambda$createMethodInfoForDoRunIteration$5(PlatformSpecRunner.java:236)
	at org.spockframework.runtime.model.MethodInfo.invoke(MethodInfo.java:156)
	at org.spockframework.runtime.extension.MethodInvocation.proceed(MethodInvocation.java:102)
	at org.spockframework.junit4.ExceptionAdapterInterceptor.intercept(ExceptionAdapterInterceptor.java:13)
	at org.spockframework.runtime.extension.MethodInvocation.proceed(MethodInvocation.java:101)
	at org.spockframework.runtime.PlatformSpecRunner.invoke(PlatformSpecRunner.java:398)
	at org.spockframework.runtime.PlatformSpecRunner.runIteration(PlatformSpecRunner.java:218)
	at org.spockframework.runtime.IterationNode.around(IterationNode.java:67)
	at org.spockframework.runtime.SimpleFeatureNode.lambda$around$0(SimpleFeatureNode.java:52)
	at org.spockframework.runtime.SpockNode.sneakyInvoke(SpockNode.java:40)
	at org.spockframework.runtime.FeatureNode.lambda$around$0(FeatureNode.java:41)
	at org.spockframework.runtime.PlatformSpecRunner.lambda$createMethodInfoForDoRunFeature$4(PlatformSpecRunner.java:199)
	at org.spockframework.runtime.model.MethodInfo.invoke(MethodInfo.java:156)
	at org.spockframework.runtime.PlatformSpecRunner.invokeRaw(PlatformSpecRunner.java:407)
	at org.spockframework.runtime.PlatformSpecRunner.invoke(PlatformSpecRunner.java:390)
	at org.spockframework.runtime.PlatformSpecRunner.runFeature(PlatformSpecRunner.java:192)
	at org.spockframework.runtime.FeatureNode.around(FeatureNode.java:41)
	at org.spockframework.runtime.SimpleFeatureNode.around(SimpleFeatureNode.java:52)
	at org.spockframework.runtime.SimpleFeatureNode.around(SimpleFeatureNode.java:15)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$9(NodeTestTask.java:139)
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.executeRecursively(NodeTestTask.java:138)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.execute(NodeTestTask.java:95)
	at java.base/java.util.ArrayList.forEach(ArrayList.java:1596)
	at org.junit.platform.engine.support.hierarchical.SameThreadHierarchicalTestExecutorService.invokeAll(SameThreadHierarchicalTestExecutorService.java:41)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$6(NodeTestTask.java:155)
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$8(NodeTestTask.java:141)
	at org.spockframework.runtime.SpockNode.sneakyInvoke(SpockNode.java:40)
	at org.spockframework.runtime.SpecNode.lambda$around$0(SpecNode.java:63)
	at org.spockframework.runtime.PlatformSpecRunner.lambda$createMethodInfoForDoRunSpec$0(PlatformSpecRunner.java:61)
	at org.spockframework.runtime.model.MethodInfo.invoke(MethodInfo.java:156)
	at org.spockframework.runtime.PlatformSpecRunner.invokeRaw(PlatformSpecRunner.java:407)
	at org.spockframework.runtime.PlatformSpecRunner.invoke(PlatformSpecRunner.java:390)
	at org.spockframework.runtime.PlatformSpecRunner.runSpec(PlatformSpecRunner.java:55)
	at org.spockframework.runtime.SpecNode.around(SpecNode.java:63)
	at org.spockframework.runtime.SpecNode.around(SpecNode.java:11)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$9(NodeTestTask.java:139)
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.executeRecursively(NodeTestTask.java:138)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.execute(NodeTestTask.java:95)
	at java.base/java.util.ArrayList.forEach(ArrayList.java:1596)
	at org.junit.platform.engine.support.hierarchical.SameThreadHierarchicalTestExecutorService.invokeAll(SameThreadHierarchicalTestExecutorService.java:41)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$6(NodeTestTask.java:155)
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$8(NodeTestTask.java:141)
	at org.junit.platform.engine.support.hierarchical.Node.around(Node.java:137)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$9(NodeTestTask.java:139)
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.executeRecursively(NodeTestTask.java:138)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.execute(NodeTestTask.java:95)
	at org.junit.platform.engine.support.hierarchical.SameThreadHierarchicalTestExecutorService.submit(SameThreadHierarchicalTestExecutorService.java:35)
	at org.junit.platform.engine.support.hierarchical.HierarchicalTestExecutor.execute(HierarchicalTestExecutor.java:57)
	at org.junit.platform.engine.support.hierarchical.HierarchicalTestEngine.execute(HierarchicalTestEngine.java:54)
	at org.junit.platform.launcher.core.EngineExecutionOrchestrator.execute(EngineExecutionOrchestrator.java:198)
	at org.junit.platform.launcher.core.EngineExecutionOrchestrator.execute(EngineExecutionOrchestrator.java:169)
	at org.junit.platform.launcher.core.EngineExecutionOrchestrator.execute(EngineExecutionOrchestrator.java:93)
	at org.junit.platform.launcher.core.EngineExecutionOrchestrator.lambda$execute$0(EngineExecutionOrchestrator.java:58)
	at org.junit.platform.launcher.core.EngineExecutionOrchestrator.withInterceptedStreams(EngineExecutionOrchestrator.java:141)
	at org.junit.platform.launcher.core.EngineExecutionOrchestrator.execute(EngineExecutionOrchestrator.java:57)
	at org.junit.platform.launcher.core.DefaultLauncher.execute(DefaultLauncher.java:103)
	at org.junit.platform.launcher.core.DefaultLauncher.execute(DefaultLauncher.java:85)
	at org.junit.platform.launcher.core.DelegatingLauncher.execute(DelegatingLauncher.java:47)
	at org.gradle.api.internal.tasks.testing.junitplatform.JUnitPlatformTestClassProcessor$CollectAllTestClassesExecutor.processAllTestClasses(JUnitPlatformTestClassProcessor.java:124)
	at org.gradle.api.internal.tasks.testing.junitplatform.JUnitPlatformTestClassProcessor$CollectAllTestClassesExecutor.access$000(JUnitPlatformTestClassProcessor.java:99)
	at org.gradle.api.internal.tasks.testing.junitplatform.JUnitPlatformTestClassProcessor.stop(JUnitPlatformTestClassProcessor.java:94)
	at org.gradle.api.internal.tasks.testing.SuiteTestClassProcessor.stop(SuiteTestClassProcessor.java:63)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.gradle.internal.dispatch.ReflectionDispatch.dispatch(ReflectionDispatch.java:36)
	at org.gradle.internal.dispatch.ReflectionDispatch.dispatch(ReflectionDispatch.java:24)
	at org.gradle.internal.dispatch.ContextClassLoaderDispatch.dispatch(ContextClassLoaderDispatch.java:33)
	at org.gradle.internal.dispatch.ProxyDispatchAdapter$DispatchingInvocationHandler.invoke(ProxyDispatchAdapter.java:92)
	at jdk.proxy1/jdk.proxy1.$Proxy4.stop(Unknown Source)
	at org.gradle.api.internal.tasks.testing.worker.TestWorker$3.run(TestWorker.java:200)
	at org.gradle.api.internal.tasks.testing.worker.TestWorker.executeAndMaintainThreadName(TestWorker.java:132)
	at org.gradle.api.internal.tasks.testing.worker.TestWorker.execute(TestWorker.java:103)
	at org.gradle.api.internal.tasks.testing.worker.TestWorker.execute(TestWorker.java:63)
	at org.gradle.process.internal.worker.child.ActionExecutionWorker.execute(ActionExecutionWorker.java:56)
	at org.gradle.process.internal.worker.child.SystemApplicationClassLoaderWorker.call(SystemApplicationClassLoaderWorker.java:121)
	at org.gradle.process.internal.worker.child.SystemApplicationClassLoaderWorker.call(SystemApplicationClassLoaderWorker.java:71)
	at worker.org.gradle.process.internal.worker.GradleWorkerMain.run(GradleWorkerMain.java:69)
	at worker.org.gradle.process.internal.worker.GradleWorkerMain.main(GradleWorkerMain.java:74)
2025-05-30 23:27:46 [Test worker] INFO  c.i.a.c.InstrumentController - Starting SEC synchronization - dryRun: false, forceRefresh: false, maxInstruments: 100
2025-05-30 23:27:46 [Test worker] WARN  c.i.a.c.InstrumentController - PERFORMING ACTUAL SYNCHRONIZATION - This will add new instruments to the database
2025-05-30 23:27:46 [Test worker] INFO  c.i.a.c.InstrumentController - SEC synchronization completed: SYNC COMPLETED: Added 2 new instruments out of 50 missing symbols. Database now has 102 instruments from SEC data.
2025-05-30 23:27:46 [Test worker] INFO  c.i.a.c.InstrumentController - Starting SEC synchronization - dryRun: true, forceRefresh: true, maxInstruments: 200
2025-05-30 23:27:46 [Test worker] INFO  c.i.a.c.InstrumentController - SEC synchronization (dry-run) completed: DRY RUN: Found 30 missing symbols out of 5000 SEC symbols. Would add 30 new instruments to database.
2025-05-30 23:27:46 [Test worker] INFO  c.i.a.c.InstrumentController - Starting SEC synchronization - dryRun: true, forceRefresh: false, maxInstruments: 100
2025-05-30 23:27:46 [Test worker] ERROR c.i.a.c.InstrumentController - Error during SEC synchronization
java.lang.RuntimeException: Database error
	at java.base/jdk.internal.reflect.DirectConstructorHandleAccessor.newInstance(DirectConstructorHandleAccessor.java:62)
	at java.base/java.lang.reflect.Constructor.newInstanceWithCaller(Constructor.java:502)
	at java.base/java.lang.reflect.Constructor.newInstance(Constructor.java:486)
	at org.codehaus.groovy.reflection.CachedConstructor.invoke(CachedConstructor.java:72)
	at org.codehaus.groovy.runtime.callsite.ConstructorSite$ConstructorSiteNoUnwrapNoCoerce.callConstructor(ConstructorSite.java:105)
	at org.codehaus.groovy.runtime.callsite.CallSiteArray.defaultCallConstructor(CallSiteArray.java:59)
	at org.codehaus.groovy.runtime.callsite.AbstractCallSite.callConstructor(AbstractCallSite.java:263)
	at org.codehaus.groovy.runtime.callsite.AbstractCallSite.callConstructor(AbstractCallSite.java:277)
	at com.investment.api.controller.InstrumentControllerSpec$__spock_feature_0_14_closure4.doCall(InstrumentControllerSpec.groovy:245)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.codehaus.groovy.reflection.CachedMethod.invoke(CachedMethod.java:107)
	at groovy.lang.MetaMethod.doMethodInvoke(MetaMethod.java:323)
	at org.codehaus.groovy.runtime.metaclass.ClosureMetaClass.invokeMethod(ClosureMetaClass.java:274)
	at groovy.lang.MetaClassImpl.invokeMethod(MetaClassImpl.java:1030)
	at groovy.lang.Closure.call(Closure.java:427)
	at org.spockframework.runtime.GroovyRuntimeUtil.invokeClosure(GroovyRuntimeUtil.java:200)
	at org.spockframework.mock.response.CodeResponseGenerator.invokeClosure(CodeResponseGenerator.java:54)
	at org.spockframework.mock.response.CodeResponseGenerator.doRespond(CodeResponseGenerator.java:37)
	at org.spockframework.mock.response.SingleResponseGenerator.respond(SingleResponseGenerator.java:32)
	at org.spockframework.mock.response.ResponseGeneratorChain.respond(ResponseGeneratorChain.java:44)
	at org.spockframework.mock.runtime.MockInteraction.accept(MockInteraction.java:73)
	at org.spockframework.mock.runtime.MockInteractionDecorator.accept(MockInteractionDecorator.java:50)
	at org.spockframework.mock.runtime.InteractionScope$1.accept(InteractionScope.java:57)
	at org.spockframework.mock.runtime.MockController.handle(MockController.java:40)
	at org.spockframework.mock.runtime.JavaMockInterceptor.intercept(JavaMockInterceptor.java:86)
	at org.spockframework.mock.runtime.ByteBuddyInterceptorAdapter.interceptNonAbstract(ByteBuddyInterceptorAdapter.java:35)
	at com.investment.service.SecSynchronizationService$SpockMock$239248313.synchronizeInstruments(Unknown Source)
	at com.investment.api.controller.InstrumentController.syncSecData(InstrumentController.java:276)
	at com.investment.api.controller.InstrumentController$syncSecData$3.call(Unknown Source)
	at org.codehaus.groovy.runtime.callsite.CallSiteArray.defaultCall(CallSiteArray.java:47)
	at org.codehaus.groovy.runtime.callsite.AbstractCallSite.call(AbstractCallSite.java:125)
	at org.codehaus.groovy.runtime.callsite.AbstractCallSite.call(AbstractCallSite.java:139)
	at com.investment.api.controller.InstrumentControllerSpec.$spock_feature_0_14(InstrumentControllerSpec.groovy:248)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.spockframework.util.ReflectionUtil.invokeMethod(ReflectionUtil.java:187)
	at org.spockframework.runtime.model.MethodInfo.lambda$new$0(MethodInfo.java:49)
	at org.spockframework.runtime.model.MethodInfo.invoke(MethodInfo.java:156)
	at org.spockframework.runtime.extension.MethodInvocation.proceed(MethodInvocation.java:102)
	at org.spockframework.junit4.ExceptionAdapterInterceptor.intercept(ExceptionAdapterInterceptor.java:13)
	at org.spockframework.runtime.extension.MethodInvocation.proceed(MethodInvocation.java:101)
	at org.spockframework.runtime.PlatformSpecRunner.invoke(PlatformSpecRunner.java:398)
	at org.spockframework.runtime.PlatformSpecRunner.runFeatureMethod(PlatformSpecRunner.java:324)
	at org.spockframework.runtime.IterationNode.execute(IterationNode.java:50)
	at org.spockframework.runtime.SimpleFeatureNode.execute(SimpleFeatureNode.java:58)
	at org.spockframework.runtime.SimpleFeatureNode.execute(SimpleFeatureNode.java:15)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$6(NodeTestTask.java:151)
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$8(NodeTestTask.java:141)
	at org.spockframework.runtime.SpockNode.sneakyInvoke(SpockNode.java:40)
	at org.spockframework.runtime.IterationNode.lambda$around$0(IterationNode.java:67)
	at org.spockframework.runtime.PlatformSpecRunner.lambda$createMethodInfoForDoRunIteration$5(PlatformSpecRunner.java:236)
	at org.spockframework.runtime.model.MethodInfo.invoke(MethodInfo.java:156)
	at org.spockframework.runtime.extension.MethodInvocation.proceed(MethodInvocation.java:102)
	at org.spockframework.junit4.ExceptionAdapterInterceptor.intercept(ExceptionAdapterInterceptor.java:13)
	at org.spockframework.runtime.extension.MethodInvocation.proceed(MethodInvocation.java:101)
	at org.spockframework.runtime.PlatformSpecRunner.invoke(PlatformSpecRunner.java:398)
	at org.spockframework.runtime.PlatformSpecRunner.runIteration(PlatformSpecRunner.java:218)
	at org.spockframework.runtime.IterationNode.around(IterationNode.java:67)
	at org.spockframework.runtime.SimpleFeatureNode.lambda$around$0(SimpleFeatureNode.java:52)
	at org.spockframework.runtime.SpockNode.sneakyInvoke(SpockNode.java:40)
	at org.spockframework.runtime.FeatureNode.lambda$around$0(FeatureNode.java:41)
	at org.spockframework.runtime.PlatformSpecRunner.lambda$createMethodInfoForDoRunFeature$4(PlatformSpecRunner.java:199)
	at org.spockframework.runtime.model.MethodInfo.invoke(MethodInfo.java:156)
	at org.spockframework.runtime.PlatformSpecRunner.invokeRaw(PlatformSpecRunner.java:407)
	at org.spockframework.runtime.PlatformSpecRunner.invoke(PlatformSpecRunner.java:390)
	at org.spockframework.runtime.PlatformSpecRunner.runFeature(PlatformSpecRunner.java:192)
	at org.spockframework.runtime.FeatureNode.around(FeatureNode.java:41)
	at org.spockframework.runtime.SimpleFeatureNode.around(SimpleFeatureNode.java:52)
	at org.spockframework.runtime.SimpleFeatureNode.around(SimpleFeatureNode.java:15)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$9(NodeTestTask.java:139)
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.executeRecursively(NodeTestTask.java:138)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.execute(NodeTestTask.java:95)
	at java.base/java.util.ArrayList.forEach(ArrayList.java:1596)
	at org.junit.platform.engine.support.hierarchical.SameThreadHierarchicalTestExecutorService.invokeAll(SameThreadHierarchicalTestExecutorService.java:41)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$6(NodeTestTask.java:155)
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$8(NodeTestTask.java:141)
	at org.spockframework.runtime.SpockNode.sneakyInvoke(SpockNode.java:40)
	at org.spockframework.runtime.SpecNode.lambda$around$0(SpecNode.java:63)
	at org.spockframework.runtime.PlatformSpecRunner.lambda$createMethodInfoForDoRunSpec$0(PlatformSpecRunner.java:61)
	at org.spockframework.runtime.model.MethodInfo.invoke(MethodInfo.java:156)
	at org.spockframework.runtime.PlatformSpecRunner.invokeRaw(PlatformSpecRunner.java:407)
	at org.spockframework.runtime.PlatformSpecRunner.invoke(PlatformSpecRunner.java:390)
	at org.spockframework.runtime.PlatformSpecRunner.runSpec(PlatformSpecRunner.java:55)
	at org.spockframework.runtime.SpecNode.around(SpecNode.java:63)
	at org.spockframework.runtime.SpecNode.around(SpecNode.java:11)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$9(NodeTestTask.java:139)
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.executeRecursively(NodeTestTask.java:138)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.execute(NodeTestTask.java:95)
	at java.base/java.util.ArrayList.forEach(ArrayList.java:1596)
	at org.junit.platform.engine.support.hierarchical.SameThreadHierarchicalTestExecutorService.invokeAll(SameThreadHierarchicalTestExecutorService.java:41)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$6(NodeTestTask.java:155)
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$8(NodeTestTask.java:141)
	at org.junit.platform.engine.support.hierarchical.Node.around(Node.java:137)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$9(NodeTestTask.java:139)
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.executeRecursively(NodeTestTask.java:138)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.execute(NodeTestTask.java:95)
	at org.junit.platform.engine.support.hierarchical.SameThreadHierarchicalTestExecutorService.submit(SameThreadHierarchicalTestExecutorService.java:35)
	at org.junit.platform.engine.support.hierarchical.HierarchicalTestExecutor.execute(HierarchicalTestExecutor.java:57)
	at org.junit.platform.engine.support.hierarchical.HierarchicalTestEngine.execute(HierarchicalTestEngine.java:54)
	at org.junit.platform.launcher.core.EngineExecutionOrchestrator.execute(EngineExecutionOrchestrator.java:198)
	at org.junit.platform.launcher.core.EngineExecutionOrchestrator.execute(EngineExecutionOrchestrator.java:169)
	at org.junit.platform.launcher.core.EngineExecutionOrchestrator.execute(EngineExecutionOrchestrator.java:93)
	at org.junit.platform.launcher.core.EngineExecutionOrchestrator.lambda$execute$0(EngineExecutionOrchestrator.java:58)
	at org.junit.platform.launcher.core.EngineExecutionOrchestrator.withInterceptedStreams(EngineExecutionOrchestrator.java:141)
	at org.junit.platform.launcher.core.EngineExecutionOrchestrator.execute(EngineExecutionOrchestrator.java:57)
	at org.junit.platform.launcher.core.DefaultLauncher.execute(DefaultLauncher.java:103)
	at org.junit.platform.launcher.core.DefaultLauncher.execute(DefaultLauncher.java:85)
	at org.junit.platform.launcher.core.DelegatingLauncher.execute(DelegatingLauncher.java:47)
	at org.gradle.api.internal.tasks.testing.junitplatform.JUnitPlatformTestClassProcessor$CollectAllTestClassesExecutor.processAllTestClasses(JUnitPlatformTestClassProcessor.java:124)
	at org.gradle.api.internal.tasks.testing.junitplatform.JUnitPlatformTestClassProcessor$CollectAllTestClassesExecutor.access$000(JUnitPlatformTestClassProcessor.java:99)
	at org.gradle.api.internal.tasks.testing.junitplatform.JUnitPlatformTestClassProcessor.stop(JUnitPlatformTestClassProcessor.java:94)
	at org.gradle.api.internal.tasks.testing.SuiteTestClassProcessor.stop(SuiteTestClassProcessor.java:63)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.gradle.internal.dispatch.ReflectionDispatch.dispatch(ReflectionDispatch.java:36)
	at org.gradle.internal.dispatch.ReflectionDispatch.dispatch(ReflectionDispatch.java:24)
	at org.gradle.internal.dispatch.ContextClassLoaderDispatch.dispatch(ContextClassLoaderDispatch.java:33)
	at org.gradle.internal.dispatch.ProxyDispatchAdapter$DispatchingInvocationHandler.invoke(ProxyDispatchAdapter.java:92)
	at jdk.proxy1/jdk.proxy1.$Proxy4.stop(Unknown Source)
	at org.gradle.api.internal.tasks.testing.worker.TestWorker$3.run(TestWorker.java:200)
	at org.gradle.api.internal.tasks.testing.worker.TestWorker.executeAndMaintainThreadName(TestWorker.java:132)
	at org.gradle.api.internal.tasks.testing.worker.TestWorker.execute(TestWorker.java:103)
	at org.gradle.api.internal.tasks.testing.worker.TestWorker.execute(TestWorker.java:63)
	at org.gradle.process.internal.worker.child.ActionExecutionWorker.execute(ActionExecutionWorker.java:56)
	at org.gradle.process.internal.worker.child.SystemApplicationClassLoaderWorker.call(SystemApplicationClassLoaderWorker.java:121)
	at org.gradle.process.internal.worker.child.SystemApplicationClassLoaderWorker.call(SystemApplicationClassLoaderWorker.java:71)
	at worker.org.gradle.process.internal.worker.GradleWorkerMain.run(GradleWorkerMain.java:69)
	at worker.org.gradle.process.internal.worker.GradleWorkerMain.main(GradleWorkerMain.java:74)
2025-05-30 23:27:46 [Test worker] INFO  c.i.a.c.InstrumentController - Starting SEC synchronization - dryRun: true, forceRefresh: false, maxInstruments: 1000
2025-05-30 23:27:46 [Test worker] INFO  c.i.a.c.InstrumentController - SEC synchronization (dry-run) completed: DRY RUN: Found 50 missing symbols out of 5000 SEC symbols. Would add 50 new instruments to database.
2025-05-30 23:27:46 [Test worker] INFO  c.i.a.c.InstrumentController - Starting CSV upload processing - file: instruments.csv, size: 289 bytes, dryRun: true, maxInstruments: 1000, skipDuplicates: true, validateData: true
2025-05-30 23:27:46 [Test worker] INFO  c.i.a.c.InstrumentController - CSV validation completed: DRY RUN: Processed 2 valid rows out of 2 total rows. Would add/update 2 instruments. Found 0 validation errors.
2025-05-30 23:27:46 [Test worker] INFO  c.i.a.c.InstrumentController - Starting CSV upload processing - file: instruments.csv, size: 191 bytes, dryRun: false, maxInstruments: 1000, skipDuplicates: true, validateData: true
2025-05-30 23:27:46 [Test worker] WARN  c.i.a.c.InstrumentController - PERFORMING ACTUAL CSV IMPORT - This will add/update instruments in the database
2025-05-30 23:27:46 [Test worker] INFO  c.i.a.c.InstrumentController - CSV import completed: CSV UPLOAD COMPLETED: Processed 1 instruments from 1 valid rows. Added 1 new instruments, updated 0 existing instruments, skipped 0 duplicates.
2025-05-30 23:27:46 [Test worker] INFO  c.i.a.c.InstrumentController - Starting CSV upload processing - file: invalid.csv, size: 15 bytes, dryRun: true, maxInstruments: 1000, skipDuplicates: true, validateData: true
2025-05-30 23:27:46 [Test worker] ERROR c.i.a.c.InstrumentController - Error during CSV processing
java.lang.RuntimeException: Processing error
	at java.base/jdk.internal.reflect.DirectConstructorHandleAccessor.newInstance(DirectConstructorHandleAccessor.java:62)
	at java.base/java.lang.reflect.Constructor.newInstanceWithCaller(Constructor.java:502)
	at java.base/java.lang.reflect.Constructor.newInstance(Constructor.java:486)
	at org.codehaus.groovy.reflection.CachedConstructor.invoke(CachedConstructor.java:72)
	at org.codehaus.groovy.runtime.callsite.ConstructorSite$ConstructorSiteNoUnwrapNoCoerce.callConstructor(ConstructorSite.java:105)
	at org.codehaus.groovy.runtime.callsite.CallSiteArray.defaultCallConstructor(CallSiteArray.java:59)
	at org.codehaus.groovy.runtime.callsite.AbstractCallSite.callConstructor(AbstractCallSite.java:263)
	at org.codehaus.groovy.runtime.callsite.AbstractCallSite.callConstructor(AbstractCallSite.java:277)
	at com.investment.api.controller.InstrumentControllerSpec$__spock_feature_0_20_closure5.doCall(InstrumentControllerSpec.groovy:337)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.codehaus.groovy.reflection.CachedMethod.invoke(CachedMethod.java:107)
	at groovy.lang.MetaMethod.doMethodInvoke(MetaMethod.java:323)
	at org.codehaus.groovy.runtime.metaclass.ClosureMetaClass.invokeMethod(ClosureMetaClass.java:274)
	at groovy.lang.MetaClassImpl.invokeMethod(MetaClassImpl.java:1030)
	at groovy.lang.Closure.call(Closure.java:427)
	at org.spockframework.runtime.GroovyRuntimeUtil.invokeClosure(GroovyRuntimeUtil.java:200)
	at org.spockframework.mock.response.CodeResponseGenerator.invokeClosure(CodeResponseGenerator.java:54)
	at org.spockframework.mock.response.CodeResponseGenerator.doRespond(CodeResponseGenerator.java:37)
	at org.spockframework.mock.response.SingleResponseGenerator.respond(SingleResponseGenerator.java:32)
	at org.spockframework.mock.response.ResponseGeneratorChain.respond(ResponseGeneratorChain.java:44)
	at org.spockframework.mock.runtime.MockInteraction.accept(MockInteraction.java:73)
	at org.spockframework.mock.runtime.MockInteractionDecorator.accept(MockInteractionDecorator.java:50)
	at org.spockframework.mock.runtime.InteractionScope$1.accept(InteractionScope.java:57)
	at org.spockframework.mock.runtime.MockController.handle(MockController.java:40)
	at org.spockframework.mock.runtime.JavaMockInterceptor.intercept(JavaMockInterceptor.java:86)
	at org.spockframework.mock.runtime.ByteBuddyInterceptorAdapter.interceptNonAbstract(ByteBuddyInterceptorAdapter.java:35)
	at com.investment.service.CsvInstrumentService$SpockMock$51640393.processCsvFile(Unknown Source)
	at com.investment.api.controller.InstrumentController.uploadCsv(InstrumentController.java:355)
	at com.investment.api.controller.InstrumentController$uploadCsv$4.call(Unknown Source)
	at org.codehaus.groovy.runtime.callsite.CallSiteArray.defaultCall(CallSiteArray.java:47)
	at org.codehaus.groovy.runtime.callsite.AbstractCallSite.call(AbstractCallSite.java:125)
	at com.investment.api.controller.InstrumentControllerSpec.$spock_feature_0_20(InstrumentControllerSpec.groovy:340)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.spockframework.util.ReflectionUtil.invokeMethod(ReflectionUtil.java:187)
	at org.spockframework.runtime.model.MethodInfo.lambda$new$0(MethodInfo.java:49)
	at org.spockframework.runtime.model.MethodInfo.invoke(MethodInfo.java:156)
	at org.spockframework.runtime.extension.MethodInvocation.proceed(MethodInvocation.java:102)
	at org.spockframework.junit4.ExceptionAdapterInterceptor.intercept(ExceptionAdapterInterceptor.java:13)
	at org.spockframework.runtime.extension.MethodInvocation.proceed(MethodInvocation.java:101)
	at org.spockframework.runtime.PlatformSpecRunner.invoke(PlatformSpecRunner.java:398)
	at org.spockframework.runtime.PlatformSpecRunner.runFeatureMethod(PlatformSpecRunner.java:324)
	at org.spockframework.runtime.IterationNode.execute(IterationNode.java:50)
	at org.spockframework.runtime.SimpleFeatureNode.execute(SimpleFeatureNode.java:58)
	at org.spockframework.runtime.SimpleFeatureNode.execute(SimpleFeatureNode.java:15)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$6(NodeTestTask.java:151)
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$8(NodeTestTask.java:141)
	at org.spockframework.runtime.SpockNode.sneakyInvoke(SpockNode.java:40)
	at org.spockframework.runtime.IterationNode.lambda$around$0(IterationNode.java:67)
	at org.spockframework.runtime.PlatformSpecRunner.lambda$createMethodInfoForDoRunIteration$5(PlatformSpecRunner.java:236)
	at org.spockframework.runtime.model.MethodInfo.invoke(MethodInfo.java:156)
	at org.spockframework.runtime.extension.MethodInvocation.proceed(MethodInvocation.java:102)
	at org.spockframework.junit4.ExceptionAdapterInterceptor.intercept(ExceptionAdapterInterceptor.java:13)
	at org.spockframework.runtime.extension.MethodInvocation.proceed(MethodInvocation.java:101)
	at org.spockframework.runtime.PlatformSpecRunner.invoke(PlatformSpecRunner.java:398)
	at org.spockframework.runtime.PlatformSpecRunner.runIteration(PlatformSpecRunner.java:218)
	at org.spockframework.runtime.IterationNode.around(IterationNode.java:67)
	at org.spockframework.runtime.SimpleFeatureNode.lambda$around$0(SimpleFeatureNode.java:52)
	at org.spockframework.runtime.SpockNode.sneakyInvoke(SpockNode.java:40)
	at org.spockframework.runtime.FeatureNode.lambda$around$0(FeatureNode.java:41)
	at org.spockframework.runtime.PlatformSpecRunner.lambda$createMethodInfoForDoRunFeature$4(PlatformSpecRunner.java:199)
	at org.spockframework.runtime.model.MethodInfo.invoke(MethodInfo.java:156)
	at org.spockframework.runtime.PlatformSpecRunner.invokeRaw(PlatformSpecRunner.java:407)
	at org.spockframework.runtime.PlatformSpecRunner.invoke(PlatformSpecRunner.java:390)
	at org.spockframework.runtime.PlatformSpecRunner.runFeature(PlatformSpecRunner.java:192)
	at org.spockframework.runtime.FeatureNode.around(FeatureNode.java:41)
	at org.spockframework.runtime.SimpleFeatureNode.around(SimpleFeatureNode.java:52)
	at org.spockframework.runtime.SimpleFeatureNode.around(SimpleFeatureNode.java:15)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$9(NodeTestTask.java:139)
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.executeRecursively(NodeTestTask.java:138)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.execute(NodeTestTask.java:95)
	at java.base/java.util.ArrayList.forEach(ArrayList.java:1596)
	at org.junit.platform.engine.support.hierarchical.SameThreadHierarchicalTestExecutorService.invokeAll(SameThreadHierarchicalTestExecutorService.java:41)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$6(NodeTestTask.java:155)
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$8(NodeTestTask.java:141)
	at org.spockframework.runtime.SpockNode.sneakyInvoke(SpockNode.java:40)
	at org.spockframework.runtime.SpecNode.lambda$around$0(SpecNode.java:63)
	at org.spockframework.runtime.PlatformSpecRunner.lambda$createMethodInfoForDoRunSpec$0(PlatformSpecRunner.java:61)
	at org.spockframework.runtime.model.MethodInfo.invoke(MethodInfo.java:156)
	at org.spockframework.runtime.PlatformSpecRunner.invokeRaw(PlatformSpecRunner.java:407)
	at org.spockframework.runtime.PlatformSpecRunner.invoke(PlatformSpecRunner.java:390)
	at org.spockframework.runtime.PlatformSpecRunner.runSpec(PlatformSpecRunner.java:55)
	at org.spockframework.runtime.SpecNode.around(SpecNode.java:63)
	at org.spockframework.runtime.SpecNode.around(SpecNode.java:11)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$9(NodeTestTask.java:139)
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.executeRecursively(NodeTestTask.java:138)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.execute(NodeTestTask.java:95)
	at java.base/java.util.ArrayList.forEach(ArrayList.java:1596)
	at org.junit.platform.engine.support.hierarchical.SameThreadHierarchicalTestExecutorService.invokeAll(SameThreadHierarchicalTestExecutorService.java:41)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$6(NodeTestTask.java:155)
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$8(NodeTestTask.java:141)
	at org.junit.platform.engine.support.hierarchical.Node.around(Node.java:137)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$9(NodeTestTask.java:139)
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.executeRecursively(NodeTestTask.java:138)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.execute(NodeTestTask.java:95)
	at org.junit.platform.engine.support.hierarchical.SameThreadHierarchicalTestExecutorService.submit(SameThreadHierarchicalTestExecutorService.java:35)
	at org.junit.platform.engine.support.hierarchical.HierarchicalTestExecutor.execute(HierarchicalTestExecutor.java:57)
	at org.junit.platform.engine.support.hierarchical.HierarchicalTestEngine.execute(HierarchicalTestEngine.java:54)
	at org.junit.platform.launcher.core.EngineExecutionOrchestrator.execute(EngineExecutionOrchestrator.java:198)
	at org.junit.platform.launcher.core.EngineExecutionOrchestrator.execute(EngineExecutionOrchestrator.java:169)
	at org.junit.platform.launcher.core.EngineExecutionOrchestrator.execute(EngineExecutionOrchestrator.java:93)
	at org.junit.platform.launcher.core.EngineExecutionOrchestrator.lambda$execute$0(EngineExecutionOrchestrator.java:58)
	at org.junit.platform.launcher.core.EngineExecutionOrchestrator.withInterceptedStreams(EngineExecutionOrchestrator.java:141)
	at org.junit.platform.launcher.core.EngineExecutionOrchestrator.execute(EngineExecutionOrchestrator.java:57)
	at org.junit.platform.launcher.core.DefaultLauncher.execute(DefaultLauncher.java:103)
	at org.junit.platform.launcher.core.DefaultLauncher.execute(DefaultLauncher.java:85)
	at org.junit.platform.launcher.core.DelegatingLauncher.execute(DelegatingLauncher.java:47)
	at org.gradle.api.internal.tasks.testing.junitplatform.JUnitPlatformTestClassProcessor$CollectAllTestClassesExecutor.processAllTestClasses(JUnitPlatformTestClassProcessor.java:124)
	at org.gradle.api.internal.tasks.testing.junitplatform.JUnitPlatformTestClassProcessor$CollectAllTestClassesExecutor.access$000(JUnitPlatformTestClassProcessor.java:99)
	at org.gradle.api.internal.tasks.testing.junitplatform.JUnitPlatformTestClassProcessor.stop(JUnitPlatformTestClassProcessor.java:94)
	at org.gradle.api.internal.tasks.testing.SuiteTestClassProcessor.stop(SuiteTestClassProcessor.java:63)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.gradle.internal.dispatch.ReflectionDispatch.dispatch(ReflectionDispatch.java:36)
	at org.gradle.internal.dispatch.ReflectionDispatch.dispatch(ReflectionDispatch.java:24)
	at org.gradle.internal.dispatch.ContextClassLoaderDispatch.dispatch(ContextClassLoaderDispatch.java:33)
	at org.gradle.internal.dispatch.ProxyDispatchAdapter$DispatchingInvocationHandler.invoke(ProxyDispatchAdapter.java:92)
	at jdk.proxy1/jdk.proxy1.$Proxy4.stop(Unknown Source)
	at org.gradle.api.internal.tasks.testing.worker.TestWorker$3.run(TestWorker.java:200)
	at org.gradle.api.internal.tasks.testing.worker.TestWorker.executeAndMaintainThreadName(TestWorker.java:132)
	at org.gradle.api.internal.tasks.testing.worker.TestWorker.execute(TestWorker.java:103)
	at org.gradle.api.internal.tasks.testing.worker.TestWorker.execute(TestWorker.java:63)
	at org.gradle.process.internal.worker.child.ActionExecutionWorker.execute(ActionExecutionWorker.java:56)
	at org.gradle.process.internal.worker.child.SystemApplicationClassLoaderWorker.call(SystemApplicationClassLoaderWorker.java:121)
	at org.gradle.process.internal.worker.child.SystemApplicationClassLoaderWorker.call(SystemApplicationClassLoaderWorker.java:71)
	at worker.org.gradle.process.internal.worker.GradleWorkerMain.run(GradleWorkerMain.java:69)
	at worker.org.gradle.process.internal.worker.GradleWorkerMain.main(GradleWorkerMain.java:74)
2025-05-30 23:27:46 [Test worker] INFO  c.i.a.c.InstrumentController - Starting CSV upload processing - file: instruments.csv, size: 22 bytes, dryRun: false, maxInstruments: 500, skipDuplicates: false, validateData: false
2025-05-30 23:27:46 [Test worker] WARN  c.i.a.c.InstrumentController - PERFORMING ACTUAL CSV IMPORT - This will add/update instruments in the database
2025-05-30 23:27:46 [Test worker] INFO  c.i.a.c.InstrumentController - CSV import completed: CSV UPLOAD COMPLETED: Processed 1 instruments from 1 valid rows. Added 0 new instruments, updated 1 existing instruments, skipped 0 duplicates.
2025-05-30 23:27:46 [Test worker] INFO  c.i.api.controller.OHLCVController - Retrieving OHLCV data for symbol: AAPL, from 2025-05-20 to 2025-05-30
2025-05-30 23:27:46 [Test worker] INFO  c.i.api.controller.OHLCVController - Retrieving OHLCV data for symbol: UNKNOWN, from 2025-05-20 to 2025-05-30
2025-05-30 23:27:46 [Test worker] INFO  c.i.api.controller.OHLCVController - Updating OHLCV data for 3 symbols
2025-05-30 23:27:46 [Test worker] INFO  c.i.api.controller.OHLCVController - Updating OHLCV data for symbol: AAPL
2025-05-30 23:27:46 [Test worker] INFO  c.i.database.DatabaseManager - Connected to the database
2025-05-30 23:27:47 [Test worker] INFO  c.i.database.DatabaseManager - Database tables created or already exist
2025-05-30 23:27:47 [Test worker] INFO  c.i.database.DatabaseManager - Current schema version: 0
2025-05-30 23:27:47 [Test worker] INFO  c.i.database.DatabaseManager - Running migrations from version 0 to 2
2025-05-30 23:27:47 [Test worker] INFO  c.i.database.DatabaseManager - Migrating to schema version 1
2025-05-30 23:27:47 [Test worker] INFO  c.i.database.DatabaseManager - Migrating to schema version 2 - updating instruments table
2025-05-30 23:27:47 [Test worker] INFO  c.i.database.DatabaseManager - Schema already up to date
2025-05-30 23:27:47 [Test worker] INFO  c.i.database.DatabaseManager - Migrations completed successfully
2025-05-30 23:27:47 [Test worker] INFO  c.i.database.DatabaseManager - Database connection closed
2025-05-30 23:30:32 [background-preinit] INFO  o.h.validator.internal.util.Version - HV000001: Hibernate Validator 8.0.1.Final
2025-05-30 23:30:32 [main] INFO  c.investment.InvestmentApplication - Starting InvestmentApplication using Java 21.0.5 with PID 41804 (C:\workspace-intellij\InvestmentTookKitV2\build\classes\java\main started by user in C:\workspace-intellij\InvestmentTookKitV2)
2025-05-30 23:30:32 [main] DEBUG c.investment.InvestmentApplication - Running with Spring Boot v3.2.3, Spring v6.1.4
2025-05-30 23:30:32 [main] INFO  c.investment.InvestmentApplication - No active profile set, falling back to 1 default profile: "default"
2025-05-30 23:30:32 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat initialized with port 8080 (http)
2025-05-30 23:30:32 [main] INFO  o.a.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-8080"]
2025-05-30 23:30:32 [main] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2025-05-30 23:30:32 [main] INFO  o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/10.1.19]
2025-05-30 23:30:32 [main] INFO  o.a.c.c.C.[.[.[/investment-toolkit] - Initializing Spring embedded WebApplicationContext
2025-05-30 23:30:32 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 460 ms
2025-05-30 23:30:33 [main] ERROR c.i.database.DatabaseManager - Error initializing database
java.sql.SQLException: IO Error: File is already open in 
C:\Program Files\DBeaver\dbeaver.exe (PID 22248)
	at org.duckdb.DuckDBNative.duckdb_jdbc_startup(Native Method)
	at org.duckdb.DuckDBConnection.newConnection(DuckDBConnection.java:52)
	at org.duckdb.DuckDBDriver.connect(DuckDBDriver.java:48)
	at java.sql/java.sql.DriverManager.getConnection(DriverManager.java:683)
	at java.sql/java.sql.DriverManager.getConnection(DriverManager.java:230)
	at com.investment.database.DatabaseManager.initDatabase(DatabaseManager.java:38)
	at com.investment.config.AppConfig.databaseManager(AppConfig.java:18)
	at com.investment.config.AppConfig$$SpringCGLIB$$0.CGLIB$databaseManager$0(<generated>)
	at com.investment.config.AppConfig$$SpringCGLIB$$FastClass$$1.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invokeSuper(MethodProxy.java:258)
	at org.springframework.context.annotation.ConfigurationClassEnhancer$BeanMethodInterceptor.intercept(ConfigurationClassEnhancer.java:331)
	at com.investment.config.AppConfig$$SpringCGLIB$$0.databaseManager(<generated>)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.springframework.beans.factory.support.SimpleInstantiationStrategy.instantiate(SimpleInstantiationStrategy.java:140)
	at org.springframework.beans.factory.support.ConstructorResolver.instantiate(ConstructorResolver.java:647)
	at org.springframework.beans.factory.support.ConstructorResolver.instantiateUsingFactoryMethod(ConstructorResolver.java:485)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.instantiateUsingFactoryMethod(AbstractAutowireCapableBeanFactory.java:1335)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBeanInstance(AbstractAutowireCapableBeanFactory.java:1165)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:562)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:522)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:325)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:323)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:199)
	at org.springframework.beans.factory.config.DependencyDescriptor.resolveCandidate(DependencyDescriptor.java:254)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.doResolveDependency(DefaultListableBeanFactory.java:1443)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveDependency(DefaultListableBeanFactory.java:1353)
	at org.springframework.beans.factory.support.ConstructorResolver.resolveAutowiredArgument(ConstructorResolver.java:907)
	at org.springframework.beans.factory.support.ConstructorResolver.createArgumentArray(ConstructorResolver.java:785)
	at org.springframework.beans.factory.support.ConstructorResolver.autowireConstructor(ConstructorResolver.java:237)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.autowireConstructor(AbstractAutowireCapableBeanFactory.java:1355)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBeanInstance(AbstractAutowireCapableBeanFactory.java:1192)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:562)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:522)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:325)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:323)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:199)
	at org.springframework.beans.factory.config.DependencyDescriptor.resolveCandidate(DependencyDescriptor.java:254)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.doResolveDependency(DefaultListableBeanFactory.java:1443)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveDependency(DefaultListableBeanFactory.java:1353)
	at org.springframework.beans.factory.support.ConstructorResolver.resolveAutowiredArgument(ConstructorResolver.java:907)
	at org.springframework.beans.factory.support.ConstructorResolver.createArgumentArray(ConstructorResolver.java:785)
	at org.springframework.beans.factory.support.ConstructorResolver.autowireConstructor(ConstructorResolver.java:237)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.autowireConstructor(AbstractAutowireCapableBeanFactory.java:1355)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBeanInstance(AbstractAutowireCapableBeanFactory.java:1192)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:562)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:522)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:325)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:323)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:199)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.preInstantiateSingletons(DefaultListableBeanFactory.java:975)
	at org.springframework.context.support.AbstractApplicationContext.finishBeanFactoryInitialization(AbstractApplicationContext.java:959)
	at org.springframework.context.support.AbstractApplicationContext.refresh(AbstractApplicationContext.java:624)
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.refresh(ServletWebServerApplicationContext.java:146)
	at org.springframework.boot.SpringApplication.refresh(SpringApplication.java:754)
	at org.springframework.boot.SpringApplication.refreshContext(SpringApplication.java:456)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:334)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1354)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1343)
	at com.investment.InvestmentApplication.main(InvestmentApplication.java:23)
2025-05-30 23:30:33 [main] WARN  o.s.b.w.s.c.AnnotationConfigServletWebServerApplicationContext - Exception encountered during context initialization - cancelling refresh attempt: org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'instrumentController' defined in file [C:\workspace-intellij\InvestmentTookKitV2\build\classes\java\main\com\investment\api\controller\InstrumentController.class]: Unsatisfied dependency expressed through constructor parameter 0: Error creating bean with name 'symbolValidationService' defined in file [C:\workspace-intellij\InvestmentTookKitV2\build\classes\java\main\com\investment\service\SymbolValidationService.class]: Unsatisfied dependency expressed through constructor parameter 0: Error creating bean with name 'databaseManager' defined in class path resource [com/investment/config/AppConfig.class]: Failed to instantiate [com.investment.database.DatabaseManager]: Factory method 'databaseManager' threw exception with message: Failed to initialize database
2025-05-30 23:30:33 [main] INFO  o.a.catalina.core.StandardService - Stopping service [Tomcat]
2025-05-30 23:30:33 [main] INFO  o.s.b.a.l.ConditionEvaluationReportLogger - 

Error starting ApplicationContext. To display the condition evaluation report re-run your application with 'debug' enabled.
2025-05-30 23:30:33 [main] ERROR o.s.boot.SpringApplication - Application run failed
org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'instrumentController' defined in file [C:\workspace-intellij\InvestmentTookKitV2\build\classes\java\main\com\investment\api\controller\InstrumentController.class]: Unsatisfied dependency expressed through constructor parameter 0: Error creating bean with name 'symbolValidationService' defined in file [C:\workspace-intellij\InvestmentTookKitV2\build\classes\java\main\com\investment\service\SymbolValidationService.class]: Unsatisfied dependency expressed through constructor parameter 0: Error creating bean with name 'databaseManager' defined in class path resource [com/investment/config/AppConfig.class]: Failed to instantiate [com.investment.database.DatabaseManager]: Factory method 'databaseManager' threw exception with message: Failed to initialize database
	at org.springframework.beans.factory.support.ConstructorResolver.createArgumentArray(ConstructorResolver.java:798)
	at org.springframework.beans.factory.support.ConstructorResolver.autowireConstructor(ConstructorResolver.java:237)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.autowireConstructor(AbstractAutowireCapableBeanFactory.java:1355)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBeanInstance(AbstractAutowireCapableBeanFactory.java:1192)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:562)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:522)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:325)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:323)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:199)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.preInstantiateSingletons(DefaultListableBeanFactory.java:975)
	at org.springframework.context.support.AbstractApplicationContext.finishBeanFactoryInitialization(AbstractApplicationContext.java:959)
	at org.springframework.context.support.AbstractApplicationContext.refresh(AbstractApplicationContext.java:624)
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.refresh(ServletWebServerApplicationContext.java:146)
	at org.springframework.boot.SpringApplication.refresh(SpringApplication.java:754)
	at org.springframework.boot.SpringApplication.refreshContext(SpringApplication.java:456)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:334)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1354)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1343)
	at com.investment.InvestmentApplication.main(InvestmentApplication.java:23)
Caused by: org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'symbolValidationService' defined in file [C:\workspace-intellij\InvestmentTookKitV2\build\classes\java\main\com\investment\service\SymbolValidationService.class]: Unsatisfied dependency expressed through constructor parameter 0: Error creating bean with name 'databaseManager' defined in class path resource [com/investment/config/AppConfig.class]: Failed to instantiate [com.investment.database.DatabaseManager]: Factory method 'databaseManager' threw exception with message: Failed to initialize database
	at org.springframework.beans.factory.support.ConstructorResolver.createArgumentArray(ConstructorResolver.java:798)
	at org.springframework.beans.factory.support.ConstructorResolver.autowireConstructor(ConstructorResolver.java:237)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.autowireConstructor(AbstractAutowireCapableBeanFactory.java:1355)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBeanInstance(AbstractAutowireCapableBeanFactory.java:1192)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:562)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:522)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:325)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:323)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:199)
	at org.springframework.beans.factory.config.DependencyDescriptor.resolveCandidate(DependencyDescriptor.java:254)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.doResolveDependency(DefaultListableBeanFactory.java:1443)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveDependency(DefaultListableBeanFactory.java:1353)
	at org.springframework.beans.factory.support.ConstructorResolver.resolveAutowiredArgument(ConstructorResolver.java:907)
	at org.springframework.beans.factory.support.ConstructorResolver.createArgumentArray(ConstructorResolver.java:785)
	... 19 common frames omitted
Caused by: org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'databaseManager' defined in class path resource [com/investment/config/AppConfig.class]: Failed to instantiate [com.investment.database.DatabaseManager]: Factory method 'databaseManager' threw exception with message: Failed to initialize database
	at org.springframework.beans.factory.support.ConstructorResolver.instantiate(ConstructorResolver.java:651)
	at org.springframework.beans.factory.support.ConstructorResolver.instantiateUsingFactoryMethod(ConstructorResolver.java:485)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.instantiateUsingFactoryMethod(AbstractAutowireCapableBeanFactory.java:1335)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBeanInstance(AbstractAutowireCapableBeanFactory.java:1165)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:562)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:522)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:325)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:323)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:199)
	at org.springframework.beans.factory.config.DependencyDescriptor.resolveCandidate(DependencyDescriptor.java:254)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.doResolveDependency(DefaultListableBeanFactory.java:1443)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveDependency(DefaultListableBeanFactory.java:1353)
	at org.springframework.beans.factory.support.ConstructorResolver.resolveAutowiredArgument(ConstructorResolver.java:907)
	at org.springframework.beans.factory.support.ConstructorResolver.createArgumentArray(ConstructorResolver.java:785)
	... 33 common frames omitted
Caused by: org.springframework.beans.BeanInstantiationException: Failed to instantiate [com.investment.database.DatabaseManager]: Factory method 'databaseManager' threw exception with message: Failed to initialize database
	at org.springframework.beans.factory.support.SimpleInstantiationStrategy.instantiate(SimpleInstantiationStrategy.java:177)
	at org.springframework.beans.factory.support.ConstructorResolver.instantiate(ConstructorResolver.java:647)
	... 47 common frames omitted
Caused by: java.lang.RuntimeException: Failed to initialize database
	at com.investment.database.DatabaseManager.initDatabase(DatabaseManager.java:48)
	at com.investment.config.AppConfig.databaseManager(AppConfig.java:18)
	at com.investment.config.AppConfig$$SpringCGLIB$$0.CGLIB$databaseManager$0(<generated>)
	at com.investment.config.AppConfig$$SpringCGLIB$$FastClass$$1.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invokeSuper(MethodProxy.java:258)
	at org.springframework.context.annotation.ConfigurationClassEnhancer$BeanMethodInterceptor.intercept(ConfigurationClassEnhancer.java:331)
	at com.investment.config.AppConfig$$SpringCGLIB$$0.databaseManager(<generated>)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.springframework.beans.factory.support.SimpleInstantiationStrategy.instantiate(SimpleInstantiationStrategy.java:140)
	... 48 common frames omitted
Caused by: java.sql.SQLException: IO Error: File is already open in 
C:\Program Files\DBeaver\dbeaver.exe (PID 22248)
	at org.duckdb.DuckDBNative.duckdb_jdbc_startup(Native Method)
	at org.duckdb.DuckDBConnection.newConnection(DuckDBConnection.java:52)
	at org.duckdb.DuckDBDriver.connect(DuckDBDriver.java:48)
	at java.sql/java.sql.DriverManager.getConnection(DriverManager.java:683)
	at java.sql/java.sql.DriverManager.getConnection(DriverManager.java:230)
	at com.investment.database.DatabaseManager.initDatabase(DatabaseManager.java:38)
	... 57 common frames omitted
